import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Typo<PERSON>,
  Box,
  TextField,
  Button,
  Paper,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { useTranslation } from 'react-i18next';

interface InsulinType {
  InsulinID: number;
  InsulinName: string;
  SupportType: string;
  Unit: string;
  Balance: number;
}

const InsulinPage: React.FC = () => {
  const { t } = useTranslation();
  const [newInsulinName, setNewInsulinName] = useState<string>('');
  const [newSupportType, setNewSupportType] = useState<string>('');
  const [newUnit, setNewUnit] = useState<string>('');
  const [newBalance, setNewBalance] = useState<number | ''>('');
  const [insulinTypes, setInsulinTypes] = useState<InsulinType[]>([]);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [insulinToDelete, setInsulinToDelete] = useState<number | null>(null);

  const API_BASE_URL = 'http://localhost:8000';

  useEffect(() => {
    fetchInsulinTypes();
  }, []);

  const fetchInsulinTypes = async () => {
    try {
      const response = await axios.get<InsulinType[]>(`${API_BASE_URL}/insulin-types/`);
      setInsulinTypes(response.data);
    } catch (error) {
      console.error('Error fetching insulin types:', error);
    }
  };

  const handleAddOrUpdateInsulin = async () => {
    if (newInsulinName.trim() === '' || !newSupportType || newUnit.trim() === '' || newBalance === '') return;

    try {
      if (editingId) {
        // Update existing insulin type
        await axios.put(`${API_BASE_URL}/insulin-types/${editingId}`, {
          InsulinName: newInsulinName,
          SupportType: newSupportType,
          Unit: newUnit,
          Balance: newBalance as number,
        });
        setSnackbarMessage(t('insulinUpdatedSuccess'));
      } else {
        // Add new insulin type
        await axios.post(`${API_BASE_URL}/insulin-types/`, {
          InsulinName: newInsulinName,
          SupportType: newSupportType,
          Unit: newUnit,
          Balance: newBalance as number,
        });
        setSnackbarMessage(t('insulinAddedSuccess'));
      }
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setNewInsulinName('');
      setNewSupportType('');
      setNewUnit('');
      setNewBalance('');
      setEditingId(null);
      fetchInsulinTypes(); // Refresh the list
    } catch (error) {
      console.error('Error adding/updating insulin type:', error);
      setSnackbarMessage(t('insulinOperationError'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const handleEdit = (type: InsulinType) => {
    setNewInsulinName(type.InsulinName);
    setNewSupportType(type.SupportType);
    setNewUnit(type.Unit);
    setNewBalance(type.Balance);
    setEditingId(type.InsulinID);
  };

  const handleDeleteClick = (id: number) => {
    setInsulinToDelete(id);
    setDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (insulinToDelete === null) return;
    try {
      await axios.delete(`${API_BASE_URL}/insulin-types/${insulinToDelete}`);
      setSnackbarMessage(t('insulinDeletedSuccess'));
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      fetchInsulinTypes(); // Refresh the list
    } catch (error) {
      console.error('Error deleting insulin type:', error);
      setSnackbarMessage(t('insulinOperationError'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setDialogOpen(false);
      setInsulinToDelete(null);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setInsulinToDelete(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>{t('insulinManagement')}</Typography>

      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>{t('addEditInsulinType')}</Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} component="div">
            <TextField
              fullWidth
              label={t('insulinName')}
              value={newInsulinName}
              onChange={(e) => setNewInsulinName(e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="support-type-label">{t('supportType')}</InputLabel>
              <Select
                labelId="support-type-label"
                id="support-type-select"
                value={newSupportType}
                label={t('supportType')}
                onChange={(e) => setNewSupportType(e.target.value as string)}
              >
                <MenuItem value="مدعّم">{t('supported')}</MenuItem>
                <MenuItem value="هيئة">{t('authority')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} component="div">
            <TextField
              fullWidth
              label={t('unit')}
              value={newUnit}
              onChange={(e) => setNewUnit(e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6} component="div">
            <TextField
              fullWidth
              label={t('balance')}
              type="number"
              value={newBalance}
              onChange={(e) => setNewBalance(Number(e.target.value))}
              inputProps={{ min: 0 }}
            />
          </Grid>
          <Grid item xs={12} component="div">
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddOrUpdateInsulin}
              fullWidth
            >
              {editingId ? t('update') : t('add')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper elevation={3} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>{t('existingInsulinTypes')}</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('id')}</TableCell>
                <TableCell>{t('insulinName')}</TableCell>
                <TableCell>{t('supportType')}</TableCell>
                <TableCell>{t('unit')}</TableCell>
                <TableCell>{t('balance')}</TableCell>
                <TableCell align="right">{t('actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {insulinTypes.map((type) => (
                <TableRow key={type.InsulinID}>
                  <TableCell>{type.InsulinID}</TableCell>
                  <TableCell>{type.InsulinName}</TableCell>
                  <TableCell>{type.SupportType}</TableCell>
                  <TableCell>{type.Unit}</TableCell>
                  <TableCell>{type.Balance}</TableCell>
                  <TableCell align="right">
                    <IconButton onClick={() => handleEdit(type)} color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteClick(type.InsulinID)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleCloseSnackbar}>
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>

      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {t('confirmDeleteTitle')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {t('confirmDeleteInsulinMessage')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>{t('cancel')}</Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            {t('delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default InsulinPage;