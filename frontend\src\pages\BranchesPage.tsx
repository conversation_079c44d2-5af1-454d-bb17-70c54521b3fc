import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  TextField,
  Button,
  Paper,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Snackbar,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { useTranslation } from 'react-i18next';
import axios from 'axios';

interface Branch {
  id: number;
  name: string;
}

const BranchesPage: React.FC = () => {
  const { t } = useTranslation();
  const [newBranchName, setNewBranchName] = useState<string>('');
  const [branches, setBranches] = useState<Branch[]>([]);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [branchToDelete, setBranchToDelete] = useState<number | null>(null);
  const [branchNameError, setBranchNameError] = useState<boolean>(false);
  const [branchNameHelperText, setBranchNameHelperText] = useState<string>('');

  const API_BASE_URL = 'http://localhost:8000'; // Replace with your backend API URL

  useEffect(() => {
    fetchBranches();
  }, []);

  const fetchBranches = async () => {
    try {
      const response = await axios.get<Branch[]>(`${API_BASE_URL}/branches/`);
      setBranches(response.data);
      console.log('Fetched branches:', response.data);
    } catch (error) {
      console.error('Error fetching branches:', error);
    }
  };

  const handleAddOrUpdateBranch = async () => {
    // Reset errors
    setBranchNameError(false);

    if (newBranchName.trim() === '') {
      setBranchNameError(true);
      return;
    }

    try {
      if (editingId) {
        // Update existing branch
        await axios.put(`${API_BASE_URL}/branches/${editingId}`, { name: newBranchName });
        setSnackbarMessage(t('branchUpdatedSuccess'));
      } else {
        // Add new branch
        await axios.post(`${API_BASE_URL}/branches/`, { name: newBranchName });
        setSnackbarMessage(t('branchAddedSuccess'));
      }
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setNewBranchName('');
      setEditingId(null);
      fetchBranches(); // Refresh the list
    } catch (error) {
      console.error('Error adding/updating branch:', error);
      setSnackbarMessage(t('branchOperationError'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      // Log the full error object for more details
      console.error('Full error details:', error);
    }
  };

  const handleEdit = (branch: Branch) => {
    setNewBranchName(branch.name);
    setEditingId(branch.id);
  };

  const handleDeleteClick = (id: number) => {
    setBranchToDelete(id);
    setDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (branchToDelete === null) return;
    try {
      await axios.delete(`${API_BASE_URL}/branches/${branchToDelete}`);
      setSnackbarMessage(t('branchDeletedSuccess'));
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      fetchBranches(); // Refresh the list
    } catch (error) {
      console.error('Error deleting branch:', error);
      setSnackbarMessage(t('branchOperationError'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setDialogOpen(false);
      setBranchToDelete(null);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setBranchToDelete(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>{t('branchesManagement')}</Typography>

      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>{t('addEditBranch')}</Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              label={t('branchName')}
              value={newBranchName}
              onChange={(e) => setNewBranchName(e.target.value)}
              error={branchNameError}
              helperText={branchNameError ? t('branchNameRequired') : ''}
            />
          </Grid>
          <Grid item xs={12} sm={4} component="div">
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddOrUpdateBranch}
              fullWidth
            >
              {editingId ? t('update') : t('add')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper elevation={3} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>{t('existingBranches')}</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('id')}</TableCell>
                <TableCell>{t('branchName')}</TableCell>
                <TableCell align="right">{t('actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {branches.map((branch) => (
                <TableRow key={branch.id}>
                  <TableCell>{branch.id}</TableCell>
                  <TableCell>{branch.name}</TableCell>
                  <TableCell align="right">
                    <IconButton onClick={() => handleEdit(branch)} color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteClick(branch.id)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleCloseSnackbar}>
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>

      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {t('confirmDeleteTitle')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {t('confirmDeleteBranchMessage')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>{t('cancel')}</Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            {t('delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BranchesPage;
