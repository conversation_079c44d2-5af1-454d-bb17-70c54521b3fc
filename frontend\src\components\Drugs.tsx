import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
    Box, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, 
    Typography, Dialog, DialogActions, DialogContent, DialogTitle, TextField, Select, 
    MenuItem, FormControl, InputLabel, CircularProgress
} from '@mui/material';

const API_URL = 'http://127.0.0.1:8000';

interface DrugCategory {
    id: number;
    name: string;
}

interface Drug {
    id: number;
    name: string;
    category_id: number;
}

const Drugs = () => {
    const [drugs, setDrugs] = useState<Drug[]>([]);
    const [categories, setCategories] = useState<DrugCategory[]>([]);
    const [open, setOpen] = useState(false);
    const [newDrugName, setNewDrugName] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<number | ''>('');
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchDrugsAndCategories();
    }, []);

    const fetchDrugsAndCategories = async () => {
        setLoading(true);
        try {
            const [drugsRes, categoriesRes] = await Promise.all([
                axios.get<Drug[]>(`${API_URL}/drugs`),
                axios.get<DrugCategory[]>(`${API_URL}/drug-categories`)
            ]);
            setDrugs(drugsRes.data);
            setCategories(categoriesRes.data);
        } catch (error) {
            console.error('Error fetching data:', error);
        }
        setLoading(false);
    };

    const handleAddDrug = async () => {
        if (!newDrugName.trim() || !selectedCategory) return;
        try {
            const response = await axios.post<Drug>(`${API_URL}/drugs`, { 
                name: newDrugName, 
                category_id: selectedCategory 
            });
            setDrugs([...drugs, response.data]);
            setNewDrugName('');
            setSelectedCategory('');
            setOpen(false);
        } catch (error) {
            console.error('Error adding drug:', error);
        }
    };

    const getCategoryName = (categoryId: number) => {
        const category = categories.find(c => c.id === categoryId);
        return category ? category.name : 'غير معروف';
    };

    if (loading) {
        return <CircularProgress />;
    }

    return (
        <Box sx={{ width: '100%' }}>
            <Typography variant="h6" gutterBottom component="div">
                إدارة الأدوية
            </Typography>
            <Button variant="contained" onClick={() => setOpen(true)} sx={{ mb: 2 }}>
                إضافة دواء جديد
            </Button>
            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>اسم الدواء</TableCell>
                            <TableCell>التصنيف</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {drugs.map((drug) => (
                            <TableRow key={drug.id}>
                                <TableCell>{drug.name}</TableCell>
                                <TableCell>{getCategoryName(drug.category_id)}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            <Dialog open={open} onClose={() => setOpen(false)} fullWidth>
                <DialogTitle>إضافة دواء جديد</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="اسم الدواء"
                        type="text"
                        fullWidth
                        variant="standard"
                        value={newDrugName}
                        onChange={(e) => setNewDrugName(e.target.value)}
                        sx={{ mb: 2 }}
                    />
                    <FormControl fullWidth variant="standard">
                        <InputLabel>التصنيف</InputLabel>
                        <Select
                            value={selectedCategory}
                            label="التصنيف"
                            onChange={(e) => setSelectedCategory(e.target.value as number)}
                        >
                            {categories.map((category) => (
                                <MenuItem key={category.id} value={category.id}>
                                    {category.name}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpen(false)}>إلغاء</Button>
                    <Button onClick={handleAddDrug}>إضافة</Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default Drugs;
