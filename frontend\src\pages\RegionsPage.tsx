import React, { useState, useEffect } from 'react';
import {
  <PERSON>po<PERSON>,
  Box,
  TextField,
  Button,
  Paper,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Snackbar,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  FormHelperText,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { useTranslation } from 'react-i18next';
import axios from 'axios';

const API_URL = 'http://127.0.0.1:8000'; // Replace with your backend API URL

interface Branch {
  id: number;
  name: string;
}

interface Region {
  id: number;
  name: string;
  branch_id: number;
}

const RegionsPage: React.FC = () => {
  const { t } = useTranslation();
  const [regions, setRegions] = useState<Region[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [newRegionName, setNewRegionName] = useState<string>('');
  const [selectedBranch, setSelectedBranch] = useState<number | ''>('');
  const [editingId, setEditingId] = useState<number | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [regionToDelete, setRegionToDelete] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [regionNameError, setRegionNameError] = useState<boolean>(false);
  const [branchSelectError, setBranchSelectError] = useState<boolean>(false);

    useEffect(() => {
    fetchRegionsAndBranches();
  }, []);

  const fetchRegionsAndBranches = async () => {
    setLoading(true);
    console.log('Fetching regions and branches...');
    try {
      const [regionsRes, branchesRes] = await Promise.all([
        axios.get<Region[]>(`${API_URL}/regions/`),
        axios.get<Branch[]>(`${API_URL}/branches/`)
      ]);
      setRegions(regionsRes.data);
      setBranches(branchesRes.data);
      console.log('Branches fetched:', branchesRes.data);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

    const handleAddOrUpdateRegion = async () => {
    // Reset errors
    setRegionNameError(false);
    setBranchSelectError(false);

    if (!newRegionName.trim()) {
      setRegionNameError(true);
      return;
    }
    if (!selectedBranch) {
      setBranchSelectError(true);
      return;
    }

    try {
      if (editingId) {
        // Update existing region
        await axios.put(`${API_URL}/regions/${editingId}`, {
          name: newRegionName,
          branch_id: selectedBranch,
        });
        setSnackbarMessage(t('regionUpdatedSuccess'));
      } else {
        // Add new region
        await axios.post(`${API_URL}/regions/`, {
          name: newRegionName,
          branch_id: selectedBranch,
        });
        setSnackbarMessage(t('regionAddedSuccess'));
      }
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setNewRegionName('');
      setSelectedBranch('');
      setEditingId(null);
      fetchRegionsAndBranches(); // Refresh the list
    } catch (error) {
      console.error('Error adding/updating region:', error);
      setSnackbarMessage(t('regionOperationError'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const handleEdit = (region: Region) => {
    setNewRegionName(region.name);
    setSelectedBranch(region.branch_id);
    setEditingId(region.id);
  };

  const handleDeleteClick = (id: number) => {
    setRegionToDelete(id);
    setDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (regionToDelete === null) return;
    try {
      await axios.delete(`${API_URL}/regions/${regionToDelete}`);
      setSnackbarMessage(t('regionDeletedSuccess'));
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      fetchRegionsAndBranches(); // Refresh the list
    } catch (error) {
      console.error('Error deleting region:', error);
      setSnackbarMessage(t('regionOperationError'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setDialogOpen(false);
      setRegionToDelete(null);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setRegionToDelete(null);
  };

  const getBranchName = (branchId: number) => {
    const branch = branches.find(b => b.id === branchId);
    return branch ? branch.name : t('unknown');
  };

    if (loading) {
    return <CircularProgress />;
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>{t('regionsManagement')}</Typography>

      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>{t('addEditRegion')}</Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} component="div">
            <TextField
              fullWidth
              label={t('regionName')}
              value={newRegionName}
              onChange={(e) => setNewRegionName(e.target.value)}
              error={regionNameError}
              helperText={regionNameError ? t('regionNameRequired') : ''}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth error={branchSelectError}>
              <InputLabel>{t('branch')}</InputLabel>
              <Select
                value={selectedBranch}
                label={t('branch')}
                onChange={(e) => setSelectedBranch(e.target.value as number)}
              >
                {branches.map((branch) => (
                  <MenuItem key={branch.id} value={branch.id}>
                    {branch.name}
                  </MenuItem>
                ))}
              </Select>
              {branchSelectError ? <FormHelperText>{t('branchSelectionRequired')}</FormHelperText> : null}
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3} component="div">
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddOrUpdateRegion}
              fullWidth
            >
              {editingId ? t('update') : t('add')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper elevation={3} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>{t('existingRegions')}</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('id')}</TableCell>
                <TableCell>{t('regionName')}</TableCell>
                <TableCell>{t('branch')}</TableCell>
                <TableCell align="right">{t('actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {regions.map((region) => (
                <TableRow key={region.id}>
                  <TableCell>{region.id}</TableCell>
                  <TableCell>{region.name}</TableCell>
                  <TableCell>{getBranchName(region.branch_id)}</TableCell>
                  <TableCell align="right">
                    <IconButton onClick={() => handleEdit(region)} color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteClick(region.id)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleCloseSnackbar}>
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>

      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {t('confirmDeleteTitle')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {t('confirmDeleteRegionMessage')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>{t('cancel')}</Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            {t('delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RegionsPage;
