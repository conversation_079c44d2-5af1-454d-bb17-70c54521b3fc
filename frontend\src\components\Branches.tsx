import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
    Box, 
    Button, 
    Table, 
    TableBody, 
    TableCell, 
    TableContainer, 
    TableHead, 
    TableRow, 
    Paper, 
    Typography, 
    Dialog, 
    DialogActions, 
    DialogContent, 
    DialogTitle, 
    TextField 
} from '@mui/material';

// The base URL of your FastAPI backend
const API_URL = 'http://127.0.0.1:8000';

interface Branch {
    id: number;
    name: string;
}

const Branches = () => {
    const [branches, setBranches] = useState<Branch[]>([]);
    const [open, setOpen] = useState(false);
    const [newBranchName, setNewBranchName] = useState('');

    useEffect(() => {
        fetchBranches();
    }, []);

    const fetchBranches = async () => {
        try {
            const response = await axios.get<Branch[]>(`${API_URL}/branches`);
            setBranches(response.data);
        } catch (error) {
            console.error('Error fetching branches:', error);
        }
    };

    const handleAddBranch = async () => {
        if (!newBranchName.trim()) return;
        try {
            const response = await axios.post<Branch>(`${API_URL}/branches`, { name: newBranchName });
            setBranches([...branches, response.data]);
            setNewBranchName('');
            setOpen(false);
        } catch (error) {
            console.error('Error adding branch:', error);
        }
    };

    return (
        <Box sx={{ width: '100%' }}>
            <Typography variant="h6" gutterBottom component="div">
                إدارة الفروع
            </Typography>
            <Button variant="contained" onClick={() => setOpen(true)} sx={{ mb: 2 }}>
                إضافة فرع جديد
            </Button>
            <TableContainer component={Paper}>
                <Table sx={{ minWidth: 650 }} aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell>المعرّف (ID)</TableCell>
                            <TableCell>اسم الفرع</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {branches.map((branch) => (
                            <TableRow key={branch.id}>
                                <TableCell>{branch.id}</TableCell>
                                <TableCell>{branch.name}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            {/* Add Branch Dialog */}
            <Dialog open={open} onClose={() => setOpen(false)}>
                <DialogTitle>إضافة فرع جديد</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        id="name"
                        label="اسم الفرع"
                        type="text"
                        fullWidth
                        variant="standard"
                        value={newBranchName}
                        onChange={(e) => setNewBranchName(e.target.value)}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpen(false)}>إلغاء</Button>
                    <Button onClick={handleAddBranch}>إضافة</Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default Branches;
