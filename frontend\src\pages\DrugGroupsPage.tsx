import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Typo<PERSON>,
  Box,
  TextField,
  Button,
  Paper,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Snackbar,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { useTranslation } from 'react-i18next';

interface DrugGroup {
  GroupID: number;
  GroupName: string;
}

const DrugGroupsPage: React.FC = () => {
  const { t } = useTranslation();
  const [newGroupName, setNewGroupName] = useState<string>('');
  const [drugGroups, setDrugGroups] = useState<DrugGroup[]>([]);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [groupToDelete, setGroupToDelete] = useState<number | null>(null);

  const API_BASE_URL = 'http://localhost:8000';

  useEffect(() => {
    fetchDrugGroups();
  }, []);

  const fetchDrugGroups = async () => {
    try {
      const response = await axios.get<DrugGroup[]>(`${API_BASE_URL}/drug-groups/`);
      setDrugGroups(response.data);
    } catch (error) {
      console.error('Error fetching drug groups:', error);
    }
  };

  const handleAddOrUpdateGroup = async () => {
    if (newGroupName.trim() === '') return;

    try {
      if (editingId) {
        // Update existing group
        await axios.put(`${API_BASE_URL}/drug-groups/${editingId}`, { GroupName: newGroupName });
        setSnackbarMessage(t('drugGroupUpdatedSuccess'));
      } else {
        // Add new group
        await axios.post(`${API_BASE_URL}/drug-groups/`, { GroupName: newGroupName });
        setSnackbarMessage(t('drugGroupAddedSuccess'));
      }
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setNewGroupName('');
      setEditingId(null);
      fetchDrugGroups(); // Refresh the list
    } catch (error) {
      console.error('Error adding/updating drug group:', error);
      setSnackbarMessage(t('drugGroupOperationError'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const handleEdit = (group: DrugGroup) => {
    setNewGroupName(group.GroupName);
    setEditingId(group.GroupID);
  };

  const handleDeleteClick = (id: number) => {
    setGroupToDelete(id);
    setDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (groupToDelete === null) return;
    try {
      await axios.delete(`${API_BASE_URL}/drug-groups/${groupToDelete}`);
      setSnackbarMessage(t('drugGroupDeletedSuccess'));
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      fetchDrugGroups(); // Refresh the list
    } catch (error) {
      console.error('Error deleting drug group:', error);
      setSnackbarMessage(t('drugGroupOperationError'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setDialogOpen(false);
      setGroupToDelete(null);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setGroupToDelete(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>{t('drugGroupsManagement')}</Typography>

      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>{t('addEditDrugGroup')}</Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              label={t('drugGroupName')}
              value={newGroupName}
              onChange={(e) => setNewGroupName(e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={4} component="div">
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddOrUpdateGroup}
              fullWidth
            >
              {editingId ? t('update') : t('add')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper elevation={3} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>{t('existingDrugGroups')}</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('id')}</TableCell>
                <TableCell>{t('drugGroupName')}</TableCell>
                <TableCell align="right">{t('actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {drugGroups.map((group) => (
                <TableRow key={group.GroupID}>
                  <TableCell>{group.GroupID}</TableCell>
                  <TableCell>{group.GroupName}</TableCell>
                  <TableCell align="right">
                    <IconButton onClick={() => handleEdit(group)} color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteClick(group.GroupID)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleCloseSnackbar}>
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>

      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {t('confirmDeleteTitle')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {t('confirmDeleteDrugGroupMessage')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>{t('cancel')}</Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            {t('delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DrugGroupsPage;