
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Box,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Snackbar,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';
import { Edit, Delete } from '@mui/icons-material';

interface Clinic {
  id: number;
  name: string;
}

interface Drug {
  DrugID: number;
  CategoryID: number;
  DrugName: string;
  Unit: string | null;
}

interface DrugCategory {
  CategoryID: number;
  CategoryName: string;
}

interface DispenseItem {
  tempId: string; // Unique ID for temporary list items
  ClinicID: number;
  DrugID: number;
  Quantity: number;
  UnitPrice: number;
  Cases: number;
  DispenseDate: string; // YYYY-MM-DD format
  // For display purposes
  ClinicName: string;
  DrugName: string;
  DrugUnit: string | null;
}

const DispenseDrugsPage = () => {
  const { t } = useTranslation();
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [drugs, setDrugs] = useState<Drug[]>([]);
  const [categories, setCategories] = useState<DrugCategory[]>([]);

  const [selectedClinicId, setSelectedClinicId] = useState<number | ''>('');
  const [selectedDrugId, setSelectedDrugId] = useState<number | ''>('');
  const [quantity, setQuantity] = useState<string>('');
  const [unitPrice, setUnitPrice] = useState<string>('');
  const [cases, setCases] = useState<string>('');
  const [dispenseMonth, setDispenseMonth] = useState<Dayjs | null>(dayjs());
  const [selectedFilterCategoryId, setSelectedFilterCategoryId] = useState<number | ''>('');

  const [dispenseList, setDispenseList] = useState<DispenseItem[]>([]);
  const [editingItem, setEditingItem] = useState<DispenseItem | null>(null);
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);

  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);

  // Validation states
  const [clinicError, setClinicError] = useState(false);
  const [drugError, setDrugError] = useState(false);
  const [quantityError, setQuantityError] = useState(false);
  const [unitPriceError, setUnitPriceError] = useState(false);
  const [casesError, setCasesError] = useState(false);
  const [monthError, setMonthError] = useState(false);

  useEffect(() => {
    fetchClinics();
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchDrugs(selectedFilterCategoryId);
  }, [selectedFilterCategoryId]);

  const fetchClinics = async () => {
    try {
      const response = await axios.get<Clinic[]>('http://localhost:8000/clinics/');
      setClinics(response.data);
    } catch (error) {
      console.error('Error fetching clinics:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchDrugs = async (categoryId: number | '') => {
    try {
      const url = categoryId ? `http://localhost:8000/drugs/?category_id=${categoryId}` : 'http://localhost:8000/drugs/';
      const response = await axios.get<Drug[]>(url);
      setDrugs(response.data);
    } catch (error) {
      console.error('Error fetching drugs:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/');
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching drug categories:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const handleAddItemToList = () => {
    let hasError = false;

    if (!selectedClinicId) { setClinicError(true); hasError = true; }
    if (!selectedDrugId) { setDrugError(true); hasError = true; }
    if (!quantity || parseFloat(quantity) <= 0) { setQuantityError(true); hasError = true; }
    if (!unitPrice || parseFloat(unitPrice) <= 0) { setUnitPriceError(true); hasError = true; }
    if (!cases || parseInt(cases) <= 0) { setCasesError(true); hasError = true; }
    if (!dispenseMonth) { setMonthError(true); hasError = true; }

    if (hasError) {
      setSnackbar({ open: true, message: t('fillAllRequiredFields'), severity: 'error' });
      return;
    }

    const clinic = clinics.find(c => c.id === selectedClinicId);
    const drug = drugs.find(d => d.DrugID === selectedDrugId);

    if (!clinic || !drug) {
      setSnackbar({ open: true, message: t('selectionError'), severity: 'error' });
      return;
    }

    const newItem: DispenseItem = {
      tempId: Date.now().toString(), // Simple unique ID for temporary list
      ClinicID: selectedClinicId as number,
      DrugID: selectedDrugId as number,
      Quantity: parseFloat(quantity),
      UnitPrice: parseFloat(unitPrice),
      Cases: parseInt(cases),
      DispenseDate: dispenseMonth?.startOf('month').format('YYYY-MM-DD') || '',
      ClinicName: clinic.name,
      DrugName: drug.DrugName,
      DrugUnit: drug.Unit,
    };

    // Check for duplicates in the current dispenseList
    const isDuplicate = dispenseList.some(item =>
      item.ClinicID === newItem.ClinicID &&
      item.DrugID === newItem.DrugID &&
      item.Quantity === newItem.Quantity &&
      item.UnitPrice === newItem.UnitPrice &&
      dayjs(item.DispenseDate).format('YYYY-MM') === dayjs(newItem.DispenseDate).format('YYYY-MM')
    );

    if (isDuplicate) {
      setSnackbar({ open: true, message: t('duplicateEntryError'), severity: 'error' });
      return;
    }

    setDispenseList(prevList => [...prevList, newItem]);
    setSnackbar({ open: true, message: t('itemAddedToList'), severity: 'success' });

    // Clear form
    setSelectedDrugId('');
    setQuantity('');
    setUnitPrice('');
    setCases('');
    // Keep selectedClinicId and dispenseMonth for convenience

    // Reset errors
    setDrugError(false);
    setQuantityError(false);
    setUnitPriceError(false);
    setCasesError(false);
    setMonthError(false);
  };

  const handleSaveAll = async () => {
    if (dispenseList.length === 0) {
      setSnackbar({ open: true, message: t('emptyDispenseList'), severity: 'error' });
      return;
    }

    try {
      const dispenseDataToSend = dispenseList.map(item => ({
        ClinicID: item.ClinicID,
        DrugID: item.DrugID,
        Quantity: item.Quantity,
        UnitPrice: item.UnitPrice,
        Cases: item.Cases,
        DispenseDate: item.DispenseDate,
      }));

      // Assuming a bulk dispense endpoint exists or we send them one by one
      // For now, let's assume a bulk endpoint for efficiency
      await axios.post('http://localhost:8000/dispensed-drugs/bulk/', { dispensed_drugs: dispenseDataToSend });

      setSnackbar({ open: true, message: t('allDispensedSuccess'), severity: 'success' });
      setDispenseList([]); // Clear the list after successful save
    } catch (error: any) {
      console.error('Error saving all dispensed drugs:', error);
      if (error.response && error.response.status === 400 && error.response.data && error.response.data.detail.includes("Duplicate dispense entry")) {
        setSnackbar({ open: true, message: t('duplicateEntryErrorBackend'), severity: 'error' });
      } else {
        setSnackbar({ open: true, message: t('dispenseError'), severity: 'error' });
      }
    }
  };

  const handleEditItem = (item: DispenseItem) => {
    setEditingItem(item);
    setSelectedClinicId(item.ClinicID);
    setSelectedDrugId(item.DrugID);
    setQuantity(item.Quantity.toString());
    setUnitPrice(item.UnitPrice.toString());
    setCases(item.Cases.toString());
    setDispenseMonth(dayjs(item.DispenseDate));
  };

  const handleUpdateItem = () => {
    if (!editingItem) return;

    let hasError = false;
    if (!selectedClinicId) { setClinicError(true); hasError = true; }
    if (!selectedDrugId) { setDrugError(true); hasError = true; }
    if (!quantity || parseFloat(quantity) <= 0) { setQuantityError(true); hasError = true; }
    if (!unitPrice || parseFloat(unitPrice) <= 0) { setUnitPriceError(true); hasError = true; }
    if (!cases || parseInt(cases) <= 0) { setCasesError(true); hasError = true; }
    if (!dispenseMonth) { setMonthError(true); hasError = true; }

    if (hasError) {
      setSnackbar({ open: true, message: t('fillAllRequiredFields'), severity: 'error' });
      return;
    }

    const clinic = clinics.find(c => c.id === selectedClinicId);
    const drug = drugs.find(d => d.DrugID === selectedDrugId);

    if (!clinic || !drug) {
      setSnackbar({ open: true, message: t('selectionError'), severity: 'error' });
      return;
    }

    const updatedItem: DispenseItem = {
      ...editingItem,
      ClinicID: selectedClinicId as number,
      DrugID: selectedDrugId as number,
      Quantity: parseFloat(quantity),
      UnitPrice: parseFloat(unitPrice),
      Cases: parseInt(cases),
      DispenseDate: dispenseMonth?.startOf('month').format('YYYY-MM-DD') || '',
      ClinicName: clinic.name,
      DrugName: drug.DrugName,
      DrugUnit: drug.Unit,
    };

    // Check for duplicates in the current dispenseList, excluding the item being edited
    const isDuplicate = dispenseList.some(item =>
      item.tempId !== updatedItem.tempId && // Exclude the item being edited
      item.ClinicID === updatedItem.ClinicID &&
      item.DrugID === updatedItem.DrugID &&
      item.Quantity === updatedItem.Quantity &&
      item.UnitPrice === updatedItem.UnitPrice &&
      dayjs(item.DispenseDate).format('YYYY-MM') === dayjs(updatedItem.DispenseDate).format('YYYY-MM')
    );

    if (isDuplicate) {
      setSnackbar({ open: true, message: t('duplicateEntryError'), severity: 'error' });
      return;
    }

    setDispenseList(prevList =>
      prevList.map(item =>
        item.tempId === editingItem.tempId
          ? updatedItem
          : item
      )
    );
    setEditingItem(null);
    setSnackbar({ open: true, message: t('itemUpdatedInList'), severity: 'success' });
    // Clear form fields after update
    setSelectedDrugId('');
    setQuantity('');
    setUnitPrice('');
    setCases('');
    setDispenseMonth(dayjs());
  };

  const handleDeleteItem = () => {
    if (!deleteItemId) return;
    setDispenseList(prevList => prevList.filter(item => item.tempId !== deleteItemId));
    setDeleteItemId(null);
    setSnackbar({ open: true, message: t('itemDeletedFromList'), severity: 'success' });
  };

  const handleCloseEditDialog = () => {
    setEditingItem(null);
    // Reset form fields and errors
    setSelectedDrugId('');
    setQuantity('');
    setUnitPrice('');
    setCases('');
    setDispenseMonth(dayjs());
    setClinicError(false);
    setDrugError(false);
    setQuantityError(false);
    setUnitPriceError(false);
    setCasesError(false);
    setMonthError(false);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteItemId(null);
  };

  const getDrugNameAndUnit = (drugId: number) => {
    const drug = drugs.find(d => d.DrugID === drugId);
    return drug ? `${drug.DrugName} (${drug.Unit})` : t('unknownDrug');
  };

  const getClinicName = (clinicId: number) => {
    const clinic = clinics.find(c => c.id === clinicId);
    return clinic ? clinic.name : t('unknownClinic');
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          {t('dispenseDrugs')}
        </Typography>

        <FormControl fullWidth sx={{ mb: 2 }} error={clinicError}>
          <InputLabel>{t('clinic')}</InputLabel>
          <Select
            value={selectedClinicId}
            label={t('clinic')}
            onChange={(e) => { setSelectedClinicId(e.target.value as number); setClinicError(false); }}
          >
            {clinics.map((clinic) => (
              <MenuItem key={clinic.id} value={clinic.id}>
                {clinic.name}
              </MenuItem>
            ))}
          </Select>
          {clinicError && <Typography color="error" variant="caption">{t('clinicRequired')}</Typography>}
        </FormControl>

        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>{t('filterByCategory')}</InputLabel>
          <Select
            value={selectedFilterCategoryId}
            label={t('filterByCategory')}
            onChange={(e) => { setSelectedFilterCategoryId(e.target.value as number); setSelectedDrugId(''); setDrugError(false); }}
          >
            <MenuItem value="">{t('allCategories')}</MenuItem>
            {categories.map((category) => (
              <MenuItem key={category.CategoryID} value={category.CategoryID}>
                {category.CategoryName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl fullWidth sx={{ mb: 2 }} error={drugError}>
          <InputLabel>{t('drug')}</InputLabel>
          <Select
            value={selectedDrugId}
            label={t('drug')}
            onChange={(e) => { setSelectedDrugId(e.target.value as number); setDrugError(false); }}
            disabled={drugs.length === 0}
          >
            {drugs.map((drug) => (
              <MenuItem key={drug.DrugID} value={drug.DrugID}>
                {drug.DrugName} ({drug.Unit})
              </MenuItem>
            ))}
          </Select>
          {drugError && <Typography color="error" variant="caption">{t('drugRequired')}</Typography>}
        </FormControl>

        <TextField
          label={t('quantity')}
          type="number"
          fullWidth
          value={quantity}
          onChange={(e) => { setQuantity(e.target.value); setQuantityError(false); }}
          sx={{ mb: 2 }}
          error={quantityError}
          helperText={quantityError ? t('quantityRequired') : ''}
        />

        <TextField
          label={t('unitPrice')}
          type="number"
          fullWidth
          value={unitPrice}
          onChange={(e) => { setUnitPrice(e.target.value); setUnitPriceError(false); }}
          sx={{ mb: 2 }}
          error={unitPriceError}
          helperText={unitPriceError ? t('unitPriceRequired') : ''}
        />

        <TextField
          label={t('cases')}
          type="number"
          fullWidth
          value={cases}
          onChange={(e) => { setCases(e.target.value); setCasesError(false); }}
          sx={{ mb: 2 }}
          error={casesError}
          helperText={casesError ? t('casesRequired') : ''}
        />

        <DatePicker
          label={t('dispenseMonth')}
          views={['year', 'month']}
          value={dispenseMonth}
          onChange={(value: unknown) => { setDispenseMonth(value as Dayjs | null); setMonthError(false); }}
          slots={{ textField: TextField }}
          slotProps={{
            textField: {
              fullWidth: true,
            }
          }}
        />

        <Button variant="contained" color="primary" onClick={handleAddItemToList} sx={{ mb: 2 }}>
          {t('addItemToList')}
        </Button>

        <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>
          {t('dispenseList')}
        </Typography>
        {dispenseList.length === 0 ? (
          <Typography>{t('emptyListMessage')}</Typography>
        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('clinic')}</TableCell>
                  <TableCell>{t('drug')}</TableCell>
                  <TableCell>{t('quantity')}</TableCell>
                  <TableCell>{t('unitPrice')}</TableCell>
                  <TableCell>{t('cases')}</TableCell>
                  <TableCell>{t('dispenseMonth')}</TableCell>
                  <TableCell align="right">{t('actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {dispenseList.map((item) => (
                  <TableRow key={item.tempId}>
                    <TableCell>{item.ClinicName}</TableCell>
                    <TableCell>{item.DrugName} ({item.DrugUnit})</TableCell>
                    <TableCell>{item.Quantity}</TableCell>
                    <TableCell>{item.UnitPrice}</TableCell>
                    <TableCell>{item.Cases}</TableCell>
                    <TableCell>{dayjs(item.DispenseDate).format('YYYY-MM')}</TableCell>
                    <TableCell align="right">
                      <IconButton color="primary" onClick={() => handleEditItem(item)}>
                        <Edit />
                      </IconButton>
                      <IconButton color="secondary" onClick={() => setDeleteItemId(item.tempId)}>
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {dispenseList.length > 0 && (
          <Button variant="contained" color="success" onClick={handleSaveAll} sx={{ mt: 2 }}>
            {t('saveAll')}
          </Button>
        )}

        {/* Edit Item Dialog */}
        <Dialog open={!!editingItem} onClose={handleCloseEditDialog}>
          <DialogTitle>{t('editDispenseItem')}</DialogTitle>
          <DialogContent>
            <FormControl fullWidth sx={{ mb: 2 }} error={clinicError}>
              <InputLabel>{t('clinic')}</InputLabel>
              <Select
                value={selectedClinicId}
                label={t('clinic')}
                onChange={(e) => { setSelectedClinicId(e.target.value as number); setClinicError(false); }}
              >
                {clinics.map((clinic) => (
                  <MenuItem key={clinic.id} value={clinic.id}>
                    {clinic.name}
                  </MenuItem>
                ))}
              </Select>
              {clinicError && <Typography color="error" variant="caption">{t('clinicRequired')}</Typography>}
            </FormControl>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>{t('filterByCategory')}</InputLabel>
              <Select
                value={selectedFilterCategoryId}
                label={t('filterByCategory')}
                onChange={(e) => { setSelectedFilterCategoryId(e.target.value as number); setSelectedDrugId(''); setDrugError(false); }}
              >
                <MenuItem value="">{t('allCategories')}</MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category.CategoryID} value={category.CategoryID}>
                    {category.CategoryName}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth sx={{ mb: 2 }} error={drugError}>
              <InputLabel>{t('drug')}</InputLabel>
              <Select
                value={selectedDrugId}
                label={t('drug')}
                onChange={(e) => { setSelectedDrugId(e.target.value as number); setDrugError(false); }}
                disabled={drugs.length === 0}
              >
                {drugs.map((drug) => (
                  <MenuItem key={drug.DrugID} value={drug.DrugID}>
                    {drug.DrugName} ({drug.Unit})
                  </MenuItem>
                ))}
              </Select>
              {drugError && <Typography color="error" variant="caption">{t('drugRequired')}</Typography>}
            </FormControl>

            <TextField
              label={t('quantity')}
              type="number"
              fullWidth
              value={quantity}
              onChange={(e) => { setQuantity(e.target.value); setQuantityError(false); }}
              sx={{ mb: 2 }}
              error={quantityError}
              helperText={quantityError ? t('quantityRequired') : ''}
            />

            <TextField
              label={t('unitPrice')}
              type="number"
              fullWidth
              value={unitPrice}
              onChange={(e) => { setUnitPrice(e.target.value); setUnitPriceError(false); }}
              sx={{ mb: 2 }}
              error={unitPriceError}
              helperText={unitPriceError ? t('unitPriceRequired') : ''}
            />

            <TextField
              label={t('cases')}
              type="number"
              fullWidth
              value={cases}
              onChange={(e) => { setCases(e.target.value); setCasesError(false); }}
              sx={{ mb: 2 }}
              error={casesError}
              helperText={casesError ? t('casesRequired') : ''}
            />

            <DatePicker
              label={t('dispenseMonth')}
              views={['year', 'month']}
              value={dispenseMonth}
              onChange={(value: unknown) => { setDispenseMonth(value as Dayjs | null); setMonthError(false); }}
              slots={{ textField: TextField }}
              slotProps={{
                textField: {
                  fullWidth: true,
                  sx: { mb: 2 },
                  error: monthError,
                  helperText: monthError ? t('monthRequired') : ''
                }
              }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseEditDialog}>{t('cancel')}</Button>
            <Button onClick={handleUpdateItem}>{t('save')}</Button>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={!!deleteItemId}
          onClose={handleCloseDeleteDialog}
        >
          <DialogTitle>{t('confirmDeleteTitle')}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {t('confirmDeleteItemMessage')}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDeleteDialog}>{t('cancel')}</Button>
            <Button onClick={handleDeleteItem} color="error">
              {t('delete')}
            </Button>
          </DialogActions>
        </Dialog>

        {snackbar && (
          <Snackbar
            open={snackbar.open}
            autoHideDuration={6000}
            onClose={() => setSnackbar(null)}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          >
            <Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>
              {snackbar.message}
            </Alert>
          </Snackbar>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default DispenseDrugsPage;
