import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    Box, Button, Typography, Select, MenuItem, FormControl, InputLabel,
    TextField, CircularProgress, Table, TableBody, TableCell, TableContainer,
    TableHead, TableRow, Paper, RadioGroup, FormControlLabel, Radio, Checkbox, ListItemText, OutlinedInput
} from '@mui/material';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const API_URL = 'http://127.0.0.1:8000';

// --- Interfaces ---
interface Branch { id: number; name: string; }
interface Region { id: number; name: string; branch_id: number; }
interface Clinic { id: number; name: string; region_id: number; }
interface DrugCategory { CategoryID: number; CategoryName: string; }
interface Drug { DrugID: number; DrugName: string; CategoryID: number; }

interface ReportDataItem {
    [key: string]: any;
}

const Reports = () => {
    // --- State Variables ---
    const [branches, setBranches] = useState<Branch[]>([]);
    const [regions, setRegions] = useState<Region[]>([]);
    const [clinics, setClinics] = useState<Clinic[]>([]);
    const [drugCategories, setDrugCategories] = useState<DrugCategory[]>([]);
    const [drugs, setDrugs] = useState<Drug[]>([]);

    const [reportMode, setReportMode] = useState<'standard' | 'comparison'>('standard');
    const [reportType, setReportType] = useState<'clinic' | 'region' | 'branch'>('clinic');
    const [aggregationType, setAggregationType] = useState<'detailed' | 'aggregated'>('aggregated');

    // Filters
    const [selectedBranch, setSelectedBranch] = useState<number | ''>('');
    const [selectedRegion, setSelectedRegion] = useState<number | ''>('');
    const [selectedClinic, setSelectedClinic] = useState<number | ''>('');
    const [selectedCategory, setSelectedCategory] = useState<number | ''>('');
    const [selectedDrug, setSelectedDrug] = useState<number | ''>('');
    const [startMonth, setStartMonth] = useState('');
    const [endMonth, setEndMonth] = useState('');

    // Comparison Filters
    const [comparisonLevel, setComparisonLevel] = useState<'clinics' | 'regions' | 'branches'>('clinics');
    const [selectedEntities, setSelectedEntities] = useState<number[]>([]);

    // Report Data
    const [reportData, setReportData] = useState<ReportDataItem[]>([]);
    const [chartData, setChartData] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [reportLoading, setReportLoading] = useState(false);

    // --- Data Fetching ---
    useEffect(() => {
        fetchInitialData();
    }, []);

    const fetchInitialData = async () => {
        setLoading(true);
        try {
            const [branchesRes, regionsRes, clinicsRes, categoriesRes, drugsRes] = await Promise.all([
                axios.get(`${API_URL}/branches/`),
                axios.get(`${API_URL}/regions/`),
                axios.get(`${API_URL}/clinics/`),
                axios.get(`${API_URL}/drug-categories/`),
                axios.get(`${API_URL}/drugs/`),
            ]);
            setBranches(branchesRes.data);
            setRegions(regionsRes.data);
            setClinics(clinicsRes.data);
            setDrugCategories(categoriesRes.data.map((c: any) => ({ CategoryID: c.CategoryID, CategoryName: c.CategoryName })));
            setDrugs(drugsRes.data.map((d: any) => ({ DrugID: d.DrugID, DrugName: d.DrugName, CategoryID: d.CategoryID })));
        } catch (error) {
            console.error('Error fetching initial data:', error);
        }
        setLoading(false);
    };

    // --- Report Generation ---
    const handleGenerateReport = async () => {
        setReportLoading(true);
        setReportData([]);
        setChartData([]);
        let url = `${API_URL}/reports/`;
        const params: any = {};

        if (reportMode === 'standard') {
            url += `dispensed-drugs/by-${reportType}`;
            if (reportType === 'clinic' && selectedClinic) params.clinic_id = selectedClinic;
            if (reportType === 'region' && selectedRegion) params.region_id = selectedRegion;
            if (reportType === 'branch' && selectedBranch) params.branch_id = selectedBranch;
        } else { // Comparison
            url += `comparison/${comparisonLevel}`;
            if (selectedEntities.length === 0) {
                alert('Please select at least one entity to compare.');
                setReportLoading(false);
                return;
            }
            params[`${comparisonLevel.slice(0, -1)}_ids`] = selectedEntities.join(',');
        }

        if (selectedCategory) params.category_id = selectedCategory;
        if (selectedDrug) params.drug_id = selectedDrug;
        if (startMonth) params.start_month = startMonth;
        if (endMonth) params.end_month = endMonth;

        try {
            const response = await axios.get(url, { params });
            console.log("--- DEBUG: Data received from backend ---");
            console.log(JSON.stringify(response.data, null, 2));
            setReportData(response.data);
            if (reportMode === 'comparison') {
                processChartData(response.data);
            }
        } catch (error) {
            console.error('Error generating report:', error);
        }
        setReportLoading(false);
    };

    const processChartData = (data: any[]) => {
        const groupedData: { [key: string]: any } = {};
        const entityNames: { [key: number]: string } = {};

        data.forEach(item => {
            const month = item.Month;
            if (!groupedData[month]) {
                groupedData[month] = { Month: month };
            }
            if (!entityNames[item.EntityID]) {
                entityNames[item.EntityID] = item.EntityName;
            }
            groupedData[month][item.EntityName] = (groupedData[month][item.EntityName] || 0) + item.TotalQuantity;
        });

        setChartData(Object.values(groupedData));
    };

    // --- UI Renderers ---
    const renderName = (id: number, type: 'clinic' | 'region' | 'branch' | 'drug' | 'category') => {
        switch (type) {
            case 'clinic': return clinics.find(c => c.id === id)?.name || 'N/A';
            case 'region': return regions.find(r => r.id === id)?.name || 'N/A';
            case 'branch': return branches.find(b => b.id === id)?.name || 'N/A';
            case 'drug': return drugs.find(d => d.DrugID === id)?.DrugName || 'N/A';
            case 'category': return drugCategories.find(c => c.CategoryID === id)?.CategoryName || 'N/A';
            default: return 'N/A';
        }
    };

    const renderStandardFilters = () => (
        <>
            <FormControl sx={{ minWidth: 180 }}>
                <InputLabel>المستوى</InputLabel>
                <Select value={reportType} onChange={(e) => setReportType(e.target.value as any)} label="المستوى">
                    <MenuItem value="clinic">عيادة</MenuItem>
                    <MenuItem value="region">منطقة</MenuItem>
                    <MenuItem value="branch">فرع</MenuItem>
                </Select>
            </FormControl>
            {reportType === 'branch' &&
                <FormControl sx={{ minWidth: 180 }}>
                    <InputLabel>الفرع</InputLabel>
                    <Select value={selectedBranch} onChange={(e) => setSelectedBranch(e.target.value as number)} label="الفرع">
                        {branches.map(b => <MenuItem key={b.id} value={b.id}>{b.name}</MenuItem>)}
                    </Select>
                </FormControl>}
            {reportType === 'region' &&
                <FormControl sx={{ minWidth: 180 }}>
                    <InputLabel>المنطقة</InputLabel>
                    <Select value={selectedRegion} onChange={(e) => setSelectedRegion(e.target.value as number)} label="المنطقة">
                        {regions.map(r => <MenuItem key={r.id} value={r.id}>{r.name}</MenuItem>)}
                    </Select>
                </FormControl>}
            {reportType === 'clinic' &&
                <FormControl sx={{ minWidth: 180 }}>
                    <InputLabel>العيادة</InputLabel>
                    <Select value={selectedClinic} onChange={(e) => setSelectedClinic(e.target.value as number)} label="العيادة">
                        {clinics.map(c => <MenuItem key={c.id} value={c.id}>{c.name}</MenuItem>)}
                    </Select>
                </FormControl>}
        </>
    );

    const renderComparisonFilters = () => {
        const entityList = {
            clinics: clinics.map(c => ({ id: c.id, name: c.name })),
            regions: regions.map(r => ({ id: r.id, name: r.name })),
            branches: branches.map(b => ({ id: b.id, name: b.name }))
        };

        return (
            <>
                <FormControl sx={{ minWidth: 180 }}>
                    <InputLabel>مستوى المقارنة</InputLabel>
                    <Select value={comparisonLevel} onChange={(e) => {
                        setComparisonLevel(e.target.value as any);
                        setSelectedEntities([]);
                    }} label="مستوى المقارنة">
                        <MenuItem value="clinics">عيادات</MenuItem>
                        <MenuItem value="regions">مناطق</MenuItem>
                        <MenuItem value="branches">فروع</MenuItem>
                    </Select>
                </FormControl>
                <FormControl sx={{ minWidth: 250 }}>
                    <InputLabel>الكيانات للمقارنة</InputLabel>
                    <Select
                        multiple
                        value={selectedEntities}
                        onChange={(e) => setSelectedEntities(e.target.value as number[])}
                        input={<OutlinedInput label="الكيانات للمقارنة" />}
                        renderValue={(selected) => (selected as number[]).map(id => entityList[comparisonLevel].find(e => e.id === id)?.name).join(', ')}
                    >
                        {entityList[comparisonLevel].map((entity) => (
                            <MenuItem key={entity.id} value={entity.id}>
                                <Checkbox checked={selectedEntities.indexOf(entity.id) > -1} />
                                <ListItemText primary={entity.name} />
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </>
        );
    };

    const renderReportTable = () => {
        if (reportData.length === 0) {
            return <Typography sx={{ mt: 3 }}>لا توجد بيانات لعرضها. الرجاء تحديد الفلاتر وتوليد التقرير.</Typography>;
        }

        const headers = reportMode === 'standard'
            ? ['الشهر', 'الكيان', 'الدواء', 'الصنف', 'الكمية الإجمالية', 'التكلفة الإجمالية', 'عدد الحالات']
            : ['الشهر', 'الكيان', 'الدواء', 'الصنف', 'الكمية الإجمالية', 'التكلفة الإجمالية', 'عدد الحالات'];

        return (
            <TableContainer component={Paper} sx={{ mt: 3 }}>
                <Table>
                    <TableHead>
                        <TableRow>{headers.map(h => <TableCell key={h}>{h}</TableCell>)}</TableRow>
                    </TableHead>
                    <TableBody>
                        {reportData.map((item, index) => (
                            <TableRow key={index}>
                                <TableCell>{item.Month}</TableCell>
                                <TableCell>{item.EntityName || item.ClinicName || item.RegionName || item.BranchName}</TableCell>
                                <TableCell>{item.DrugName}</TableCell>
                                <TableCell>{item.CategoryName}</TableCell>
                                <TableCell>{item.TotalQuantity}</TableCell>
                                <TableCell>{item.TotalCost.toFixed(2)}</TableCell>
                                <TableCell>{item.NumberOfCases}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        );
    };

    const renderChart = () => {
        if (chartData.length === 0) return null;

        const entityNames = selectedEntities.map(id => {
            if (comparisonLevel === 'clinics') return renderName(id, 'clinic');
            if (comparisonLevel === 'regions') return renderName(id, 'region');
            if (comparisonLevel === 'branches') return renderName(id, 'branch');
            return '';
        });

        const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088FE', '#00C49F'];

        return (
            <Box sx={{ height: 400, mt: 4 }}>
                <Typography variant="h6" align="center">مقارنة الكميات الشهرية</Typography>
                <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="Month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        {entityNames.map((name, index) => (
                            <Bar key={name} dataKey={name} fill={colors[index % colors.length]} />
                        ))}
                    </BarChart>
                </ResponsiveContainer>
            </Box>
        );
    };


    if (loading) return <CircularProgress />;

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>التقارير</Typography>

            <FormControl component="fieldset" sx={{ mb: 2 }}>
                <RadioGroup row value={reportMode} onChange={(e) => setReportMode(e.target.value as any)}>
                    <FormControlLabel value="standard" control={<Radio />} label="تقرير قياسي" />
                    <FormControlLabel value="comparison" control={<Radio />} label="تقرير مقارنة" />
                </RadioGroup>
            </FormControl>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 3 }}>
                {reportMode === 'standard' ? renderStandardFilters() : renderComparisonFilters()}

                {/* Common Filters */}
                <FormControl sx={{ minWidth: 180 }}>
                    <InputLabel>تصنيف الدواء</InputLabel>
                    <Select value={selectedCategory} onChange={(e) => setSelectedCategory(e.target.value as number)} label="تصنيف الدواء">
                        <MenuItem value="">الكل</MenuItem>
                        {drugCategories.map(c => <MenuItem key={c.CategoryID} value={c.CategoryID}>{c.CategoryName}</MenuItem>)}
                    </Select>
                </FormControl>
                <FormControl sx={{ minWidth: 180 }}>
                    <InputLabel>الدواء</InputLabel>
                    <Select value={selectedDrug} onChange={(e) => setSelectedDrug(e.target.value as number)} label="الدواء">
                        <MenuItem value="">الكل</MenuItem>
                        {drugs.filter(d => !selectedCategory || d.CategoryID === selectedCategory).map(d => <MenuItem key={d.DrugID} value={d.DrugID}>{d.DrugName}</MenuItem>)}
                    </Select>
                </FormControl>
                <TextField
                    label="شهر البدء" type="month" value={startMonth}
                    onChange={(e) => setStartMonth(e.target.value)}
                    InputLabelProps={{ shrink: true }} sx={{ minWidth: 180 }}
                />
                <TextField
                    label="شهر الانتهاء" type="month" value={endMonth}
                    onChange={(e) => setEndMonth(e.target.value)}
                    InputLabelProps={{ shrink: true }} sx={{ minWidth: 180 }}
                />
                <Button variant="contained" onClick={handleGenerateReport} disabled={reportLoading} sx={{ alignSelf: 'center' }}>
                    {reportLoading ? <CircularProgress size={24} /> : 'توليد التقرير'}
                </Button>
            </Box>

            {reportMode === 'comparison' && renderChart()}
            {renderReportTable()}
        </Box>
    );
};

export default Reports;