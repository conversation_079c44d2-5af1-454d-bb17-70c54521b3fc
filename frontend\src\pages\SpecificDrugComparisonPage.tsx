import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Alert,
  Button,
  TextField,
  OutlinedInput,
  Checkbox,
  ListItemText,
  Grid,
  Paper,
  Card,
  CardContent,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { DataGrid, GridColDef, GridToolbar } from '@mui/x-data-grid';
import dayjs, { Dayjs } from 'dayjs';
import * as XLSX from 'xlsx';

// Interfaces
interface Branch { id: number; name: string; }
interface Region { id: number; name: string; branch_id: number; }
interface Clinic { id: number; name: string; region_id: number; }
interface DrugCategory { CategoryID: number; CategoryName: string; }
interface Drug { DrugID: number; DrugName: string; CategoryID: number; Unit: string | null; }
interface ComparisonDispensedDrugData {
  id: string; // Required for DataGrid
  Month: string; // Added for monthly comparison
  EntityID: number;
  EntityName: string;
  DrugID: number;
  DrugName: string;
  DrugUnit: string | null;
  CategoryID: number;
  CategoryName: string;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
}

const SpecificDrugComparisonPage = () => {
  const { t } = useTranslation();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [categories, setCategories] = useState<DrugCategory[]>([]);
  const [drugs, setDrugs] = useState<Drug[]>([]); // All drugs
  const [filteredDrugs, setFilteredDrugs] = useState<Drug[]>([]); // Drugs filtered by category

  // Filters
  const [compareEntityType, setCompareEntityType] = useState<'clinic' | 'region' | 'branch'>('clinic');
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [filterCategoryId, setFilterCategoryId] = useState<number | ''>('');
  const [filterDrugId, setFilterDrugId] = useState<number | ''>('');
  const [filterStartDate, setFilterStartDate] = useState<Dayjs | null>(dayjs().subtract(1, 'month'));
  const [filterEndDate, setFilterEndDate] = useState<Dayjs | null>(dayjs());

  // Data
  const [comparisonData, setComparisonData] = useState<ComparisonDispensedDrugData[]>([]);
  const [monthlyChartData, setMonthlyChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [branchesRes, regionsRes, clinicsRes, categoriesRes, drugsRes] = await Promise.all([
          axios.get<Branch[]>('http://localhost:8000/branches/'),
          axios.get<Region[]>('http://localhost:8000/regions/'),
          axios.get<Clinic[]>('http://localhost:8000/clinics/'),
          axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/'),
          axios.get<Drug[]>('http://localhost:8000/drugs/')
        ]);
        setBranches(branchesRes.data);
        setRegions(regionsRes.data);
        setClinics(clinicsRes.data);
        setCategories(categoriesRes.data);
        setDrugs(drugsRes.data);
      } catch (error) {
        console.error('Error fetching initial data:', error);
        setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
      }
    };
    fetchData();
  }, [t]);

  // Filter drugs based on selected category
  useEffect(() => {
    if (filterCategoryId) {
      setFilteredDrugs(drugs.filter(drug => drug.CategoryID === filterCategoryId));
    } else {
      setFilteredDrugs(drugs);
    }
    setFilterDrugId(''); // Reset selected drug when category changes
  }, [filterCategoryId, drugs]);

  // Fetch comparison data when filters change
  const fetchComparisonData = async () => {
    if (selectedIds.length === 0 || !filterDrugId) {
      setComparisonData([]);
      return;
    }
    setLoading(true);
    try {
      const url = `http://localhost:8000/reports/comparison/${compareEntityType === 'branch' ? 'branches' : compareEntityType + 's'}`;
      const idsParam = `${compareEntityType}_ids=${selectedIds.join(',')}`;
      const drugParam = `drug_id=${filterDrugId}`;
      const startDateParam = filterStartDate ? `start_month=${filterStartDate.format('YYYY-MM')}` : '';
      const endDateParam = filterEndDate ? `end_month=${filterEndDate.format('YYYY-MM')}` : '';
      const queryParams = [idsParam, drugParam, startDateParam, endDateParam].filter(Boolean).join('&');
      const fullUrl = `${url}?${queryParams}`;
      const response = await axios.get<ComparisonDispensedDrugData[]>(fullUrl);
      
      const data = response.data.map((item, index) => ({
        ...item,
        id: `${item.EntityID}-${item.DrugID}-${item.Month}-${index}` // Unique ID for DataGrid
      }));
      setComparisonData(data);

      // Process data for monthly comparison chart
      const monthlyAggregatedData: { [key: string]: any } = {};
      data.forEach(item => {
        if (!monthlyAggregatedData[item.Month]) {
          monthlyAggregatedData[item.Month] = { Month: item.Month };
        }
        monthlyAggregatedData[item.Month][item.EntityName] = (
          monthlyAggregatedData[item.Month][item.EntityName] || 0
        ) + item.TotalQuantity;
      });
      setMonthlyChartData(Object.values(monthlyAggregatedData));

    } catch (error) {
      console.error('Error fetching report data:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
      setComparisonData([]);
      setMonthlyChartData([]);
    } finally {
      setLoading(false);
    }
  };

  const handleResetFilters = () => {
    setCompareEntityType('clinic');
    setSelectedIds([]);
    setFilterCategoryId('');
    setFilterDrugId('');
    setFilterStartDate(dayjs().subtract(1, 'year'));
    setFilterEndDate(dayjs());
    setComparisonData([]);
  };

  const kpiData = useMemo(() => {
    return comparisonData.reduce((acc, item) => {
      acc.totalCost += item.TotalCost;
      acc.totalQuantity += item.TotalQuantity;
      acc.totalCases += item.NumberOfCases;
      return acc;
    }, { totalCost: 0, totalQuantity: 0, totalCases: 0 });
  }, [comparisonData]);

  const entityComparisonData = useMemo(() => {
    const data = comparisonData.reduce((acc, item) => {
        const entity = item.EntityName;
        if (!acc[entity]) {
            acc[entity] = { name: entity, TotalCost: 0, TotalQuantity: 0, NumberOfCases: 0 };
        }
        acc[entity].TotalCost += item.TotalCost;
        acc[entity].TotalQuantity += item.TotalQuantity;
        acc[entity].NumberOfCases += item.NumberOfCases;
        return acc;
    }, {} as { [key: string]: { name: string, TotalCost: number, TotalQuantity: number, NumberOfCases: number } });
    return Object.values(data);
}, [comparisonData]);

  const renderEntitySelection = () => {
    const items = compareEntityType === 'clinic' ? clinics : compareEntityType === 'region' ? regions : branches;
    const label = t(`select${compareEntityType.charAt(0).toUpperCase() + compareEntityType.slice(1)}s`);
    return (
      <FormControl sx={{ minWidth: 240, maxWidth: 400 }}>
        <InputLabel>{label}</InputLabel>
        <Select
          multiple
          value={selectedIds}
          onChange={(e) => setSelectedIds(e.target.value as number[])}
          input={<OutlinedInput label={label} />}
          renderValue={(selected) => selected.map(id => items.find(i => i.id === id)?.name).join(', ')}
        >
          {items.map((item) => (
            <MenuItem key={item.id} value={item.id}>
              <Checkbox checked={selectedIds.indexOf(item.id) > -1} />
              <ListItemText primary={item.name} />
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    );
  };

  const columns: GridColDef[] = [
    { field: 'Month', headerName: t('month'), width: 100 },
    { field: 'EntityName', headerName: t('entityName'), width: 180 },
    { field: 'DrugName', headerName: t('drugName'), width: 150 },
    { field: 'CategoryName', headerName: t('categoryName'), width: 150 },
    { field: 'TotalQuantity', headerName: t('totalQuantity'), type: 'number', width: 130 },
    { field: 'TotalCost', headerName: t('totalCost'), type: 'number', width: 130, valueFormatter: (params: { value: number }) => params.value.toFixed(2) },
    { field: 'NumberOfCases', headerName: t('numberOfCases'), type: 'number', width: 130 },
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#AF19FF', '#FF1919'];

  const renderEntityName = (id: number): string => {
    if (compareEntityType === 'clinic') return clinics.find(c => c.id === id)?.name || String(id);
    if (compareEntityType === 'region') return regions.find(r => r.id === id)?.name || String(id);
    if (compareEntityType === 'branch') return branches.find(b => b.id === id)?.name || String(id);
    return String(id);
  };

  const handleExportExcel = () => {
    if (comparisonData.length === 0) {
      setSnackbar({ open: true, message: t('noDataToExport'), severity: 'error' });
      return;
    }

    const dataToExport = comparisonData.map(row => ({
      [t('month')]: row.Month,
      [t('entityName')]: row.EntityName,
      [t('drugName')]: row.DrugName,
      [t('categoryName')]: row.CategoryName,
      [t('totalQuantity')]: row.TotalQuantity,
      [t('totalCost')]: row.TotalCost,
      [t('numberOfCases')]: row.NumberOfCases,
    }));

    // Create worksheet with title
    const reportTitle = t('specificDrugComparison');
    const aoa: (string | number)[][] = [];
    aoa.push([reportTitle]); // Main title
    aoa.push([]); // Spacer row

    // Add headers
    const headers = Object.keys(dataToExport[0]);
    aoa.push(headers);

    // Add data rows
    dataToExport.forEach(row => {
      aoa.push(Object.values(row));
    });

    const ws = XLSX.utils.aoa_to_sheet(aoa);
    const wb = XLSX.utils.book_new();

    // Calculate column widths
    const colWidths = headers.map(header => {
      let maxWidth = header.length;
      dataToExport.forEach(row => {
        const cellValue = String(row[header]);
        maxWidth = Math.max(maxWidth, cellValue.length);
      });
      return { wch: Math.min(maxWidth + 2, 50) }; // Add padding and cap at 50
    });
    ws['!cols'] = colWidths;

    // Apply borders to all cells first
    const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
    for (let R = range.s.r; R <= range.e.r; ++R) {
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
        if (!ws[cellAddress]) {
          ws[cellAddress] = { v: aoa[R] && aoa[R][C] ? aoa[R][C] : '' };
        }
        if (!ws[cellAddress].s) ws[cellAddress].s = {};

        // Apply basic border to all cells
        ws[cellAddress].s.border = {
          top: { style: "thin", color: { rgb: "FF000000" } },
          bottom: { style: "thin", color: { rgb: "FF000000" } },
          left: { style: "thin", color: { rgb: "FF000000" } },
          right: { style: "thin", color: { rgb: "FF000000" } }
        };
      }
    }

    // Style main title (first row)
    const mainTitleCell = XLSX.utils.encode_cell({ r: 0, c: 0 });
    if (!ws[mainTitleCell].s) ws[mainTitleCell].s = {};
    ws[mainTitleCell].s = {
      ...ws[mainTitleCell].s,
      font: { bold: true, sz: 16 },
      alignment: { horizontal: "center", vertical: "center" },
      fill: { fgColor: { rgb: "FFCCDDFF" } }, // Light blue background
      border: {
        top: { style: "thick", color: { rgb: "FF000000" } },
        bottom: { style: "thick", color: { rgb: "FF000000" } },
        left: { style: "thick", color: { rgb: "FF000000" } },
        right: { style: "thick", color: { rgb: "FF000000" } }
      }
    };

    // Merge title cell across all columns
    const titleMerge = { s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } };
    if (!ws['!merges']) ws['!merges'] = [];
    ws['!merges'].push(titleMerge);

    // Style headers (row 2, index 2)
    const headerRowIndex = 2;
    for (let c = 0; c < headers.length; c++) {
      const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: c });
      if (!ws[cellAddress].s) ws[cellAddress].s = {};
      ws[cellAddress].s = {
        ...ws[cellAddress].s,
        font: { bold: true, sz: 11, color: { rgb: "FFFFFFFF" } }, // White text
        alignment: { horizontal: "center", vertical: "center" },
        fill: { fgColor: { rgb: "FF4472C4" } }, // Blue background
        border: {
          top: { style: "thick", color: { rgb: "FF000000" } },
          bottom: { style: "thick", color: { rgb: "FF000000" } },
          left: { style: "thick", color: { rgb: "FF000000" } },
          right: { style: "thick", color: { rgb: "FF000000" } }
        }
      };
    }

    // Style data rows (starting from row 3, index 3)
    for (let r = 3; r < aoa.length; r++) {
      for (let c = 0; c < headers.length; c++) {
        const cellAddress = XLSX.utils.encode_cell({ r: r, c: c });
        if (!ws[cellAddress].s) ws[cellAddress].s = {};

        // Alternate row colors for better readability
        const isEvenRow = (r - 3) % 2 === 0;
        ws[cellAddress].s = {
          ...ws[cellAddress].s,
          font: { sz: 10 },
          alignment: { horizontal: "center", vertical: "center" },
          fill: { fgColor: { rgb: isEvenRow ? "FFF0F0F0" : "FFFFFFFF" } }, // Alternating light gray and white
          border: {
            top: { style: "thin", color: { rgb: "FF000000" } },
            bottom: { style: "thin", color: { rgb: "FF000000" } },
            left: { style: "thin", color: { rgb: "FF000000" } },
            right: { style: "thin", color: { rgb: "FF000000" } }
          }
        };
      }
    }

    XLSX.utils.book_append_sheet(wb, ws, "تقرير مقارنة الأدوية");

    // Write file with proper options for styling
    const wbout = XLSX.write(wb, {
      bookType: 'xlsx',
      type: 'array',
      cellStyles: true,
      bookSST: false
    });

    // Create blob and download
    const blob = new Blob([wbout], { type: 'application/octet-stream' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'تقرير_مقارنة_الأدوية_المحددة.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleExportPdf = async () => {
    if (comparisonData.length === 0) {
      setSnackbar({ open: true, message: t('noDataToExport'), severity: 'error' });
      return;
    }

    try {
      const reportTitle = t('specificDrugComparisonReport');
      const dataToExport = comparisonData.map(item => ({
        [t('month')]: item.Month,
        [t('entityName')]: item.EntityName,
        [t('drugName')]: item.DrugName,
        [t('categoryName')]: item.CategoryName,
        [t('totalQuantity')]: item.TotalQuantity,
        [t('totalCost')]: item.TotalCost.toFixed(2),
        [t('numberOfCases')]: item.NumberOfCases,
      }));

      const response = await axios.post(
        'http://localhost:8000/generate-pdf-report',
        dataToExport, // Send the array directly
        {
          responseType: 'blob', // Important for receiving binary data
          params: { title: reportTitle } // Send title as a query parameter
        }
      );

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'specific-drug-comparison-report.pdf');
      document.body.appendChild(link);
      link.click();
      link.parentNode?.removeChild(link);
      setSnackbar({ open: true, message: t('pdfExportSuccess'), severity: 'success' });
    } catch (error) {
      console.error('Error exporting PDF:', error);
      setSnackbar({ open: true, message: t('pdfExportError'), severity: 'error' });
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }} id="specific-drug-report-content"> {/* Added ID here */}
        <Typography variant="h4" gutterBottom>{t('specificDrugComparisonReport')}</Typography>
        
        {/* --- FILTERS --- */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth><InputLabel>{t('compareBy')}</InputLabel><Select value={compareEntityType} label={t('compareBy')} onChange={(e) => { setCompareEntityType(e.target.value as any); setSelectedIds([]); }}>
              <MenuItem value="clinic">{t('clinics')}</MenuItem>
              <MenuItem value="region">{t('regions')}</MenuItem>
              <MenuItem value="branch">{t('branches')}</MenuItem>
            </Select></FormControl></Grid>
            <Grid item xs={12} sm={6} md={3}>{renderEntitySelection()}</Grid>
            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth><InputLabel>{t('filterByCategory')}</InputLabel><Select value={filterCategoryId} label={t('filterByCategory')} onChange={(e) => setFilterCategoryId(e.target.value as number)}><MenuItem value="">{t('allCategories')}</MenuItem>{categories.map((cat) => <MenuItem key={cat.CategoryID} value={cat.CategoryID}>{cat.CategoryName}</MenuItem>)}</Select></FormControl></Grid>
            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth><InputLabel>{t('filterByDrug')}</InputLabel><Select value={filterDrugId} label={t('filterByDrug')} onChange={(e) => setFilterDrugId(e.target.value as number)} disabled={filteredDrugs.length === 0 && filterCategoryId !== ''}><MenuItem value="">{t('allDrugs')}</MenuItem>{filteredDrugs.map((drug) => <MenuItem key={drug.DrugID} value={drug.DrugID}>{drug.DrugName}</MenuItem>)}</Select></FormControl></Grid>
            <Grid item xs={12} sm={6} md={3}><DatePicker label={t('filterByStartMonth')} views={['year', 'month']} openTo="month" value={filterStartDate} onChange={setFilterStartDate} slots={{ textField: TextField }} slotProps={{ textField: { fullWidth: true } }} /></Grid>
            <Grid item xs={12} sm={6} md={3}><DatePicker label={t('filterByEndMonth')} views={['year', 'month']} openTo="month" value={filterEndDate} onChange={setFilterEndDate} slots={{ textField: TextField }} slotProps={{ textField: { fullWidth: true } }} /></Grid>
            <Grid item xs={12} sm={6} md={3}><Button variant="contained" color="primary" onClick={fetchComparisonData} disabled={loading || selectedIds.length === 0 || !filterDrugId}>{t('applyFilters')}</Button></Grid>
            <Grid item xs={12} sm={6} md={3}><Button variant="outlined" color="secondary" onClick={handleResetFilters}>{t('resetFilters')}</Button></Grid>
            <Grid item xs={12} sm={6} md={3}><Button variant="contained" color="success" onClick={handleExportExcel} disabled={comparisonData.length === 0}>{t('exportToExcel')}</Button></Grid>
            <Grid item xs={12} sm={6} md={3}><Button variant="contained" color="error" onClick={handleExportPdf} disabled={comparisonData.length === 0}>{t('exportToPDF')}</Button></Grid>
          </Grid>
        </Paper>

        {/* --- KPIs --- */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color="textSecondary" gutterBottom>{t('totalCost')}</Typography><Typography variant="h5">{kpiData.totalCost.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Typography></CardContent></Card></Grid>
            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color="textSecondary" gutterBottom>{t('totalQuantity')}</Typography><Typography variant="h5">{kpiData.totalQuantity.toLocaleString()}</Typography></CardContent></Card></Grid>
            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color="textSecondary" gutterBottom>{t('numberOfCases')}</Typography><Typography variant="h5">{kpiData.totalCases.toLocaleString()}</Typography></CardContent></Card></Grid>
            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color="textSecondary" gutterBottom>{t('avgCostPerCase')}</Typography><Typography variant="h5">{(kpiData.totalCases > 0 ? kpiData.totalCost / kpiData.totalCases : 0).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Typography></CardContent></Card></Grid>
        </Grid>

        {/* --- CHARTS --- */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12}><Paper sx={{ p: 2, height: 400 }}><Typography variant="h6">{t('entityComparison')}</Typography><ResponsiveContainer><BarChart data={entityComparisonData}><CartesianGrid strokeDasharray="3 3" /><XAxis dataKey="name" /><YAxis /><Tooltip /><Legend /><Bar dataKey="TotalCost" fill="#82ca9d" name={t('totalCost')} /><Bar dataKey="TotalQuantity" fill="#8884d8" name={t('totalQuantity')} /></BarChart></ResponsiveContainer></Paper></Grid>
          {monthlyChartData.length > 0 && (
            <Grid item xs={12}><Paper sx={{ p: 2, height: 400, mt: 3 }}><Typography variant="h6">{t('monthlyComparison')}</Typography><ResponsiveContainer><BarChart data={monthlyChartData}><CartesianGrid strokeDasharray="3 3" /><XAxis dataKey="Month" /><YAxis /><Tooltip /><Legend />
                {selectedIds.map((id, index) => (
                  <Bar key={id} dataKey={renderEntityName(id)} fill={COLORS[index % COLORS.length]} name={renderEntityName(id)} />
                ))}
              </BarChart></ResponsiveContainer></Paper></Grid>
          )}
        </Grid>

        {/* --- DATA GRID --- */}
        <Paper sx={{ height: 600, width: '100%' }}>
          <Typography variant="h6" sx={{ p: 2 }}>{t('detailedComparisonData')}</Typography>
          <DataGrid
            rows={comparisonData}
            columns={columns}
            loading={loading}
            slots={{ toolbar: GridToolbar }}
            slotProps={{
              toolbar: {
                showQuickFilter: true,
                quickFilterProps: { debounceMs: 500 },
                csvOptions: { disableToolbarButton: false },
                printOptions: { disableToolbarButton: false },
                // pdfExportOptions: { disableToolbarButton: false }, // This might be for Pro/Premium
              },
            }}
            initialState={{
              pagination: {
                paginationModel: { pageSize: 100, page: 0 },
              },
            }}
            pageSizeOptions={[10, 50, 100]}
            checkboxSelection
            disableRowSelectionOnClick
          />
        </Paper>
      </Box>
      <Button variant="outlined" onClick={() => window.print()} sx={{ mt: 2 }}>
        {t('printReport')}
      </Button>
      <Snackbar
        open={snackbar?.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert onClose={() => setSnackbar(null)} severity={snackbar?.severity} sx={{ width: '100%' }}>
          {snackbar?.message}
        </Alert>
      </Snackbar>
    </LocalizationProvider>
  );
};

export default SpecificDrugComparisonPage;