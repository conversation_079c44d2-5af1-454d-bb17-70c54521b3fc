import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
    Box, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, 
    Typography, TextField, Select, MenuItem, FormControl, InputLabel, CircularProgress, 
    IconButton 
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';

const API_URL = 'http://127.0.0.1:8000';

interface Clinic {
    id: number;
    name: string;
}

interface Drug {
    id: number;
    name: string;
}

interface DispensingItem {
    drug_id: number;
    quantity_dispensed: number;
    price_per_unit: number;
    beneficiary_count: number;
    total_cost?: number; // Optional, calculated on frontend for display
}

const DispensingEntry = () => {
    const [clinics, setClinics] = useState<Clinic[]>([]);
    const [drugs, setDrugs] = useState<Drug[]>([]);
    const [selectedClinic, setSelectedClinic] = useState<number | ''>('');
    const [dispensingMonth, setDispensingMonth] = useState('');
    const [dispensingItems, setDispensingItems] = useState<DispensingItem[]>([
        { drug_id: '', quantity_dispensed: '', price_per_unit: '', beneficiary_count: '' } as any
    ]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchInitialData();
    }, []);

    const fetchInitialData = async () => {
        setLoading(true);
        try {
            const [clinicsRes, drugsRes] = await Promise.all([
                axios.get<Clinic[]>(`${API_URL}/clinics`),
                axios.get<Drug[]>(`${API_URL}/drugs`)
            ]);
            setClinics(clinicsRes.data);
            setDrugs(drugsRes.data);

            // Set default month to current month (YYYY-MM)
            const today = new Date();
            const month = (today.getMonth() + 1).toString().padStart(2, '0');
            const year = today.getFullYear();
            setDispensingMonth(`${year}-${month}`);

        } catch (error) {
            console.error('Error fetching initial data:', error);
        }
        setLoading(false);
    };

    const handleItemChange = (index: number, field: keyof DispensingItem, value: any) => {
        const updatedItems = [...dispensingItems];
        updatedItems[index] = { ...updatedItems[index], [field]: value };

        // Calculate total_cost if quantity and price are available
        if (field === 'quantity_dispensed' || field === 'price_per_unit') {
            const qty = updatedItems[index].quantity_dispensed;
            const price = updatedItems[index].price_per_unit;
            if (typeof qty === 'number' && typeof price === 'number') {
                updatedItems[index].total_cost = qty * price;
            } else {
                updatedItems[index].total_cost = undefined;
            }
        }
        setDispensingItems(updatedItems);
    };

    const handleAddItem = () => {
        setDispensingItems([...dispensingItems, { drug_id: '', quantity_dispensed: '', price_per_unit: '', beneficiary_count: '' } as any]);
    };

    const handleRemoveItem = (index: number) => {
        const updatedItems = dispensingItems.filter((_, i) => i !== index);
        setDispensingItems(updatedItems);
    };

    const handleSubmit = async () => {
        if (!selectedClinic || !dispensingMonth) {
            alert('الرجاء اختيار العيادة والشهر.');
            return;
        }

        for (const item of dispensingItems) {
            if (!item.drug_id || !item.quantity_dispensed || !item.price_per_unit || !item.beneficiary_count) {
                alert('الرجاء إدخال جميع بيانات الأدوية المصروفة.');
                return;
            }
        }

        try {
            for (const item of dispensingItems) {
                await axios.post(`${API_URL}/dispensing-records`, {
                    clinic_id: selectedClinic,
                    dispensing_month: dispensingMonth,
                    drug_id: item.drug_id,
                    quantity_dispensed: item.quantity_dispensed,
                    price_per_unit: item.price_per_unit,
                    beneficiary_count: item.beneficiary_count,
                    dispensing_type: "monthly" // Default to monthly for now
                });
            }
            alert('تم تسجيل بيانات الصرف بنجاح!');
            // Reset form
            setSelectedClinic('');
            setDispensingItems([
                { drug_id: '', quantity_dispensed: '', price_per_unit: '', beneficiary_count: '' } as any
            ]);
        } catch (error) {
            console.error('Error submitting dispensing records:', error);
            alert('حدث خطأ أثناء تسجيل البيانات.');
        }
    };

    if (loading) {
        return <CircularProgress />;
    }

    return (
        <Box sx={{ width: '100%', p: 3 }}>
            <Typography variant="h5" gutterBottom>
                تسجيل بيانات الصرف الشهري
            </Typography>

            <FormControl fullWidth variant="outlined" sx={{ mb: 3 }}>
                <InputLabel>العيادة</InputLabel>
                <Select
                    value={selectedClinic}
                    onChange={(e) => setSelectedClinic(e.target.value as number)}
                    label="العيادة"
                >
                    {clinics.map((clinic) => (
                        <MenuItem key={clinic.id} value={clinic.id}>
                            {clinic.name}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>

            <TextField
                fullWidth
                label="شهر الصرف (YYYY-MM)"
                variant="outlined"
                value={dispensingMonth}
                onChange={(e) => setDispensingMonth(e.target.value)}
                sx={{ mb: 3 }}
            />

            <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
                الأدوية المصروفة
            </Typography>
            <TableContainer component={Paper} sx={{ mb: 3 }}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>الدواء</TableCell>
                            <TableCell>الكمية</TableCell>
                            <TableCell>السعر للوحدة</TableCell>
                            <TableCell>عدد الحالات</TableCell>
                            <TableCell>التكلفة الإجمالية</TableCell>
                            <TableCell></TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {dispensingItems.map((item, index) => (
                            <TableRow key={index}>
                                <TableCell>
                                    <FormControl fullWidth variant="standard">
                                        <Select
                                            value={item.drug_id}
                                            onChange={(e) => handleItemChange(index, 'drug_id', e.target.value as number)}
                                        >
                                            {drugs.map((drug) => (
                                                <MenuItem key={drug.id} value={drug.id}>
                                                    {drug.name}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </TableCell>
                                <TableCell>
                                    <TextField
                                        type="number"
                                        value={item.quantity_dispensed}
                                        onChange={(e) => handleItemChange(index, 'quantity_dispensed', parseFloat(e.target.value))}
                                        inputProps={{ min: 0 }}
                                        variant="standard"
                                    />
                                </TableCell>
                                <TableCell>
                                    <TextField
                                        type="number"
                                        value={item.price_per_unit}
                                        onChange={(e) => handleItemChange(index, 'price_per_unit', parseFloat(e.target.value))}
                                        inputProps={{ min: 0, step: "0.01" }}
                                        variant="standard"
                                    />
                                </TableCell>
                                <TableCell>
                                    <TextField
                                        type="number"
                                        value={item.beneficiary_count}
                                        onChange={(e) => handleItemChange(index, 'beneficiary_count', parseInt(e.target.value))}
                                        inputProps={{ min: 0 }}
                                        variant="standard"
                                    />
                                </TableCell>
                                <TableCell>
                                    <Typography>{item.total_cost !== undefined ? item.total_cost.toFixed(2) : '---'}</Typography>
                                </TableCell>
                                <TableCell>
                                    <IconButton onClick={() => handleRemoveItem(index)} color="error">
                                        <DeleteIcon />
                                    </IconButton>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            <Button 
                variant="outlined" 
                startIcon={<AddIcon />} 
                onClick={handleAddItem} 
                sx={{ mb: 3 }}
            >
                إضافة دواء آخر
            </Button>

            <Button 
                variant="contained" 
                color="primary" 
                fullWidth 
                onClick={handleSubmit}
            >
                تسجيل الصرف
            </Button>
        </Box>
    );
};

export default DispensingEntry;
