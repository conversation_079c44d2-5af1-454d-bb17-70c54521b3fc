{"ast": null, "code": "var _jsxFileName = \"E:\\\\Python\\\\cmder\\\\drug_dispensing_app\\\\frontend\\\\src\\\\pages\\\\ComparisonReportsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport axios from 'axios';\nimport { Box, Typography, FormControl, InputLabel, Select, MenuItem, Snackbar, Alert, Button, TextField, OutlinedInput, Checkbox, ListItemText, Grid, Paper, Card, CardContent } from '@mui/material';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport { BarChart, Bar, XAxis, YA<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';\nimport { DataGrid, GridToolbar } from '@mui/x-data-grid';\nimport dayjs from 'dayjs';\nimport * as XLSX from 'xlsx';\n\n// Interfaces\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComparisonReportsPage = () => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const [branches, setBranches] = useState([]);\n  const [regions, setRegions] = useState([]);\n  const [clinics, setClinics] = useState([]);\n  const [categories, setCategories] = useState([]);\n\n  // Filters\n  const [compareEntityType, setCompareEntityType] = useState('clinic');\n  const [selectedIds, setSelectedIds] = useState([]);\n  const [filterCategoryId, setFilterCategoryId] = useState('');\n  const [filterStartDate, setFilterStartDate] = useState(dayjs().subtract(1, 'month'));\n  const [filterEndDate, setFilterEndDate] = useState(dayjs());\n\n  // Data\n  const [comparisonData, setComparisonData] = useState([]);\n  const [trendData, setTrendData] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [snackbar, setSnackbar] = useState(null);\n\n  // Fetch initial data\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const [branchesRes, regionsRes, clinicsRes, categoriesRes] = await Promise.all([axios.get('http://localhost:8000/branches/'), axios.get('http://localhost:8000/regions/'), axios.get('http://localhost:8000/clinics/'), axios.get('http://localhost:8000/drug-categories/')]);\n        setBranches(branchesRes.data);\n        setRegions(regionsRes.data);\n        setClinics(clinicsRes.data);\n        setCategories(categoriesRes.data);\n      } catch (error) {\n        console.error('Error fetching initial data:', error);\n        setSnackbar({\n          open: true,\n          message: t('fetchDataError'),\n          severity: 'error'\n        });\n      }\n    };\n    fetchData();\n  }, [t]);\n\n  // Fetch comparison and trend data when filters change\n  const fetchAllData = async () => {\n    if (selectedIds.length === 0) {\n      setComparisonData([]);\n      setTrendData([]);\n      return;\n    }\n    setLoading(true);\n    try {\n      const [comparisonRes, trendRes] = await Promise.all([fetchComparisonData(), fetchTrendData()]);\n      // Process and set data\n      const processedData = processComparisonData(comparisonRes || []);\n      setComparisonData(processedData);\n      setTrendData(trendRes || []);\n    } catch (error) {\n      console.error('Error fetching report data:', error);\n      setSnackbar({\n        open: true,\n        message: t('fetchDataError'),\n        severity: 'error'\n      });\n      setComparisonData([]);\n      setTrendData([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchComparisonData = async () => {\n    const url = `http://localhost:8000/reports/comparison/${compareEntityType === 'branch' ? 'branches' : compareEntityType + 's'}`;\n    const idsParam = `${compareEntityType}_ids=${selectedIds.join(',')}`;\n    const startDateParam = filterStartDate ? `start_month=${filterStartDate.format('YYYY-MM')}` : '';\n    const endDateParam = filterEndDate ? `end_month=${filterEndDate.format('YYYY-MM')}` : '';\n    const categoryParam = filterCategoryId ? `category_id=${filterCategoryId}` : '';\n    const queryParams = [idsParam, startDateParam, endDateParam, categoryParam].filter(Boolean).join('&');\n    const fullUrl = `${url}?${queryParams}`;\n    const response = await axios.get(fullUrl);\n    return response.data;\n  };\n  const fetchTrendData = async () => {\n    const url = 'http://localhost:8000/reports/comparison/trends';\n    const entityTypeParam = `entity_type=${compareEntityType}`;\n    const entityIdsParam = `entity_ids=${selectedIds.join(',')}`;\n    const startDateParam = filterStartDate ? `start_month=${filterStartDate.format('YYYY-MM')}` : '';\n    const endDateParam = filterEndDate ? `end_month=${filterEndDate.format('YYYY-MM')}` : '';\n    const categoryParam = filterCategoryId ? `category_id=${filterCategoryId}` : '';\n    const queryParams = [entityTypeParam, entityIdsParam, startDateParam, endDateParam, categoryParam].filter(Boolean).join('&');\n    const fullUrl = `${url}?${queryParams}`;\n    const response = await axios.get(fullUrl);\n    return response.data.map((item, index) => ({\n      ...item,\n      id: item.Month || index.toString()\n    }));\n  };\n  const processComparisonData = data => {\n    return data.map((item, index) => ({\n      ...item,\n      id: `${item.EntityID}-${item.DrugID}-${index}`\n    }));\n  };\n  const handleResetFilters = () => {\n    setSelectedIds([]);\n    setFilterCategoryId('');\n    setFilterStartDate(dayjs().subtract(1, 'year'));\n    setFilterEndDate(dayjs());\n  };\n  const kpiData = useMemo(() => {\n    return comparisonData.reduce((acc, item) => {\n      acc.totalCost += item.TotalCost;\n      acc.totalQuantity += item.TotalQuantity;\n      acc.totalCases += item.NumberOfCases;\n      return acc;\n    }, {\n      totalCost: 0,\n      totalQuantity: 0,\n      totalCases: 0\n    });\n  }, [comparisonData]);\n  const categoryDistribution = useMemo(() => {\n    const data = comparisonData.reduce((acc, item) => {\n      const category = item.CategoryName;\n      if (!acc[category]) {\n        acc[category] = {\n          name: category,\n          value: 0\n        };\n      }\n      acc[category].value += item.TotalCost;\n      return acc;\n    }, {});\n    return Object.values(data);\n  }, [comparisonData]);\n  const entityComparisonData = useMemo(() => {\n    const data = comparisonData.reduce((acc, item) => {\n      const entity = item.EntityName;\n      if (!acc[entity]) {\n        acc[entity] = {\n          name: entity,\n          TotalCost: 0,\n          TotalQuantity: 0,\n          NumberOfCases: 0\n        };\n      }\n      acc[entity].TotalCost += item.TotalCost;\n      acc[entity].TotalQuantity += item.TotalQuantity;\n      acc[entity].NumberOfCases += item.NumberOfCases;\n      return acc;\n    }, {});\n    return Object.values(data);\n  }, [comparisonData]);\n  const renderEntitySelection = () => {\n    const items = compareEntityType === 'clinic' ? clinics : compareEntityType === 'region' ? regions : branches;\n    const label = t(`select${compareEntityType.charAt(0).toUpperCase() + compareEntityType.slice(1)}s`);\n    return /*#__PURE__*/_jsxDEV(FormControl, {\n      sx: {\n        minWidth: 240,\n        maxWidth: 400\n      },\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        multiple: true,\n        value: selectedIds,\n        onChange: e => setSelectedIds(e.target.value),\n        input: /*#__PURE__*/_jsxDEV(OutlinedInput, {\n          label: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 18\n        }, this),\n        renderValue: selected => selected.map(id => {\n          var _items$find;\n          return (_items$find = items.find(i => i.id === id)) === null || _items$find === void 0 ? void 0 : _items$find.name;\n        }).join(', '),\n        children: items.map(item => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: item.id,\n          children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n            checked: selectedIds.indexOf(item.id) > -1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this);\n  };\n  const columns = [{\n    field: 'EntityName',\n    headerName: t('entityName'),\n    width: 180\n  }, {\n    field: 'CategoryName',\n    headerName: t('category'),\n    width: 150\n  }, {\n    field: 'DrugName',\n    headerName: t('drugName'),\n    width: 200\n  }, {\n    field: 'TotalQuantity',\n    headerName: t('totalQuantity'),\n    type: 'number',\n    width: 130\n  }, {\n    field: 'TotalCost',\n    headerName: t('totalCost'),\n    type: 'number',\n    width: 130,\n    valueFormatter: params => params.value.toFixed(2)\n  }, {\n    field: 'NumberOfCases',\n    headerName: t('numberOfCases'),\n    type: 'number',\n    width: 130\n  }];\n  const trendColumns = [{\n    field: 'Month',\n    headerName: t('month'),\n    width: 150\n  }, {\n    field: 'TotalQuantity',\n    headerName: t('totalQuantity'),\n    type: 'number',\n    width: 150\n  }, {\n    field: 'TotalCost',\n    headerName: t('totalCost'),\n    type: 'number',\n    width: 150,\n    valueFormatter: params => params.value.toFixed(2)\n  }, {\n    field: 'NumberOfCases',\n    headerName: t('numberOfCases'),\n    type: 'number',\n    width: 150\n  }];\n  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#AF19FF', '#FF1919'];\n  const handleExportPdf = async () => {\n    if (comparisonData.length === 0) {\n      setSnackbar({\n        open: true,\n        message: t('noDataToExport'),\n        severity: 'error'\n      });\n      return;\n    }\n    try {\n      var _link$parentNode;\n      const reportTitle = t('comparisonDashboard');\n      const dataToExport = comparisonData.map(item => ({\n        'Entity Name': item.EntityName,\n        'Category': item.CategoryName,\n        'Drug Name': item.DrugName,\n        'Total Quantity': item.TotalQuantity,\n        'Total Cost': item.TotalCost,\n        'Number of Cases': item.NumberOfCases\n      }));\n      console.log('Data to export:', dataToExport); // Add this line for debugging\n\n      const response = await axios.post('http://localhost:8000/generate-pdf-report', dataToExport,\n      // Send the array directly\n      {\n        responseType: 'blob',\n        // Important for receiving binary data\n        params: {\n          title: reportTitle\n        } // Send title as a query parameter\n      });\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', 'comparison-report.pdf');\n      document.body.appendChild(link);\n      link.click();\n      (_link$parentNode = link.parentNode) === null || _link$parentNode === void 0 ? void 0 : _link$parentNode.removeChild(link);\n      setSnackbar({\n        open: true,\n        message: t('pdfExportSuccess'),\n        severity: 'success'\n      });\n    } catch (error) {\n      console.error('Error exporting PDF:', error);\n      setSnackbar({\n        open: true,\n        message: t('pdfExportError'),\n        severity: 'error'\n      });\n    }\n  };\n  const handleExportExcel = () => {\n    if (comparisonData.length === 0) {\n      setSnackbar({\n        open: true,\n        message: t('noDataToExport'),\n        severity: 'error'\n      });\n      return;\n    }\n    const dataToExport = comparisonData.map(row => ({\n      [t('entityName')]: row.EntityName,\n      [t('categoryName')]: row.CategoryName,\n      [t('drugName')]: row.DrugName,\n      [t('totalQuantity')]: row.TotalQuantity,\n      [t('totalCost')]: row.TotalCost,\n      [t('numberOfCases')]: row.NumberOfCases\n    }));\n\n    // Create worksheet with title\n    const reportTitle = t('comparisonDashboard');\n    const aoa = [];\n    aoa.push([reportTitle]); // Main title\n    aoa.push([]); // Spacer row\n\n    // Add headers\n    const headers = Object.keys(dataToExport[0]);\n    aoa.push(headers);\n\n    // Add data rows\n    dataToExport.forEach(row => {\n      aoa.push(Object.values(row));\n    });\n    const ws = XLSX.utils.aoa_to_sheet(aoa);\n    const wb = XLSX.utils.book_new();\n\n    // Calculate column widths\n    const colWidths = headers.map(header => {\n      let maxWidth = header.length;\n      dataToExport.forEach(row => {\n        const cellValue = String(row[header]);\n        maxWidth = Math.max(maxWidth, cellValue.length);\n      });\n      return {\n        wch: Math.min(maxWidth + 2, 50)\n      }; // Add padding and cap at 50\n    });\n    ws['!cols'] = colWidths;\n\n    // Style main title (first row)\n    const mainTitleCell = XLSX.utils.encode_cell({\n      r: 0,\n      c: 0\n    });\n    if (!ws[mainTitleCell]) ws[mainTitleCell] = {\n      v: reportTitle\n    };\n    ws[mainTitleCell].s = {\n      font: {\n        bold: true,\n        size: 16,\n        color: {\n          rgb: \"FF000080\"\n        }\n      },\n      alignment: {\n        horizontal: \"center\",\n        vertical: \"center\"\n      },\n      fill: {\n        fgColor: {\n          rgb: \"FFE6F3FF\"\n        }\n      },\n      // Light blue background\n      border: {\n        top: {\n          style: \"medium\",\n          color: {\n            rgb: \"FF000080\"\n          }\n        },\n        bottom: {\n          style: \"medium\",\n          color: {\n            rgb: \"FF000080\"\n          }\n        },\n        left: {\n          style: \"medium\",\n          color: {\n            rgb: \"FF000080\"\n          }\n        },\n        right: {\n          style: \"medium\",\n          color: {\n            rgb: \"FF000080\"\n          }\n        }\n      }\n    };\n\n    // Merge title cell across all columns\n    const titleMerge = {\n      s: {\n        r: 0,\n        c: 0\n      },\n      e: {\n        r: 0,\n        c: headers.length - 1\n      }\n    };\n    if (!ws['!merges']) ws['!merges'] = [];\n    ws['!merges'].push(titleMerge);\n\n    // Style headers (row 2, index 2)\n    const headerRowIndex = 2;\n    for (let c = 0; c < headers.length; c++) {\n      const cellAddress = XLSX.utils.encode_cell({\n        r: headerRowIndex,\n        c: c\n      });\n      if (!ws[cellAddress]) ws[cellAddress] = {\n        v: headers[c]\n      };\n      ws[cellAddress].s = {\n        font: {\n          bold: true,\n          size: 11,\n          color: {\n            rgb: \"FFFFFFFF\"\n          }\n        },\n        // White text\n        alignment: {\n          horizontal: \"center\",\n          vertical: \"center\"\n        },\n        fill: {\n          fgColor: {\n            rgb: \"FF4472C4\"\n          }\n        },\n        // Blue background\n        border: {\n          top: {\n            style: \"medium\",\n            color: {\n              rgb: \"FF2E4057\"\n            }\n          },\n          bottom: {\n            style: \"medium\",\n            color: {\n              rgb: \"FF2E4057\"\n            }\n          },\n          left: {\n            style: \"medium\",\n            color: {\n              rgb: \"FF2E4057\"\n            }\n          },\n          right: {\n            style: \"medium\",\n            color: {\n              rgb: \"FF2E4057\"\n            }\n          }\n        }\n      };\n    }\n\n    // Style data rows (starting from row 3, index 3)\n    for (let r = 3; r < aoa.length; r++) {\n      for (let c = 0; c < headers.length; c++) {\n        const cellAddress = XLSX.utils.encode_cell({\n          r: r,\n          c: c\n        });\n        if (!ws[cellAddress]) ws[cellAddress] = {\n          v: aoa[r][c]\n        };\n\n        // Alternate row colors for better readability\n        const isEvenRow = (r - 3) % 2 === 0;\n        ws[cellAddress].s = {\n          font: {\n            size: 10\n          },\n          alignment: {\n            horizontal: \"center\",\n            vertical: \"center\"\n          },\n          fill: {\n            fgColor: {\n              rgb: isEvenRow ? \"FFF8F9FA\" : \"FFFFFFFF\"\n            }\n          },\n          // Alternating light gray and white\n          border: {\n            top: {\n              style: \"thin\",\n              color: {\n                rgb: \"FFD0D0D0\"\n              }\n            },\n            bottom: {\n              style: \"thin\",\n              color: {\n                rgb: \"FFD0D0D0\"\n              }\n            },\n            left: {\n              style: \"thin\",\n              color: {\n                rgb: \"FFD0D0D0\"\n              }\n            },\n            right: {\n              style: \"thin\",\n              color: {\n                rgb: \"FFD0D0D0\"\n              }\n            }\n          }\n        };\n      }\n    }\n    XLSX.utils.book_append_sheet(wb, ws, \"تقرير المقارنة\");\n    XLSX.writeFile(wb, \"تقرير_المقارنة.xlsx\", {\n      cellStyles: true\n    });\n    setSnackbar({\n      open: true,\n      message: t('excelExportSuccess'),\n      severity: 'success'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n    dateAdapter: AdapterDayjs,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      id: \"comparison-report-content\",\n      children: [\" \", /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: t('comparisonDashboard')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: t('compareBy')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 69\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: compareEntityType,\n                label: t('compareBy'),\n                onChange: e => {\n                  setCompareEntityType(e.target.value);\n                  setSelectedIds([]);\n                },\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"clinic\",\n                  children: t('clinics')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"region\",\n                  children: t('regions')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"branch\",\n                  children: t('branches')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 110\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: renderEntitySelection()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: t('filterByCategory')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 69\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filterCategoryId,\n                label: t('filterByCategory'),\n                onChange: e => setFilterCategoryId(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: t('allCategories')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 15\n                }, this), categories.map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: cat.CategoryID,\n                  children: cat.CategoryName\n                }, cat.CategoryID, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 40\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 117\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(DatePicker, {\n              label: t('filterByStartMonth'),\n              views: ['year', 'month'],\n              openTo: \"month\",\n              value: filterStartDate,\n              onChange: setFilterStartDate,\n              slots: {\n                textField: TextField\n              },\n              slotProps: {\n                textField: {\n                  fullWidth: true\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(DatePicker, {\n              label: t('filterByEndMonth'),\n              views: ['year', 'month'],\n              openTo: \"month\",\n              value: filterEndDate,\n              onChange: setFilterEndDate,\n              slots: {\n                textField: TextField\n              },\n              slotProps: {\n                textField: {\n                  fullWidth: true\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: fetchAllData,\n              disabled: loading || selectedIds.length === 0,\n              children: t('applyFilters')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"secondary\",\n              onClick: handleResetFilters,\n              children: t('resetFilters')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              onClick: handleExportPdf,\n              children: t('exportToPdf')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"info\",\n              onClick: handleExportExcel,\n              children: t('exportToExcel')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: t('totalCost')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 65\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: kpiData.totalCost.toLocaleString(undefined, {\n                  minimumFractionDigits: 2,\n                  maximumFractionDigits: 2\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 141\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: t('totalQuantity')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 65\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: kpiData.totalQuantity.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 145\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: t('numberOfCases')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 65\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: kpiData.totalCases.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 145\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: t('avgCostPerCase')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 65\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: (kpiData.totalCases > 0 ? kpiData.totalCost / kpiData.totalCases : 0).toLocaleString(undefined, {\n                  minimumFractionDigits: 2,\n                  maximumFractionDigits: 2\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 146\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: 400\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: t('monthlyTrends')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 71\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: trendData,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 177\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"Month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 216\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 241\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 250\n                }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 261\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"TotalCost\",\n                  fill: \"#8884d8\",\n                  name: t('totalCost')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 271\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"TotalQuantity\",\n                  fill: \"#82ca9d\",\n                  name: t('totalQuantity')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 335\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 150\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 129\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 37\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: 400\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: t('costByCategory')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 71\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              children: /*#__PURE__*/_jsxDEV(PieChart, {\n                children: [/*#__PURE__*/_jsxDEV(Pie, {\n                  data: categoryDistribution,\n                  dataKey: \"value\",\n                  nameKey: \"name\",\n                  cx: \"50%\",\n                  cy: \"50%\",\n                  outerRadius: 100,\n                  fill: \"#8884d8\",\n                  label: true,\n                  children: categoryDistribution.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                    fill: COLORS[index % COLORS.length]\n                  }, `cell-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 326\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 161\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 401\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 151\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 130\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 37\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: 400\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: t('entityComparison')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 64\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: entityComparisonData,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 184\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 223\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 247\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 256\n                }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 267\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"TotalCost\",\n                  fill: \"#82ca9d\",\n                  name: t('totalCost')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 277\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"TotalQuantity\",\n                  fill: \"#8884d8\",\n                  name: t('totalQuantity')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 341\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 146\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 125\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 30\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          height: 600,\n          width: '100%',\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            p: 2\n          },\n          children: t('detailedComparisonData')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: comparisonData,\n          columns: columns,\n          loading: loading,\n          slots: {\n            toolbar: GridToolbar\n          },\n          slotProps: {\n            toolbar: {\n              showQuickFilter: true,\n              quickFilterProps: {\n                debounceMs: 500\n              },\n              csvOptions: {\n                disableToolbarButton: false\n              },\n              printOptions: {\n                disableToolbarButton: false\n              }\n            }\n          },\n          initialState: {\n            pagination: {\n              paginationModel: {\n                pageSize: 100,\n                page: 0\n              }\n            }\n          },\n          pageSizeOptions: [10, 50, 100],\n          checkboxSelection: true,\n          disableRowSelectionOnClick: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          height: 400,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            p: 2\n          },\n          children: t('monthlyTrendData')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: trendData,\n          columns: trendColumns,\n          loading: loading,\n          slots: {\n            toolbar: GridToolbar\n          },\n          slotProps: {\n            toolbar: {\n              showQuickFilter: true,\n              quickFilterProps: {\n                debounceMs: 500\n              },\n              csvOptions: {\n                disableToolbarButton: false\n              },\n              printOptions: {\n                disableToolbarButton: false\n              }\n            }\n          },\n          initialState: {\n            pagination: {\n              paginationModel: {\n                pageSize: 50,\n                page: 0\n              }\n            }\n          },\n          pageSizeOptions: [10, 25, 50],\n          disableRowSelectionOnClick: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), snackbar && /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: snackbar.open,\n        autoHideDuration: 6000,\n        onClose: () => setSnackbar(null),\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: () => setSnackbar(null),\n          severity: snackbar.severity,\n          sx: {\n            width: '100%'\n          },\n          children: snackbar.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 171\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 22\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 391,\n    columnNumber: 5\n  }, this);\n};\n_s(ComparisonReportsPage, \"585UxiXjhjHhvLJleRwL53BK8t4=\", false, function () {\n  return [useTranslation];\n});\n_c = ComparisonReportsPage;\nexport default ComparisonReportsPage;\nvar _c;\n$RefreshReg$(_c, \"ComparisonReportsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useTranslation", "axios", "Box", "Typography", "FormControl", "InputLabel", "Select", "MenuItem", "Snackbar", "<PERSON><PERSON>", "<PERSON><PERSON>", "TextField", "OutlinedInput", "Checkbox", "ListItemText", "Grid", "Paper", "Card", "<PERSON><PERSON><PERSON><PERSON>", "DatePicker", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LocalizationProvider", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "DataGrid", "GridToolbar", "dayjs", "XLSX", "jsxDEV", "_jsxDEV", "ComparisonReportsPage", "_s", "t", "branches", "setBranches", "regions", "setRegions", "clinics", "setClinics", "categories", "setCategories", "compareEntityType", "setCompareEntityType", "selectedIds", "setSelectedIds", "filterCategoryId", "setFilterCategoryId", "filterStartDate", "setFilterStartDate", "subtract", "filterEndDate", "setFilterEndDate", "comparisonData", "setComparisonData", "trendData", "setTrendData", "loading", "setLoading", "snackbar", "setSnackbar", "fetchData", "branchesRes", "regionsRes", "clinicsRes", "categoriesRes", "Promise", "all", "get", "data", "error", "console", "open", "message", "severity", "fetchAllData", "length", "comparisonRes", "trendRes", "fetchComparisonData", "fetchTrendData", "processedData", "processComparisonData", "url", "idsParam", "join", "startDateParam", "format", "endDateParam", "categoryParam", "queryParams", "filter", "Boolean", "fullUrl", "response", "entityTypeParam", "entityIdsParam", "map", "item", "index", "id", "Month", "toString", "EntityID", "DrugID", "handleResetFilters", "kpiData", "reduce", "acc", "totalCost", "TotalCost", "totalQuantity", "TotalQuantity", "totalCases", "NumberOfCases", "categoryDistribution", "category", "CategoryName", "name", "value", "Object", "values", "entityComparisonData", "entity", "EntityName", "renderEntitySelection", "items", "label", "char<PERSON>t", "toUpperCase", "slice", "sx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "multiple", "onChange", "e", "target", "input", "renderValue", "selected", "_items$find", "find", "i", "checked", "indexOf", "primary", "columns", "field", "headerName", "width", "type", "valueFormatter", "params", "toFixed", "trendColumns", "COLORS", "handleExportPdf", "_link$parentNode", "reportTitle", "dataToExport", "DrugName", "log", "post", "responseType", "title", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "handleExportExcel", "row", "aoa", "push", "headers", "keys", "for<PERSON>ach", "ws", "utils", "aoa_to_sheet", "wb", "book_new", "col<PERSON><PERSON><PERSON>", "header", "cellValue", "String", "Math", "max", "wch", "min", "mainTitleCell", "encode_cell", "r", "c", "v", "s", "font", "bold", "size", "color", "rgb", "alignment", "horizontal", "vertical", "fill", "fgColor", "border", "top", "style", "bottom", "left", "right", "titleMerge", "headerRowIndex", "cellAddress", "isEvenRow", "book_append_sheet", "writeFile", "cellStyles", "dateAdapter", "p", "variant", "gutterBottom", "mb", "container", "spacing", "alignItems", "xs", "sm", "md", "fullWidth", "cat", "CategoryID", "views", "openTo", "slots", "textField", "slotProps", "onClick", "disabled", "toLocaleString", "undefined", "minimumFractionDigits", "maximumFractionDigits", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "<PERSON><PERSON><PERSON>", "cx", "cy", "outerRadius", "entry", "rows", "toolbar", "showQuickFilter", "quickFilterProps", "debounceMs", "csvOptions", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "printOptions", "initialState", "pagination", "paginationModel", "pageSize", "page", "pageSizeOptions", "checkboxSelection", "disableRowSelectionOnClick", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["E:/Python/cmder/drug_dispensing_app/frontend/src/pages/ComparisonReportsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useMemo } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport axios from 'axios';\nimport {\n  Box,\n  Typography,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Snackbar,\n  Alert,\n  Button,\n  TextField,\n  OutlinedInput,\n  Checkbox,\n  ListItemText,\n  Grid,\n  Paper,\n  Card,\n  CardContent,\n} from '@mui/material';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';\nimport { DataGrid, GridColDef, GridToolbar } from '@mui/x-data-grid';\nimport dayjs, { Dayjs } from 'dayjs';\nimport * as XLSX from 'xlsx';\n\n// Interfaces\ninterface Branch { id: number; name: string; }\ninterface Region { id: number; name: string; branch_id: number; }\ninterface Clinic { id: number; name: string; region_id: number; }\ninterface DrugCategory { CategoryID: number; CategoryName: string; }\ninterface Drug { DrugID: number; DrugName: string; CategoryID: number; Unit: string | null; }\ninterface ComparisonDispensedDrugData {\n  id: string; // Required for DataGrid\n  EntityID: number;\n  EntityName: string;\n  DrugID: number;\n  DrugName: string;\n  DrugUnit: string | null;\n  CategoryID: number;\n  CategoryName: string;\n  TotalQuantity: number;\n  TotalCost: number;\n  NumberOfCases: number;\n}\ninterface ComparisonTrendData {\n  Month: string;\n  TotalQuantity: number;\n  TotalCost: number;\n  NumberOfCases: number;\n}\n\nconst ComparisonReportsPage = () => {\n  const { t } = useTranslation();\n  const [branches, setBranches] = useState<Branch[]>([]);\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [clinics, setClinics] = useState<Clinic[]>([]);\n  const [categories, setCategories] = useState<DrugCategory[]>([]);\n\n  // Filters\n  const [compareEntityType, setCompareEntityType] = useState<'clinic' | 'region' | 'branch'>('clinic');\n  const [selectedIds, setSelectedIds] = useState<number[]>([]);\n  const [filterCategoryId, setFilterCategoryId] = useState<number | ''>('');\n  const [filterStartDate, setFilterStartDate] = useState<Dayjs | null>(dayjs().subtract(1, 'month'));\n  const [filterEndDate, setFilterEndDate] = useState<Dayjs | null>(dayjs());\n\n  // Data\n  const [comparisonData, setComparisonData] = useState<ComparisonDispensedDrugData[]>([]);\n  const [trendData, setTrendData] = useState<ComparisonTrendData[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);\n\n  // Fetch initial data\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const [branchesRes, regionsRes, clinicsRes, categoriesRes] = await Promise.all([\n          axios.get<Branch[]>('http://localhost:8000/branches/'),\n          axios.get<Region[]>('http://localhost:8000/regions/'),\n          axios.get<Clinic[]>('http://localhost:8000/clinics/'),\n          axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/')\n        ]);\n        setBranches(branchesRes.data);\n        setRegions(regionsRes.data);\n        setClinics(clinicsRes.data);\n        setCategories(categoriesRes.data);\n      } catch (error) {\n        console.error('Error fetching initial data:', error);\n        setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });\n      }\n    };\n    fetchData();\n  }, [t]);\n\n  // Fetch comparison and trend data when filters change\n  const fetchAllData = async () => {\n    if (selectedIds.length === 0) {\n      setComparisonData([]);\n      setTrendData([]);\n      return;\n    }\n    setLoading(true);\n    try {\n      const [comparisonRes, trendRes] = await Promise.all([\n        fetchComparisonData(),\n        fetchTrendData(),\n      ]);\n      // Process and set data\n      const processedData = processComparisonData(comparisonRes || []);\n      setComparisonData(processedData);\n      setTrendData(trendRes || []);\n\n    } catch (error) {\n      console.error('Error fetching report data:', error);\n      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });\n      setComparisonData([]);\n      setTrendData([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchComparisonData = async () => {\n    const url = `http://localhost:8000/reports/comparison/${compareEntityType === 'branch' ? 'branches' : compareEntityType + 's'}`;\n    const idsParam = `${compareEntityType}_ids=${selectedIds.join(',')}`;\n    const startDateParam = filterStartDate ? `start_month=${filterStartDate.format('YYYY-MM')}` : '';\n    const endDateParam = filterEndDate ? `end_month=${filterEndDate.format('YYYY-MM')}` : '';\n    const categoryParam = filterCategoryId ? `category_id=${filterCategoryId}` : '';\n    const queryParams = [idsParam, startDateParam, endDateParam, categoryParam].filter(Boolean).join('&');\n    const fullUrl = `${url}?${queryParams}`;\n    const response = await axios.get<ComparisonDispensedDrugData[]>(fullUrl);\n    return response.data;\n  };\n\n  const fetchTrendData = async () => {\n    const url = 'http://localhost:8000/reports/comparison/trends';\n    const entityTypeParam = `entity_type=${compareEntityType}`;\n    const entityIdsParam = `entity_ids=${selectedIds.join(',')}`;\n    const startDateParam = filterStartDate ? `start_month=${filterStartDate.format('YYYY-MM')}` : '';\n    const endDateParam = filterEndDate ? `end_month=${filterEndDate.format('YYYY-MM')}` : '';\n    const categoryParam = filterCategoryId ? `category_id=${filterCategoryId}` : '';\n    const queryParams = [entityTypeParam, entityIdsParam, startDateParam, endDateParam, categoryParam].filter(Boolean).join('&');\n    const fullUrl = `${url}?${queryParams}`;\n    const response = await axios.get<ComparisonTrendData[]>(fullUrl);\n    return response.data.map((item, index) => ({ ...item, id: item.Month || index.toString() }));\n  };\n\n  const processComparisonData = (data: Omit<ComparisonDispensedDrugData, 'id'>[]): ComparisonDispensedDrugData[] => {\n    return data.map((item, index) => ({ ...item, id: `${item.EntityID}-${item.DrugID}-${index}` }));\n  };\n\n  const handleResetFilters = () => {\n    setSelectedIds([]);\n    setFilterCategoryId('');\n    setFilterStartDate(dayjs().subtract(1, 'year'));\n    setFilterEndDate(dayjs());\n  };\n\n  const kpiData = useMemo(() => {\n    return comparisonData.reduce((acc, item) => {\n      acc.totalCost += item.TotalCost;\n      acc.totalQuantity += item.TotalQuantity;\n      acc.totalCases += item.NumberOfCases;\n      return acc;\n    }, { totalCost: 0, totalQuantity: 0, totalCases: 0 });\n  }, [comparisonData]);\n\n  const categoryDistribution = useMemo(() => {\n    const data = comparisonData.reduce((acc, item) => {\n      const category = item.CategoryName;\n      if (!acc[category]) {\n        acc[category] = { name: category, value: 0 };\n      }\n      acc[category].value += item.TotalCost;\n      return acc;\n    }, {} as { [key: string]: { name: string, value: number } });\n    return Object.values(data);\n  }, [comparisonData]);\n\n  const entityComparisonData = useMemo(() => {\n    const data = comparisonData.reduce((acc, item) => {\n        const entity = item.EntityName;\n        if (!acc[entity]) {\n            acc[entity] = { name: entity, TotalCost: 0, TotalQuantity: 0, NumberOfCases: 0 };\n        }\n        acc[entity].TotalCost += item.TotalCost;\n        acc[entity].TotalQuantity += item.TotalQuantity;\n        acc[entity].NumberOfCases += item.NumberOfCases;\n        return acc;\n    }, {} as { [key: string]: { name: string, TotalCost: number, TotalQuantity: number, NumberOfCases: number } });\n    return Object.values(data);\n}, [comparisonData]);\n\n  const renderEntitySelection = () => {\n    const items = compareEntityType === 'clinic' ? clinics : compareEntityType === 'region' ? regions : branches;\n    const label = t(`select${compareEntityType.charAt(0).toUpperCase() + compareEntityType.slice(1)}s`);\n    return (\n      <FormControl sx={{ minWidth: 240, maxWidth: 400 }}>\n        <InputLabel>{label}</InputLabel>\n        <Select\n          multiple\n          value={selectedIds}\n          onChange={(e) => setSelectedIds(e.target.value as number[])}\n          input={<OutlinedInput label={label} />}\n          renderValue={(selected) => selected.map(id => items.find(i => i.id === id)?.name).join(', ')}\n        >\n          {items.map((item) => (\n            <MenuItem key={item.id} value={item.id}>\n              <Checkbox checked={selectedIds.indexOf(item.id) > -1} />\n              <ListItemText primary={item.name} />\n            </MenuItem>\n          ))}\n        </Select>\n      </FormControl>\n    );\n  };\n\n  const columns: GridColDef[] = [\n    { field: 'EntityName', headerName: t('entityName'), width: 180 },\n    { field: 'CategoryName', headerName: t('category'), width: 150 },\n    { field: 'DrugName', headerName: t('drugName'), width: 200 },\n    { field: 'TotalQuantity', headerName: t('totalQuantity'), type: 'number', width: 130 },\n    { field: 'TotalCost', headerName: t('totalCost'), type: 'number', width: 130, valueFormatter: (params: { value: number }) => params.value.toFixed(2) },\n    { field: 'NumberOfCases', headerName: t('numberOfCases'), type: 'number', width: 130 },\n  ];\n\n  const trendColumns: GridColDef[] = [\n    { field: 'Month', headerName: t('month'), width: 150 },\n    { field: 'TotalQuantity', headerName: t('totalQuantity'), type: 'number', width: 150 },\n    { field: 'TotalCost', headerName: t('totalCost'), type: 'number', width: 150, valueFormatter: (params: { value: number }) => params.value.toFixed(2) },\n    { field: 'NumberOfCases', headerName: t('numberOfCases'), type: 'number', width: 150 },\n  ];\n\n  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#AF19FF', '#FF1919'];\n\n  const handleExportPdf = async () => {\n    if (comparisonData.length === 0) {\n      setSnackbar({ open: true, message: t('noDataToExport'), severity: 'error' });\n      return;\n    }\n\n    try {\n      const reportTitle = t('comparisonDashboard');\n      const dataToExport = comparisonData.map(item => ({\n        'Entity Name': item.EntityName,\n        'Category': item.CategoryName,\n        'Drug Name': item.DrugName,\n        'Total Quantity': item.TotalQuantity,\n        'Total Cost': item.TotalCost,\n        'Number of Cases': item.NumberOfCases,\n      }));\n      console.log('Data to export:', dataToExport); // Add this line for debugging\n\n      const response = await axios.post(\n        'http://localhost:8000/generate-pdf-report',\n        dataToExport, // Send the array directly\n        {\n          responseType: 'blob', // Important for receiving binary data\n          params: { title: reportTitle } // Send title as a query parameter\n        }\n      );\n\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', 'comparison-report.pdf');\n      document.body.appendChild(link);\n      link.click();\n      link.parentNode?.removeChild(link);\n      setSnackbar({ open: true, message: t('pdfExportSuccess'), severity: 'success' });\n    } catch (error) {\n      console.error('Error exporting PDF:', error);\n      setSnackbar({ open: true, message: t('pdfExportError'), severity: 'error' });\n    }\n  };\n\n  const handleExportExcel = () => {\n    if (comparisonData.length === 0) {\n      setSnackbar({ open: true, message: t('noDataToExport'), severity: 'error' });\n      return;\n    }\n\n    const dataToExport = comparisonData.map(row => ({\n      [t('entityName')]: row.EntityName,\n      [t('categoryName')]: row.CategoryName,\n      [t('drugName')]: row.DrugName,\n      [t('totalQuantity')]: row.TotalQuantity,\n      [t('totalCost')]: row.TotalCost,\n      [t('numberOfCases')]: row.NumberOfCases,\n    }));\n\n    // Create worksheet with title\n    const reportTitle = t('comparisonDashboard');\n    const aoa: (string | number)[][] = [];\n    aoa.push([reportTitle]); // Main title\n    aoa.push([]); // Spacer row\n\n    // Add headers\n    const headers = Object.keys(dataToExport[0]);\n    aoa.push(headers);\n\n    // Add data rows\n    dataToExport.forEach(row => {\n      aoa.push(Object.values(row));\n    });\n\n    const ws = XLSX.utils.aoa_to_sheet(aoa);\n    const wb = XLSX.utils.book_new();\n\n    // Calculate column widths\n    const colWidths = headers.map(header => {\n      let maxWidth = header.length;\n      dataToExport.forEach(row => {\n        const cellValue = String(row[header]);\n        maxWidth = Math.max(maxWidth, cellValue.length);\n      });\n      return { wch: Math.min(maxWidth + 2, 50) }; // Add padding and cap at 50\n    });\n    ws['!cols'] = colWidths;\n\n    // Style main title (first row)\n    const mainTitleCell = XLSX.utils.encode_cell({ r: 0, c: 0 });\n    if (!ws[mainTitleCell]) ws[mainTitleCell] = { v: reportTitle };\n    ws[mainTitleCell].s = {\n      font: { bold: true, size: 16, color: { rgb: \"FF000080\" } },\n      alignment: { horizontal: \"center\", vertical: \"center\" },\n      fill: { fgColor: { rgb: \"FFE6F3FF\" } }, // Light blue background\n      border: {\n        top: { style: \"medium\", color: { rgb: \"FF000080\" } },\n        bottom: { style: \"medium\", color: { rgb: \"FF000080\" } },\n        left: { style: \"medium\", color: { rgb: \"FF000080\" } },\n        right: { style: \"medium\", color: { rgb: \"FF000080\" } }\n      }\n    };\n\n    // Merge title cell across all columns\n    const titleMerge = { s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } };\n    if (!ws['!merges']) ws['!merges'] = [];\n    ws['!merges'].push(titleMerge);\n\n    // Style headers (row 2, index 2)\n    const headerRowIndex = 2;\n    for (let c = 0; c < headers.length; c++) {\n      const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: c });\n      if (!ws[cellAddress]) ws[cellAddress] = { v: headers[c] };\n      ws[cellAddress].s = {\n        font: { bold: true, size: 11, color: { rgb: \"FFFFFFFF\" } }, // White text\n        alignment: { horizontal: \"center\", vertical: \"center\" },\n        fill: { fgColor: { rgb: \"FF4472C4\" } }, // Blue background\n        border: {\n          top: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n          bottom: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n          left: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n          right: { style: \"medium\", color: { rgb: \"FF2E4057\" } }\n        }\n      };\n    }\n\n    // Style data rows (starting from row 3, index 3)\n    for (let r = 3; r < aoa.length; r++) {\n      for (let c = 0; c < headers.length; c++) {\n        const cellAddress = XLSX.utils.encode_cell({ r: r, c: c });\n        if (!ws[cellAddress]) ws[cellAddress] = { v: aoa[r][c] };\n\n        // Alternate row colors for better readability\n        const isEvenRow = (r - 3) % 2 === 0;\n        ws[cellAddress].s = {\n          font: { size: 10 },\n          alignment: { horizontal: \"center\", vertical: \"center\" },\n          fill: { fgColor: { rgb: isEvenRow ? \"FFF8F9FA\" : \"FFFFFFFF\" } }, // Alternating light gray and white\n          border: {\n            top: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } },\n            bottom: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } },\n            left: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } },\n            right: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } }\n          }\n        };\n      }\n    }\n\n    XLSX.utils.book_append_sheet(wb, ws, \"تقرير المقارنة\");\n    XLSX.writeFile(wb, \"تقرير_المقارنة.xlsx\", { cellStyles: true });\n    setSnackbar({ open: true, message: t('excelExportSuccess'), severity: 'success' });\n  };\n\n  return (\n    <LocalizationProvider dateAdapter={AdapterDayjs}>\n      <Box sx={{ p: 3 }} id=\"comparison-report-content\"> {/* Added ID here */}\n        <Typography variant=\"h4\" gutterBottom>{t('comparisonDashboard')}</Typography>\n        \n        {/* --- FILTERS --- */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth><InputLabel>{t('compareBy')}</InputLabel><Select value={compareEntityType} label={t('compareBy')} onChange={(e) => { setCompareEntityType(e.target.value as any); setSelectedIds([]); }}>\n              <MenuItem value=\"clinic\">{t('clinics')}</MenuItem>\n              <MenuItem value=\"region\">{t('regions')}</MenuItem>\n              <MenuItem value=\"branch\">{t('branches')}</MenuItem>\n            </Select></FormControl></Grid>\n            <Grid item xs={12} sm={6} md={3}>{renderEntitySelection()}</Grid>\n            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth><InputLabel>{t('filterByCategory')}</InputLabel><Select value={filterCategoryId} label={t('filterByCategory')} onChange={(e) => setFilterCategoryId(e.target.value as number)}>\n              <MenuItem value=\"\">{t('allCategories')}</MenuItem>\n              {categories.map((cat) => <MenuItem key={cat.CategoryID} value={cat.CategoryID}>{cat.CategoryName}</MenuItem>)}\n            </Select></FormControl></Grid>\n            <Grid item xs={12} sm={6} md={3}><DatePicker label={t('filterByStartMonth')} views={['year', 'month']} openTo=\"month\" value={filterStartDate} onChange={setFilterStartDate} slots={{ textField: TextField }} slotProps={{ textField: { fullWidth: true } }} /></Grid>\n            <Grid item xs={12} sm={6} md={3}><DatePicker label={t('filterByEndMonth')} views={['year', 'month']} openTo=\"month\" value={filterEndDate} onChange={setFilterEndDate} slots={{ textField: TextField }} slotProps={{ textField: { fullWidth: true } }} /></Grid>\n            <Grid item xs={12} sm={6} md={3}><Button variant=\"contained\" color=\"primary\" onClick={fetchAllData} disabled={loading || selectedIds.length === 0}>{t('applyFilters')}</Button></Grid>\n            <Grid item xs={12} sm={6} md={3}><Button variant=\"outlined\" color=\"secondary\" onClick={handleResetFilters}>{t('resetFilters')}</Button></Grid>\n            <Grid item xs={12} sm={6} md={3}><Button variant=\"contained\" color=\"success\" onClick={handleExportPdf}>{t('exportToPdf')}</Button></Grid>\n            <Grid item xs={12} sm={6} md={3}><Button variant=\"contained\" color=\"info\" onClick={handleExportExcel}>{t('exportToExcel')}</Button></Grid>\n          </Grid>\n        </Paper>\n\n        {/* --- KPIs --- */}\n        <Grid container spacing={3} sx={{ mb: 3 }}>\n            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color=\"textSecondary\" gutterBottom>{t('totalCost')}</Typography><Typography variant=\"h5\">{kpiData.totalCost.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Typography></CardContent></Card></Grid>\n            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color=\"textSecondary\" gutterBottom>{t('totalQuantity')}</Typography><Typography variant=\"h5\">{kpiData.totalQuantity.toLocaleString()}</Typography></CardContent></Card></Grid>\n            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color=\"textSecondary\" gutterBottom>{t('numberOfCases')}</Typography><Typography variant=\"h5\">{kpiData.totalCases.toLocaleString()}</Typography></CardContent></Card></Grid>\n            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color=\"textSecondary\" gutterBottom>{t('avgCostPerCase')}</Typography><Typography variant=\"h5\">{(kpiData.totalCases > 0 ? kpiData.totalCost / kpiData.totalCases : 0).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Typography></CardContent></Card></Grid>\n        </Grid>\n\n        {/* --- CHARTS --- */}\n        <Grid container spacing={3} sx={{ mb: 3 }}>\n          <Grid item xs={12} md={8}><Paper sx={{ p: 2, height: 400 }}><Typography variant=\"h6\">{t('monthlyTrends')}</Typography><ResponsiveContainer><BarChart data={trendData}><CartesianGrid strokeDasharray=\"3 3\" /><XAxis dataKey=\"Month\" /><YAxis /><Tooltip /><Legend /><Bar dataKey=\"TotalCost\" fill=\"#8884d8\" name={t('totalCost')} /><Bar dataKey=\"TotalQuantity\" fill=\"#82ca9d\" name={t('totalQuantity')} /></BarChart></ResponsiveContainer></Paper></Grid>\n          <Grid item xs={12} md={4}><Paper sx={{ p: 2, height: 400 }}><Typography variant=\"h6\">{t('costByCategory')}</Typography><ResponsiveContainer><PieChart><Pie data={categoryDistribution} dataKey=\"value\" nameKey=\"name\" cx=\"50%\" cy=\"50%\" outerRadius={100} fill=\"#8884d8\" label>{categoryDistribution.map((entry, index) => <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />)}</Pie><Tooltip /></PieChart></ResponsiveContainer></Paper></Grid>\n          <Grid item xs={12}><Paper sx={{ p: 2, height: 400 }}><Typography variant=\"h6\">{t('entityComparison')}</Typography><ResponsiveContainer><BarChart data={entityComparisonData}><CartesianGrid strokeDasharray=\"3 3\" /><XAxis dataKey=\"name\" /><YAxis /><Tooltip /><Legend /><Bar dataKey=\"TotalCost\" fill=\"#82ca9d\" name={t('totalCost')} /><Bar dataKey=\"TotalQuantity\" fill=\"#8884d8\" name={t('totalQuantity')} /></BarChart></ResponsiveContainer></Paper></Grid>\n        </Grid>\n\n        {/* --- DATA GRID --- */}\n        <Paper sx={{ height: 600, width: '100%', mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ p: 2 }}>{t('detailedComparisonData')}</Typography>\n          <DataGrid\n            rows={comparisonData}\n            columns={columns}\n            loading={loading}\n            slots={{ toolbar: GridToolbar }}\n            slotProps={{\n              toolbar: {\n                showQuickFilter: true,\n                quickFilterProps: { debounceMs: 500 },\n                csvOptions: { disableToolbarButton: false },\n                printOptions: { disableToolbarButton: false },\n              },\n            }}\n            initialState={{\n              pagination: {\n                paginationModel: { pageSize: 100, page: 0 },\n              },\n            }}\n            pageSizeOptions={[10, 50, 100]}\n            checkboxSelection\n            disableRowSelectionOnClick\n          />\n        </Paper>\n\n        <Paper sx={{ height: 400, width: '100%' }}>\n          <Typography variant=\"h6\" sx={{ p: 2 }}>{t('monthlyTrendData')}</Typography>\n          <DataGrid\n            rows={trendData}\n            columns={trendColumns}\n            loading={loading}\n            slots={{ toolbar: GridToolbar }}\n            slotProps={{\n              toolbar: {\n                showQuickFilter: true,\n                quickFilterProps: { debounceMs: 500 },\n                csvOptions: { disableToolbarButton: false },\n                printOptions: { disableToolbarButton: false },\n              },\n            }}\n            initialState={{\n              pagination: {\n                paginationModel: { pageSize: 50, page: 0 },\n              },\n            }}\n            pageSizeOptions={[10, 25, 50]}\n            disableRowSelectionOnClick\n          />\n        </Paper>\n\n        {/* Removed the old print button */}\n        {/* <Button variant=\"outlined\" onClick={() => window.print()} sx={{ mt: 2 }}>\n          {t('printReport')}\n        </Button> */}\n\n        {snackbar && <Snackbar open={snackbar.open} autoHideDuration={6000} onClose={() => setSnackbar(null)} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}><Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>{snackbar.message}</Alert></Snackbar>}\n      </Box>\n    </LocalizationProvider>\n  );\n};\n\nexport default ComparisonReportsPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC3D,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,QAAQ,EACRC,YAAY,EACZC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,WAAW,QACN,eAAe;AACtB,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,EAAmBC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAQ,UAAU;AACjJ,SAASC,QAAQ,EAAcC,WAAW,QAAQ,kBAAkB;AACpE,OAAOC,KAAK,MAAiB,OAAO;AACpC,OAAO,KAAKC,IAAI,MAAM,MAAM;;AAE5B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AA0BA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAE,CAAC,GAAGzC,cAAc,CAAC,CAAC;EAC9B,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAW,EAAE,CAAC;EACtD,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAiB,EAAE,CAAC;;EAEhE;EACA,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAiC,QAAQ,CAAC;EACpG,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAW,EAAE,CAAC;EAC5D,MAAM,CAACyD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1D,QAAQ,CAAc,EAAE,CAAC;EACzE,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAesC,KAAK,CAAC,CAAC,CAACuB,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAClG,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAesC,KAAK,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAGjE,QAAQ,CAAgC,EAAE,CAAC;EACvF,MAAM,CAACkE,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAwB,EAAE,CAAC;EACrE,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsE,QAAQ,EAAEC,WAAW,CAAC,GAAGvE,QAAQ,CAA2E,IAAI,CAAC;;EAExH;EACAC,SAAS,CAAC,MAAM;IACd,MAAMuE,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAM,CAACC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC7E1E,KAAK,CAAC2E,GAAG,CAAW,iCAAiC,CAAC,EACtD3E,KAAK,CAAC2E,GAAG,CAAW,gCAAgC,CAAC,EACrD3E,KAAK,CAAC2E,GAAG,CAAW,gCAAgC,CAAC,EACrD3E,KAAK,CAAC2E,GAAG,CAAiB,wCAAwC,CAAC,CACpE,CAAC;QACFjC,WAAW,CAAC2B,WAAW,CAACO,IAAI,CAAC;QAC7BhC,UAAU,CAAC0B,UAAU,CAACM,IAAI,CAAC;QAC3B9B,UAAU,CAACyB,UAAU,CAACK,IAAI,CAAC;QAC3B5B,aAAa,CAACwB,aAAa,CAACI,IAAI,CAAC;MACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDV,WAAW,CAAC;UAAEY,IAAI,EAAE,IAAI;UAAEC,OAAO,EAAExC,CAAC,CAAC,gBAAgB,CAAC;UAAEyC,QAAQ,EAAE;QAAQ,CAAC,CAAC;MAC9E;IACF,CAAC;IACDb,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC5B,CAAC,CAAC,CAAC;;EAEP;EACA,MAAM0C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI/B,WAAW,CAACgC,MAAM,KAAK,CAAC,EAAE;MAC5BtB,iBAAiB,CAAC,EAAE,CAAC;MACrBE,YAAY,CAAC,EAAE,CAAC;MAChB;IACF;IACAE,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM,CAACmB,aAAa,EAAEC,QAAQ,CAAC,GAAG,MAAMZ,OAAO,CAACC,GAAG,CAAC,CAClDY,mBAAmB,CAAC,CAAC,EACrBC,cAAc,CAAC,CAAC,CACjB,CAAC;MACF;MACA,MAAMC,aAAa,GAAGC,qBAAqB,CAACL,aAAa,IAAI,EAAE,CAAC;MAChEvB,iBAAiB,CAAC2B,aAAa,CAAC;MAChCzB,YAAY,CAACsB,QAAQ,IAAI,EAAE,CAAC;IAE9B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDV,WAAW,CAAC;QAAEY,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExC,CAAC,CAAC,gBAAgB,CAAC;QAAEyC,QAAQ,EAAE;MAAQ,CAAC,CAAC;MAC5EpB,iBAAiB,CAAC,EAAE,CAAC;MACrBE,YAAY,CAAC,EAAE,CAAC;IAClB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,MAAMI,GAAG,GAAG,4CAA4CzC,iBAAiB,KAAK,QAAQ,GAAG,UAAU,GAAGA,iBAAiB,GAAG,GAAG,EAAE;IAC/H,MAAM0C,QAAQ,GAAG,GAAG1C,iBAAiB,QAAQE,WAAW,CAACyC,IAAI,CAAC,GAAG,CAAC,EAAE;IACpE,MAAMC,cAAc,GAAGtC,eAAe,GAAG,eAAeA,eAAe,CAACuC,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE;IAChG,MAAMC,YAAY,GAAGrC,aAAa,GAAG,aAAaA,aAAa,CAACoC,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE;IACxF,MAAME,aAAa,GAAG3C,gBAAgB,GAAG,eAAeA,gBAAgB,EAAE,GAAG,EAAE;IAC/E,MAAM4C,WAAW,GAAG,CAACN,QAAQ,EAAEE,cAAc,EAAEE,YAAY,EAAEC,aAAa,CAAC,CAACE,MAAM,CAACC,OAAO,CAAC,CAACP,IAAI,CAAC,GAAG,CAAC;IACrG,MAAMQ,OAAO,GAAG,GAAGV,GAAG,IAAIO,WAAW,EAAE;IACvC,MAAMI,QAAQ,GAAG,MAAMrG,KAAK,CAAC2E,GAAG,CAAgCyB,OAAO,CAAC;IACxE,OAAOC,QAAQ,CAACzB,IAAI;EACtB,CAAC;EAED,MAAMW,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,MAAMG,GAAG,GAAG,iDAAiD;IAC7D,MAAMY,eAAe,GAAG,eAAerD,iBAAiB,EAAE;IAC1D,MAAMsD,cAAc,GAAG,cAAcpD,WAAW,CAACyC,IAAI,CAAC,GAAG,CAAC,EAAE;IAC5D,MAAMC,cAAc,GAAGtC,eAAe,GAAG,eAAeA,eAAe,CAACuC,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE;IAChG,MAAMC,YAAY,GAAGrC,aAAa,GAAG,aAAaA,aAAa,CAACoC,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE;IACxF,MAAME,aAAa,GAAG3C,gBAAgB,GAAG,eAAeA,gBAAgB,EAAE,GAAG,EAAE;IAC/E,MAAM4C,WAAW,GAAG,CAACK,eAAe,EAAEC,cAAc,EAAEV,cAAc,EAAEE,YAAY,EAAEC,aAAa,CAAC,CAACE,MAAM,CAACC,OAAO,CAAC,CAACP,IAAI,CAAC,GAAG,CAAC;IAC5H,MAAMQ,OAAO,GAAG,GAAGV,GAAG,IAAIO,WAAW,EAAE;IACvC,MAAMI,QAAQ,GAAG,MAAMrG,KAAK,CAAC2E,GAAG,CAAwByB,OAAO,CAAC;IAChE,OAAOC,QAAQ,CAACzB,IAAI,CAAC4B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MAAE,GAAGD,IAAI;MAAEE,EAAE,EAAEF,IAAI,CAACG,KAAK,IAAIF,KAAK,CAACG,QAAQ,CAAC;IAAE,CAAC,CAAC,CAAC;EAC9F,CAAC;EAED,MAAMpB,qBAAqB,GAAIb,IAA+C,IAAoC;IAChH,OAAOA,IAAI,CAAC4B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MAAE,GAAGD,IAAI;MAAEE,EAAE,EAAE,GAAGF,IAAI,CAACK,QAAQ,IAAIL,IAAI,CAACM,MAAM,IAAIL,KAAK;IAAG,CAAC,CAAC,CAAC;EACjG,CAAC;EAED,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;IAC/B5D,cAAc,CAAC,EAAE,CAAC;IAClBE,mBAAmB,CAAC,EAAE,CAAC;IACvBE,kBAAkB,CAACtB,KAAK,CAAC,CAAC,CAACuB,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAC/CE,gBAAgB,CAACzB,KAAK,CAAC,CAAC,CAAC;EAC3B,CAAC;EAED,MAAM+E,OAAO,GAAGnH,OAAO,CAAC,MAAM;IAC5B,OAAO8D,cAAc,CAACsD,MAAM,CAAC,CAACC,GAAG,EAAEV,IAAI,KAAK;MAC1CU,GAAG,CAACC,SAAS,IAAIX,IAAI,CAACY,SAAS;MAC/BF,GAAG,CAACG,aAAa,IAAIb,IAAI,CAACc,aAAa;MACvCJ,GAAG,CAACK,UAAU,IAAIf,IAAI,CAACgB,aAAa;MACpC,OAAON,GAAG;IACZ,CAAC,EAAE;MAAEC,SAAS,EAAE,CAAC;MAAEE,aAAa,EAAE,CAAC;MAAEE,UAAU,EAAE;IAAE,CAAC,CAAC;EACvD,CAAC,EAAE,CAAC5D,cAAc,CAAC,CAAC;EAEpB,MAAM8D,oBAAoB,GAAG5H,OAAO,CAAC,MAAM;IACzC,MAAM8E,IAAI,GAAGhB,cAAc,CAACsD,MAAM,CAAC,CAACC,GAAG,EAAEV,IAAI,KAAK;MAChD,MAAMkB,QAAQ,GAAGlB,IAAI,CAACmB,YAAY;MAClC,IAAI,CAACT,GAAG,CAACQ,QAAQ,CAAC,EAAE;QAClBR,GAAG,CAACQ,QAAQ,CAAC,GAAG;UAAEE,IAAI,EAAEF,QAAQ;UAAEG,KAAK,EAAE;QAAE,CAAC;MAC9C;MACAX,GAAG,CAACQ,QAAQ,CAAC,CAACG,KAAK,IAAIrB,IAAI,CAACY,SAAS;MACrC,OAAOF,GAAG;IACZ,CAAC,EAAE,CAAC,CAAuD,CAAC;IAC5D,OAAOY,MAAM,CAACC,MAAM,CAACpD,IAAI,CAAC;EAC5B,CAAC,EAAE,CAAChB,cAAc,CAAC,CAAC;EAEpB,MAAMqE,oBAAoB,GAAGnI,OAAO,CAAC,MAAM;IACzC,MAAM8E,IAAI,GAAGhB,cAAc,CAACsD,MAAM,CAAC,CAACC,GAAG,EAAEV,IAAI,KAAK;MAC9C,MAAMyB,MAAM,GAAGzB,IAAI,CAAC0B,UAAU;MAC9B,IAAI,CAAChB,GAAG,CAACe,MAAM,CAAC,EAAE;QACdf,GAAG,CAACe,MAAM,CAAC,GAAG;UAAEL,IAAI,EAAEK,MAAM;UAAEb,SAAS,EAAE,CAAC;UAAEE,aAAa,EAAE,CAAC;UAAEE,aAAa,EAAE;QAAE,CAAC;MACpF;MACAN,GAAG,CAACe,MAAM,CAAC,CAACb,SAAS,IAAIZ,IAAI,CAACY,SAAS;MACvCF,GAAG,CAACe,MAAM,CAAC,CAACX,aAAa,IAAId,IAAI,CAACc,aAAa;MAC/CJ,GAAG,CAACe,MAAM,CAAC,CAACT,aAAa,IAAIhB,IAAI,CAACgB,aAAa;MAC/C,OAAON,GAAG;IACd,CAAC,EAAE,CAAC,CAAyG,CAAC;IAC9G,OAAOY,MAAM,CAACC,MAAM,CAACpD,IAAI,CAAC;EAC9B,CAAC,EAAE,CAAChB,cAAc,CAAC,CAAC;EAElB,MAAMwE,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,KAAK,GAAGpF,iBAAiB,KAAK,QAAQ,GAAGJ,OAAO,GAAGI,iBAAiB,KAAK,QAAQ,GAAGN,OAAO,GAAGF,QAAQ;IAC5G,MAAM6F,KAAK,GAAG9F,CAAC,CAAC,SAASS,iBAAiB,CAACsF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGvF,iBAAiB,CAACwF,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;IACnG,oBACEpG,OAAA,CAAClC,WAAW;MAACuI,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAC,QAAA,gBAChDxG,OAAA,CAACjC,UAAU;QAAAyI,QAAA,EAAEP;MAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC5G,OAAA,CAAChC,MAAM;QACL6I,QAAQ;QACRpB,KAAK,EAAE3E,WAAY;QACnBgG,QAAQ,EAAGC,CAAC,IAAKhG,cAAc,CAACgG,CAAC,CAACC,MAAM,CAACvB,KAAiB,CAAE;QAC5DwB,KAAK,eAAEjH,OAAA,CAAC1B,aAAa;UAAC2H,KAAK,EAAEA;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvCM,WAAW,EAAGC,QAAQ,IAAKA,QAAQ,CAAChD,GAAG,CAACG,EAAE;UAAA,IAAA8C,WAAA;UAAA,QAAAA,WAAA,GAAIpB,KAAK,CAACqB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChD,EAAE,KAAKA,EAAE,CAAC,cAAA8C,WAAA,uBAA5BA,WAAA,CAA8B5B,IAAI;QAAA,EAAC,CAACjC,IAAI,CAAC,IAAI,CAAE;QAAAiD,QAAA,EAE5FR,KAAK,CAAC7B,GAAG,CAAEC,IAAI,iBACdpE,OAAA,CAAC/B,QAAQ;UAAewH,KAAK,EAAErB,IAAI,CAACE,EAAG;UAAAkC,QAAA,gBACrCxG,OAAA,CAACzB,QAAQ;YAACgJ,OAAO,EAAEzG,WAAW,CAAC0G,OAAO,CAACpD,IAAI,CAACE,EAAE,CAAC,GAAG,CAAC;UAAE;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxD5G,OAAA,CAACxB,YAAY;YAACiJ,OAAO,EAAErD,IAAI,CAACoB;UAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GAFvBxC,IAAI,CAACE,EAAE;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGZ,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAElB,CAAC;EAED,MAAMc,OAAqB,GAAG,CAC5B;IAAEC,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAEzH,CAAC,CAAC,YAAY,CAAC;IAAE0H,KAAK,EAAE;EAAI,CAAC,EAChE;IAAEF,KAAK,EAAE,cAAc;IAAEC,UAAU,EAAEzH,CAAC,CAAC,UAAU,CAAC;IAAE0H,KAAK,EAAE;EAAI,CAAC,EAChE;IAAEF,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAEzH,CAAC,CAAC,UAAU,CAAC;IAAE0H,KAAK,EAAE;EAAI,CAAC,EAC5D;IAAEF,KAAK,EAAE,eAAe;IAAEC,UAAU,EAAEzH,CAAC,CAAC,eAAe,CAAC;IAAE2H,IAAI,EAAE,QAAQ;IAAED,KAAK,EAAE;EAAI,CAAC,EACtF;IAAEF,KAAK,EAAE,WAAW;IAAEC,UAAU,EAAEzH,CAAC,CAAC,WAAW,CAAC;IAAE2H,IAAI,EAAE,QAAQ;IAAED,KAAK,EAAE,GAAG;IAAEE,cAAc,EAAGC,MAAyB,IAAKA,MAAM,CAACvC,KAAK,CAACwC,OAAO,CAAC,CAAC;EAAE,CAAC,EACtJ;IAAEN,KAAK,EAAE,eAAe;IAAEC,UAAU,EAAEzH,CAAC,CAAC,eAAe,CAAC;IAAE2H,IAAI,EAAE,QAAQ;IAAED,KAAK,EAAE;EAAI,CAAC,CACvF;EAED,MAAMK,YAA0B,GAAG,CACjC;IAAEP,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAEzH,CAAC,CAAC,OAAO,CAAC;IAAE0H,KAAK,EAAE;EAAI,CAAC,EACtD;IAAEF,KAAK,EAAE,eAAe;IAAEC,UAAU,EAAEzH,CAAC,CAAC,eAAe,CAAC;IAAE2H,IAAI,EAAE,QAAQ;IAAED,KAAK,EAAE;EAAI,CAAC,EACtF;IAAEF,KAAK,EAAE,WAAW;IAAEC,UAAU,EAAEzH,CAAC,CAAC,WAAW,CAAC;IAAE2H,IAAI,EAAE,QAAQ;IAAED,KAAK,EAAE,GAAG;IAAEE,cAAc,EAAGC,MAAyB,IAAKA,MAAM,CAACvC,KAAK,CAACwC,OAAO,CAAC,CAAC;EAAE,CAAC,EACtJ;IAAEN,KAAK,EAAE,eAAe;IAAEC,UAAU,EAAEzH,CAAC,CAAC,eAAe,CAAC;IAAE2H,IAAI,EAAE,QAAQ;IAAED,KAAK,EAAE;EAAI,CAAC,CACvF;EAED,MAAMM,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAEjF,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI7G,cAAc,CAACuB,MAAM,KAAK,CAAC,EAAE;MAC/BhB,WAAW,CAAC;QAAEY,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExC,CAAC,CAAC,gBAAgB,CAAC;QAAEyC,QAAQ,EAAE;MAAQ,CAAC,CAAC;MAC5E;IACF;IAEA,IAAI;MAAA,IAAAyF,gBAAA;MACF,MAAMC,WAAW,GAAGnI,CAAC,CAAC,qBAAqB,CAAC;MAC5C,MAAMoI,YAAY,GAAGhH,cAAc,CAAC4C,GAAG,CAACC,IAAI,KAAK;QAC/C,aAAa,EAAEA,IAAI,CAAC0B,UAAU;QAC9B,UAAU,EAAE1B,IAAI,CAACmB,YAAY;QAC7B,WAAW,EAAEnB,IAAI,CAACoE,QAAQ;QAC1B,gBAAgB,EAAEpE,IAAI,CAACc,aAAa;QACpC,YAAY,EAAEd,IAAI,CAACY,SAAS;QAC5B,iBAAiB,EAAEZ,IAAI,CAACgB;MAC1B,CAAC,CAAC,CAAC;MACH3C,OAAO,CAACgG,GAAG,CAAC,iBAAiB,EAAEF,YAAY,CAAC,CAAC,CAAC;;MAE9C,MAAMvE,QAAQ,GAAG,MAAMrG,KAAK,CAAC+K,IAAI,CAC/B,2CAA2C,EAC3CH,YAAY;MAAE;MACd;QACEI,YAAY,EAAE,MAAM;QAAE;QACtBX,MAAM,EAAE;UAAEY,KAAK,EAAEN;QAAY,CAAC,CAAC;MACjC,CACF,CAAC;MAED,MAAMjF,GAAG,GAAGwF,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAChF,QAAQ,CAACzB,IAAI,CAAC,CAAC,CAAC;MACjE,MAAM0G,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG/F,GAAG;MACf4F,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,uBAAuB,CAAC;MACtDH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZ,CAAAnB,gBAAA,GAAAY,IAAI,CAACQ,UAAU,cAAApB,gBAAA,uBAAfA,gBAAA,CAAiBqB,WAAW,CAACT,IAAI,CAAC;MAClCnH,WAAW,CAAC;QAAEY,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExC,CAAC,CAAC,kBAAkB,CAAC;QAAEyC,QAAQ,EAAE;MAAU,CAAC,CAAC;IAClF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CV,WAAW,CAAC;QAAEY,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExC,CAAC,CAAC,gBAAgB,CAAC;QAAEyC,QAAQ,EAAE;MAAQ,CAAC,CAAC;IAC9E;EACF,CAAC;EAED,MAAM+G,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIpI,cAAc,CAACuB,MAAM,KAAK,CAAC,EAAE;MAC/BhB,WAAW,CAAC;QAAEY,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExC,CAAC,CAAC,gBAAgB,CAAC;QAAEyC,QAAQ,EAAE;MAAQ,CAAC,CAAC;MAC5E;IACF;IAEA,MAAM2F,YAAY,GAAGhH,cAAc,CAAC4C,GAAG,CAACyF,GAAG,KAAK;MAC9C,CAACzJ,CAAC,CAAC,YAAY,CAAC,GAAGyJ,GAAG,CAAC9D,UAAU;MACjC,CAAC3F,CAAC,CAAC,cAAc,CAAC,GAAGyJ,GAAG,CAACrE,YAAY;MACrC,CAACpF,CAAC,CAAC,UAAU,CAAC,GAAGyJ,GAAG,CAACpB,QAAQ;MAC7B,CAACrI,CAAC,CAAC,eAAe,CAAC,GAAGyJ,GAAG,CAAC1E,aAAa;MACvC,CAAC/E,CAAC,CAAC,WAAW,CAAC,GAAGyJ,GAAG,CAAC5E,SAAS;MAC/B,CAAC7E,CAAC,CAAC,eAAe,CAAC,GAAGyJ,GAAG,CAACxE;IAC5B,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMkD,WAAW,GAAGnI,CAAC,CAAC,qBAAqB,CAAC;IAC5C,MAAM0J,GAA0B,GAAG,EAAE;IACrCA,GAAG,CAACC,IAAI,CAAC,CAACxB,WAAW,CAAC,CAAC,CAAC,CAAC;IACzBuB,GAAG,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd;IACA,MAAMC,OAAO,GAAGrE,MAAM,CAACsE,IAAI,CAACzB,YAAY,CAAC,CAAC,CAAC,CAAC;IAC5CsB,GAAG,CAACC,IAAI,CAACC,OAAO,CAAC;;IAEjB;IACAxB,YAAY,CAAC0B,OAAO,CAACL,GAAG,IAAI;MAC1BC,GAAG,CAACC,IAAI,CAACpE,MAAM,CAACC,MAAM,CAACiE,GAAG,CAAC,CAAC;IAC9B,CAAC,CAAC;IAEF,MAAMM,EAAE,GAAGpK,IAAI,CAACqK,KAAK,CAACC,YAAY,CAACP,GAAG,CAAC;IACvC,MAAMQ,EAAE,GAAGvK,IAAI,CAACqK,KAAK,CAACG,QAAQ,CAAC,CAAC;;IAEhC;IACA,MAAMC,SAAS,GAAGR,OAAO,CAAC5F,GAAG,CAACqG,MAAM,IAAI;MACtC,IAAIjE,QAAQ,GAAGiE,MAAM,CAAC1H,MAAM;MAC5ByF,YAAY,CAAC0B,OAAO,CAACL,GAAG,IAAI;QAC1B,MAAMa,SAAS,GAAGC,MAAM,CAACd,GAAG,CAACY,MAAM,CAAC,CAAC;QACrCjE,QAAQ,GAAGoE,IAAI,CAACC,GAAG,CAACrE,QAAQ,EAAEkE,SAAS,CAAC3H,MAAM,CAAC;MACjD,CAAC,CAAC;MACF,OAAO;QAAE+H,GAAG,EAAEF,IAAI,CAACG,GAAG,CAACvE,QAAQ,GAAG,CAAC,EAAE,EAAE;MAAE,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;IACF2D,EAAE,CAAC,OAAO,CAAC,GAAGK,SAAS;;IAEvB;IACA,MAAMQ,aAAa,GAAGjL,IAAI,CAACqK,KAAK,CAACa,WAAW,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;IAC5D,IAAI,CAAChB,EAAE,CAACa,aAAa,CAAC,EAAEb,EAAE,CAACa,aAAa,CAAC,GAAG;MAAEI,CAAC,EAAE7C;IAAY,CAAC;IAC9D4B,EAAE,CAACa,aAAa,CAAC,CAACK,CAAC,GAAG;MACpBC,IAAI,EAAE;QAAEC,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE;UAAEC,GAAG,EAAE;QAAW;MAAE,CAAC;MAC1DC,SAAS,EAAE;QAAEC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACvDC,IAAI,EAAE;QAAEC,OAAO,EAAE;UAAEL,GAAG,EAAE;QAAW;MAAE,CAAC;MAAE;MACxCM,MAAM,EAAE;QACNC,GAAG,EAAE;UAAEC,KAAK,EAAE,QAAQ;UAAET,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAW;QAAE,CAAC;QACpDS,MAAM,EAAE;UAAED,KAAK,EAAE,QAAQ;UAAET,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAW;QAAE,CAAC;QACvDU,IAAI,EAAE;UAAEF,KAAK,EAAE,QAAQ;UAAET,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAW;QAAE,CAAC;QACrDW,KAAK,EAAE;UAAEH,KAAK,EAAE,QAAQ;UAAET,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAW;QAAE;MACvD;IACF,CAAC;;IAED;IACA,MAAMY,UAAU,GAAG;MAAEjB,CAAC,EAAE;QAAEH,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MAAEnE,CAAC,EAAE;QAAEkE,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAEnB,OAAO,CAACjH,MAAM,GAAG;MAAE;IAAE,CAAC;IAC5E,IAAI,CAACoH,EAAE,CAAC,SAAS,CAAC,EAAEA,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE;IACtCA,EAAE,CAAC,SAAS,CAAC,CAACJ,IAAI,CAACuC,UAAU,CAAC;;IAE9B;IACA,MAAMC,cAAc,GAAG,CAAC;IACxB,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,OAAO,CAACjH,MAAM,EAAEoI,CAAC,EAAE,EAAE;MACvC,MAAMqB,WAAW,GAAGzM,IAAI,CAACqK,KAAK,CAACa,WAAW,CAAC;QAAEC,CAAC,EAAEqB,cAAc;QAAEpB,CAAC,EAAEA;MAAE,CAAC,CAAC;MACvE,IAAI,CAAChB,EAAE,CAACqC,WAAW,CAAC,EAAErC,EAAE,CAACqC,WAAW,CAAC,GAAG;QAAEpB,CAAC,EAAEpB,OAAO,CAACmB,CAAC;MAAE,CAAC;MACzDhB,EAAE,CAACqC,WAAW,CAAC,CAACnB,CAAC,GAAG;QAClBC,IAAI,EAAE;UAAEC,IAAI,EAAE,IAAI;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAW;QAAE,CAAC;QAAE;QAC5DC,SAAS,EAAE;UAAEC,UAAU,EAAE,QAAQ;UAAEC,QAAQ,EAAE;QAAS,CAAC;QACvDC,IAAI,EAAE;UAAEC,OAAO,EAAE;YAAEL,GAAG,EAAE;UAAW;QAAE,CAAC;QAAE;QACxCM,MAAM,EAAE;UACNC,GAAG,EAAE;YAAEC,KAAK,EAAE,QAAQ;YAAET,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE,CAAC;UACpDS,MAAM,EAAE;YAAED,KAAK,EAAE,QAAQ;YAAET,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE,CAAC;UACvDU,IAAI,EAAE;YAAEF,KAAK,EAAE,QAAQ;YAAET,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE,CAAC;UACrDW,KAAK,EAAE;YAAEH,KAAK,EAAE,QAAQ;YAAET,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE;QACvD;MACF,CAAC;IACH;;IAEA;IACA,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,GAAG,CAAC/G,MAAM,EAAEmI,CAAC,EAAE,EAAE;MACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,OAAO,CAACjH,MAAM,EAAEoI,CAAC,EAAE,EAAE;QACvC,MAAMqB,WAAW,GAAGzM,IAAI,CAACqK,KAAK,CAACa,WAAW,CAAC;UAAEC,CAAC,EAAEA,CAAC;UAAEC,CAAC,EAAEA;QAAE,CAAC,CAAC;QAC1D,IAAI,CAAChB,EAAE,CAACqC,WAAW,CAAC,EAAErC,EAAE,CAACqC,WAAW,CAAC,GAAG;UAAEpB,CAAC,EAAEtB,GAAG,CAACoB,CAAC,CAAC,CAACC,CAAC;QAAE,CAAC;;QAExD;QACA,MAAMsB,SAAS,GAAG,CAACvB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;QACnCf,EAAE,CAACqC,WAAW,CAAC,CAACnB,CAAC,GAAG;UAClBC,IAAI,EAAE;YAAEE,IAAI,EAAE;UAAG,CAAC;UAClBG,SAAS,EAAE;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAC;UACvDC,IAAI,EAAE;YAAEC,OAAO,EAAE;cAAEL,GAAG,EAAEe,SAAS,GAAG,UAAU,GAAG;YAAW;UAAE,CAAC;UAAE;UACjET,MAAM,EAAE;YACNC,GAAG,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YAClDS,MAAM,EAAE;cAAED,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACrDU,IAAI,EAAE;cAAEF,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACnDW,KAAK,EAAE;cAAEH,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE;UACrD;QACF,CAAC;MACH;IACF;IAEA3L,IAAI,CAACqK,KAAK,CAACsC,iBAAiB,CAACpC,EAAE,EAAEH,EAAE,EAAE,gBAAgB,CAAC;IACtDpK,IAAI,CAAC4M,SAAS,CAACrC,EAAE,EAAE,qBAAqB,EAAE;MAAEsC,UAAU,EAAE;IAAK,CAAC,CAAC;IAC/D7K,WAAW,CAAC;MAAEY,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAExC,CAAC,CAAC,oBAAoB,CAAC;MAAEyC,QAAQ,EAAE;IAAU,CAAC,CAAC;EACpF,CAAC;EAED,oBACE5C,OAAA,CAACjB,oBAAoB;IAAC6N,WAAW,EAAE9N,YAAa;IAAA0H,QAAA,eAC9CxG,OAAA,CAACpC,GAAG;MAACyI,EAAE,EAAE;QAAEwG,CAAC,EAAE;MAAE,CAAE;MAACvI,EAAE,EAAC,2BAA2B;MAAAkC,QAAA,GAAC,GAAC,eACjDxG,OAAA,CAACnC,UAAU;QAACiP,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAvG,QAAA,EAAErG,CAAC,CAAC,qBAAqB;MAAC;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAG7E5G,OAAA,CAACtB,KAAK;QAAC2H,EAAE,EAAE;UAAEwG,CAAC,EAAE,CAAC;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAxG,QAAA,eACzBxG,OAAA,CAACvB,IAAI;UAACwO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAA3G,QAAA,gBAC7CxG,OAAA,CAACvB,IAAI;YAAC2F,IAAI;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9G,QAAA,eAACxG,OAAA,CAAClC,WAAW;cAACyP,SAAS;cAAA/G,QAAA,gBAACxG,OAAA,CAACjC,UAAU;gBAAAyI,QAAA,EAAErG,CAAC,CAAC,WAAW;cAAC;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA5G,OAAA,CAAChC,MAAM;gBAACyH,KAAK,EAAE7E,iBAAkB;gBAACqF,KAAK,EAAE9F,CAAC,CAAC,WAAW,CAAE;gBAAC2G,QAAQ,EAAGC,CAAC,IAAK;kBAAElG,oBAAoB,CAACkG,CAAC,CAACC,MAAM,CAACvB,KAAY,CAAC;kBAAE1E,cAAc,CAAC,EAAE,CAAC;gBAAE,CAAE;gBAAAyF,QAAA,gBAC9OxG,OAAA,CAAC/B,QAAQ;kBAACwH,KAAK,EAAC,QAAQ;kBAAAe,QAAA,EAAErG,CAAC,CAAC,SAAS;gBAAC;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClD5G,OAAA,CAAC/B,QAAQ;kBAACwH,KAAK,EAAC,QAAQ;kBAAAe,QAAA,EAAErG,CAAC,CAAC,SAAS;gBAAC;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClD5G,OAAA,CAAC/B,QAAQ;kBAACwH,KAAK,EAAC,QAAQ;kBAAAe,QAAA,EAAErG,CAAC,CAAC,UAAU;gBAAC;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9B5G,OAAA,CAACvB,IAAI;YAAC2F,IAAI;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9G,QAAA,EAAET,qBAAqB,CAAC;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjE5G,OAAA,CAACvB,IAAI;YAAC2F,IAAI;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9G,QAAA,eAACxG,OAAA,CAAClC,WAAW;cAACyP,SAAS;cAAA/G,QAAA,gBAACxG,OAAA,CAACjC,UAAU;gBAAAyI,QAAA,EAAErG,CAAC,CAAC,kBAAkB;cAAC;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA5G,OAAA,CAAChC,MAAM;gBAACyH,KAAK,EAAEzE,gBAAiB;gBAACiF,KAAK,EAAE9F,CAAC,CAAC,kBAAkB,CAAE;gBAAC2G,QAAQ,EAAGC,CAAC,IAAK9F,mBAAmB,CAAC8F,CAAC,CAACC,MAAM,CAACvB,KAAe,CAAE;gBAAAe,QAAA,gBACpOxG,OAAA,CAAC/B,QAAQ;kBAACwH,KAAK,EAAC,EAAE;kBAAAe,QAAA,EAAErG,CAAC,CAAC,eAAe;gBAAC;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,EACjDlG,UAAU,CAACyD,GAAG,CAAEqJ,GAAG,iBAAKxN,OAAA,CAAC/B,QAAQ;kBAAsBwH,KAAK,EAAE+H,GAAG,CAACC,UAAW;kBAAAjH,QAAA,EAAEgH,GAAG,CAACjI;gBAAY,GAAxDiI,GAAG,CAACC,UAAU;kBAAAhH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqD,CAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9B5G,OAAA,CAACvB,IAAI;YAAC2F,IAAI;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9G,QAAA,eAACxG,OAAA,CAACnB,UAAU;cAACoH,KAAK,EAAE9F,CAAC,CAAC,oBAAoB,CAAE;cAACuN,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAE;cAACC,MAAM,EAAC,OAAO;cAAClI,KAAK,EAAEvE,eAAgB;cAAC4F,QAAQ,EAAE3F,kBAAmB;cAACyM,KAAK,EAAE;gBAAEC,SAAS,EAAExP;cAAU,CAAE;cAACyP,SAAS,EAAE;gBAAED,SAAS,EAAE;kBAAEN,SAAS,EAAE;gBAAK;cAAE;YAAE;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrQ5G,OAAA,CAACvB,IAAI;YAAC2F,IAAI;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9G,QAAA,eAACxG,OAAA,CAACnB,UAAU;cAACoH,KAAK,EAAE9F,CAAC,CAAC,kBAAkB,CAAE;cAACuN,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAE;cAACC,MAAM,EAAC,OAAO;cAAClI,KAAK,EAAEpE,aAAc;cAACyF,QAAQ,EAAExF,gBAAiB;cAACsM,KAAK,EAAE;gBAAEC,SAAS,EAAExP;cAAU,CAAE;cAACyP,SAAS,EAAE;gBAAED,SAAS,EAAE;kBAAEN,SAAS,EAAE;gBAAK;cAAE;YAAE;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/P5G,OAAA,CAACvB,IAAI;YAAC2F,IAAI;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9G,QAAA,eAACxG,OAAA,CAAC5B,MAAM;cAAC0O,OAAO,EAAC,WAAW;cAACtB,KAAK,EAAC,SAAS;cAACuC,OAAO,EAAElL,YAAa;cAACmL,QAAQ,EAAErM,OAAO,IAAIb,WAAW,CAACgC,MAAM,KAAK,CAAE;cAAA0D,QAAA,EAAErG,CAAC,CAAC,cAAc;YAAC;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtL5G,OAAA,CAACvB,IAAI;YAAC2F,IAAI;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9G,QAAA,eAACxG,OAAA,CAAC5B,MAAM;cAAC0O,OAAO,EAAC,UAAU;cAACtB,KAAK,EAAC,WAAW;cAACuC,OAAO,EAAEpJ,kBAAmB;cAAA6B,QAAA,EAAErG,CAAC,CAAC,cAAc;YAAC;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9I5G,OAAA,CAACvB,IAAI;YAAC2F,IAAI;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9G,QAAA,eAACxG,OAAA,CAAC5B,MAAM;cAAC0O,OAAO,EAAC,WAAW;cAACtB,KAAK,EAAC,SAAS;cAACuC,OAAO,EAAE3F,eAAgB;cAAA5B,QAAA,EAAErG,CAAC,CAAC,aAAa;YAAC;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzI5G,OAAA,CAACvB,IAAI;YAAC2F,IAAI;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9G,QAAA,eAACxG,OAAA,CAAC5B,MAAM;cAAC0O,OAAO,EAAC,WAAW;cAACtB,KAAK,EAAC,MAAM;cAACuC,OAAO,EAAEpE,iBAAkB;cAAAnD,QAAA,EAAErG,CAAC,CAAC,eAAe;YAAC;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR5G,OAAA,CAACvB,IAAI;QAACwO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC7G,EAAE,EAAE;UAAE2G,EAAE,EAAE;QAAE,CAAE;QAAAxG,QAAA,gBACtCxG,OAAA,CAACvB,IAAI;UAAC2F,IAAI;UAACgJ,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9G,QAAA,eAACxG,OAAA,CAACrB,IAAI;YAAA6H,QAAA,eAACxG,OAAA,CAACpB,WAAW;cAAA4H,QAAA,gBAACxG,OAAA,CAACnC,UAAU;gBAAC2N,KAAK,EAAC,eAAe;gBAACuB,YAAY;gBAAAvG,QAAA,EAAErG,CAAC,CAAC,WAAW;cAAC;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA5G,OAAA,CAACnC,UAAU;gBAACiP,OAAO,EAAC,IAAI;gBAAAtG,QAAA,EAAE5B,OAAO,CAACG,SAAS,CAACkJ,cAAc,CAACC,SAAS,EAAE;kBAACC,qBAAqB,EAAE,CAAC;kBAAEC,qBAAqB,EAAE;gBAAC,CAAC;cAAC;gBAAA3H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrS5G,OAAA,CAACvB,IAAI;UAAC2F,IAAI;UAACgJ,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9G,QAAA,eAACxG,OAAA,CAACrB,IAAI;YAAA6H,QAAA,eAACxG,OAAA,CAACpB,WAAW;cAAA4H,QAAA,gBAACxG,OAAA,CAACnC,UAAU;gBAAC2N,KAAK,EAAC,eAAe;gBAACuB,YAAY;gBAAAvG,QAAA,EAAErG,CAAC,CAAC,eAAe;cAAC;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA5G,OAAA,CAACnC,UAAU;gBAACiP,OAAO,EAAC,IAAI;gBAAAtG,QAAA,EAAE5B,OAAO,CAACK,aAAa,CAACgJ,cAAc,CAAC;cAAC;gBAAAxH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9O5G,OAAA,CAACvB,IAAI;UAAC2F,IAAI;UAACgJ,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9G,QAAA,eAACxG,OAAA,CAACrB,IAAI;YAAA6H,QAAA,eAACxG,OAAA,CAACpB,WAAW;cAAA4H,QAAA,gBAACxG,OAAA,CAACnC,UAAU;gBAAC2N,KAAK,EAAC,eAAe;gBAACuB,YAAY;gBAAAvG,QAAA,EAAErG,CAAC,CAAC,eAAe;cAAC;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA5G,OAAA,CAACnC,UAAU;gBAACiP,OAAO,EAAC,IAAI;gBAAAtG,QAAA,EAAE5B,OAAO,CAACO,UAAU,CAAC8I,cAAc,CAAC;cAAC;gBAAAxH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3O5G,OAAA,CAACvB,IAAI;UAAC2F,IAAI;UAACgJ,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9G,QAAA,eAACxG,OAAA,CAACrB,IAAI;YAAA6H,QAAA,eAACxG,OAAA,CAACpB,WAAW;cAAA4H,QAAA,gBAACxG,OAAA,CAACnC,UAAU;gBAAC2N,KAAK,EAAC,eAAe;gBAACuB,YAAY;gBAAAvG,QAAA,EAAErG,CAAC,CAAC,gBAAgB;cAAC;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA5G,OAAA,CAACnC,UAAU;gBAACiP,OAAO,EAAC,IAAI;gBAAAtG,QAAA,EAAE,CAAC5B,OAAO,CAACO,UAAU,GAAG,CAAC,GAAGP,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACO,UAAU,GAAG,CAAC,EAAE8I,cAAc,CAACC,SAAS,EAAE;kBAACC,qBAAqB,EAAE,CAAC;kBAAEC,qBAAqB,EAAE;gBAAC,CAAC;cAAC;gBAAA3H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5V,CAAC,eAGP5G,OAAA,CAACvB,IAAI;QAACwO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC7G,EAAE,EAAE;UAAE2G,EAAE,EAAE;QAAE,CAAE;QAAAxG,QAAA,gBACxCxG,OAAA,CAACvB,IAAI;UAAC2F,IAAI;UAACgJ,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA9G,QAAA,eAACxG,OAAA,CAACtB,KAAK;YAAC2H,EAAE,EAAE;cAAEwG,CAAC,EAAE,CAAC;cAAEwB,MAAM,EAAE;YAAI,CAAE;YAAA7H,QAAA,gBAACxG,OAAA,CAACnC,UAAU;cAACiP,OAAO,EAAC,IAAI;cAAAtG,QAAA,EAAErG,CAAC,CAAC,eAAe;YAAC;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAAA5G,OAAA,CAACT,mBAAmB;cAAAiH,QAAA,eAACxG,OAAA,CAAChB,QAAQ;gBAACuD,IAAI,EAAEd,SAAU;gBAAA+E,QAAA,gBAACxG,OAAA,CAACZ,aAAa;kBAACkP,eAAe,EAAC;gBAAK;kBAAA7H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACd,KAAK;kBAACqP,OAAO,EAAC;gBAAO;kBAAA9H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACb,KAAK;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACX,OAAO;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACV,MAAM;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACf,GAAG;kBAACsP,OAAO,EAAC,WAAW;kBAAC1C,IAAI,EAAC,SAAS;kBAACrG,IAAI,EAAErF,CAAC,CAAC,WAAW;gBAAE;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACf,GAAG;kBAACsP,OAAO,EAAC,eAAe;kBAAC1C,IAAI,EAAC,SAAS;kBAACrG,IAAI,EAAErF,CAAC,CAAC,eAAe;gBAAE;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5b5G,OAAA,CAACvB,IAAI;UAAC2F,IAAI;UAACgJ,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA9G,QAAA,eAACxG,OAAA,CAACtB,KAAK;YAAC2H,EAAE,EAAE;cAAEwG,CAAC,EAAE,CAAC;cAAEwB,MAAM,EAAE;YAAI,CAAE;YAAA7H,QAAA,gBAACxG,OAAA,CAACnC,UAAU;cAACiP,OAAO,EAAC,IAAI;cAAAtG,QAAA,EAAErG,CAAC,CAAC,gBAAgB;YAAC;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAAA5G,OAAA,CAACT,mBAAmB;cAAAiH,QAAA,eAACxG,OAAA,CAACR,QAAQ;gBAAAgH,QAAA,gBAACxG,OAAA,CAACP,GAAG;kBAAC8C,IAAI,EAAE8C,oBAAqB;kBAACkJ,OAAO,EAAC,OAAO;kBAACC,OAAO,EAAC,MAAM;kBAACC,EAAE,EAAC,KAAK;kBAACC,EAAE,EAAC,KAAK;kBAACC,WAAW,EAAE,GAAI;kBAAC9C,IAAI,EAAC,SAAS;kBAAC5F,KAAK;kBAAAO,QAAA,EAAEnB,oBAAoB,CAAClB,GAAG,CAAC,CAACyK,KAAK,EAAEvK,KAAK,kBAAKrE,OAAA,CAACN,IAAI;oBAAuBmM,IAAI,EAAE1D,MAAM,CAAC9D,KAAK,GAAG8D,MAAM,CAACrF,MAAM;kBAAE,GAArD,QAAQuB,KAAK,EAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAwC,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAAA5G,OAAA,CAACX,OAAO;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjc5G,OAAA,CAACvB,IAAI;UAAC2F,IAAI;UAACgJ,EAAE,EAAE,EAAG;UAAA5G,QAAA,eAACxG,OAAA,CAACtB,KAAK;YAAC2H,EAAE,EAAE;cAAEwG,CAAC,EAAE,CAAC;cAAEwB,MAAM,EAAE;YAAI,CAAE;YAAA7H,QAAA,gBAACxG,OAAA,CAACnC,UAAU;cAACiP,OAAO,EAAC,IAAI;cAAAtG,QAAA,EAAErG,CAAC,CAAC,kBAAkB;YAAC;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAAA5G,OAAA,CAACT,mBAAmB;cAAAiH,QAAA,eAACxG,OAAA,CAAChB,QAAQ;gBAACuD,IAAI,EAAEqD,oBAAqB;gBAAAY,QAAA,gBAACxG,OAAA,CAACZ,aAAa;kBAACkP,eAAe,EAAC;gBAAK;kBAAA7H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACd,KAAK;kBAACqP,OAAO,EAAC;gBAAM;kBAAA9H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACb,KAAK;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACX,OAAO;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACV,MAAM;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACf,GAAG;kBAACsP,OAAO,EAAC,WAAW;kBAAC1C,IAAI,EAAC,SAAS;kBAACrG,IAAI,EAAErF,CAAC,CAAC,WAAW;gBAAE;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA5G,OAAA,CAACf,GAAG;kBAACsP,OAAO,EAAC,eAAe;kBAAC1C,IAAI,EAAC,SAAS;kBAACrG,IAAI,EAAErF,CAAC,CAAC,eAAe;gBAAE;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9b,CAAC,eAGP5G,OAAA,CAACtB,KAAK;QAAC2H,EAAE,EAAE;UAAEgI,MAAM,EAAE,GAAG;UAAExG,KAAK,EAAE,MAAM;UAAEmF,EAAE,EAAE;QAAE,CAAE;QAAAxG,QAAA,gBAC/CxG,OAAA,CAACnC,UAAU;UAACiP,OAAO,EAAC,IAAI;UAACzG,EAAE,EAAE;YAAEwG,CAAC,EAAE;UAAE,CAAE;UAAArG,QAAA,EAAErG,CAAC,CAAC,wBAAwB;QAAC;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjF5G,OAAA,CAACL,QAAQ;UACPkP,IAAI,EAAEtN,cAAe;UACrBmG,OAAO,EAAEA,OAAQ;UACjB/F,OAAO,EAAEA,OAAQ;UACjBiM,KAAK,EAAE;YAAEkB,OAAO,EAAElP;UAAY,CAAE;UAChCkO,SAAS,EAAE;YACTgB,OAAO,EAAE;cACPC,eAAe,EAAE,IAAI;cACrBC,gBAAgB,EAAE;gBAAEC,UAAU,EAAE;cAAI,CAAC;cACrCC,UAAU,EAAE;gBAAEC,oBAAoB,EAAE;cAAM,CAAC;cAC3CC,YAAY,EAAE;gBAAED,oBAAoB,EAAE;cAAM;YAC9C;UACF,CAAE;UACFE,YAAY,EAAE;YACZC,UAAU,EAAE;cACVC,eAAe,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,IAAI,EAAE;cAAE;YAC5C;UACF,CAAE;UACFC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UAC/BC,iBAAiB;UACjBC,0BAA0B;QAAA;UAAAnJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAER5G,OAAA,CAACtB,KAAK;QAAC2H,EAAE,EAAE;UAAEgI,MAAM,EAAE,GAAG;UAAExG,KAAK,EAAE;QAAO,CAAE;QAAArB,QAAA,gBACxCxG,OAAA,CAACnC,UAAU;UAACiP,OAAO,EAAC,IAAI;UAACzG,EAAE,EAAE;YAAEwG,CAAC,EAAE;UAAE,CAAE;UAAArG,QAAA,EAAErG,CAAC,CAAC,kBAAkB;QAAC;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC3E5G,OAAA,CAACL,QAAQ;UACPkP,IAAI,EAAEpN,SAAU;UAChBiG,OAAO,EAAEQ,YAAa;UACtBvG,OAAO,EAAEA,OAAQ;UACjBiM,KAAK,EAAE;YAAEkB,OAAO,EAAElP;UAAY,CAAE;UAChCkO,SAAS,EAAE;YACTgB,OAAO,EAAE;cACPC,eAAe,EAAE,IAAI;cACrBC,gBAAgB,EAAE;gBAAEC,UAAU,EAAE;cAAI,CAAC;cACrCC,UAAU,EAAE;gBAAEC,oBAAoB,EAAE;cAAM,CAAC;cAC3CC,YAAY,EAAE;gBAAED,oBAAoB,EAAE;cAAM;YAC9C;UACF,CAAE;UACFE,YAAY,EAAE;YACZC,UAAU,EAAE;cACVC,eAAe,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,IAAI,EAAE;cAAE;YAC3C;UACF,CAAE;UACFC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;UAC9BE,0BAA0B;QAAA;UAAAnJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,EAOP/E,QAAQ,iBAAI7B,OAAA,CAAC9B,QAAQ;QAACwE,IAAI,EAAEb,QAAQ,CAACa,IAAK;QAACmN,gBAAgB,EAAE,IAAK;QAACC,OAAO,EAAEA,CAAA,KAAMhO,WAAW,CAAC,IAAI,CAAE;QAACiO,YAAY,EAAE;UAAEnE,QAAQ,EAAE,QAAQ;UAAED,UAAU,EAAE;QAAS,CAAE;QAAAnF,QAAA,eAACxG,OAAA,CAAC7B,KAAK;UAAC2R,OAAO,EAAEA,CAAA,KAAMhO,WAAW,CAAC,IAAI,CAAE;UAACc,QAAQ,EAAEf,QAAQ,CAACe,QAAS;UAACyD,EAAE,EAAE;YAAEwB,KAAK,EAAE;UAAO,CAAE;UAAArB,QAAA,EAAE3E,QAAQ,CAACc;QAAO;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAE3B,CAAC;AAAC1G,EAAA,CApbID,qBAAqB;EAAA,QACXvC,cAAc;AAAA;AAAAsS,EAAA,GADxB/P,qBAAqB;AAsb3B,eAAeA,qBAAqB;AAAC,IAAA+P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}