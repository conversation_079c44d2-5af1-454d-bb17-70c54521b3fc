{"ast": null, "code": "var _jsxFileName = \"E:\\\\Python\\\\cmder\\\\drug_dispensing_app\\\\frontend\\\\src\\\\pages\\\\SpecificDrugComparisonPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport axios from 'axios';\nimport { Box, Typography, FormControl, InputLabel, Select, MenuItem, Snackbar, Alert, Button, TextField, OutlinedInput, Checkbox, ListItemText, Grid, Paper, Card, CardContent } from '@mui/material';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport { BarChart, Bar, XAxis, YA<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';\nimport { DataGrid, GridToolbar } from '@mui/x-data-grid';\nimport dayjs from 'dayjs';\nimport * as XLSX from 'xlsx';\n\n// Interfaces\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SpecificDrugComparisonPage = () => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const [branches, setBranches] = useState([]);\n  const [regions, setRegions] = useState([]);\n  const [clinics, setClinics] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [drugs, setDrugs] = useState([]); // All drugs\n  const [filteredDrugs, setFilteredDrugs] = useState([]); // Drugs filtered by category\n\n  // Filters\n  const [compareEntityType, setCompareEntityType] = useState('clinic');\n  const [selectedIds, setSelectedIds] = useState([]);\n  const [filterCategoryId, setFilterCategoryId] = useState('');\n  const [filterDrugId, setFilterDrugId] = useState('');\n  const [filterStartDate, setFilterStartDate] = useState(dayjs().subtract(1, 'month'));\n  const [filterEndDate, setFilterEndDate] = useState(dayjs());\n\n  // Data\n  const [comparisonData, setComparisonData] = useState([]);\n  const [monthlyChartData, setMonthlyChartData] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [snackbar, setSnackbar] = useState(null);\n\n  // Fetch initial data\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const [branchesRes, regionsRes, clinicsRes, categoriesRes, drugsRes] = await Promise.all([axios.get('http://localhost:8000/branches/'), axios.get('http://localhost:8000/regions/'), axios.get('http://localhost:8000/clinics/'), axios.get('http://localhost:8000/drug-categories/'), axios.get('http://localhost:8000/drugs/')]);\n        setBranches(branchesRes.data);\n        setRegions(regionsRes.data);\n        setClinics(clinicsRes.data);\n        setCategories(categoriesRes.data);\n        setDrugs(drugsRes.data);\n      } catch (error) {\n        console.error('Error fetching initial data:', error);\n        setSnackbar({\n          open: true,\n          message: t('fetchDataError'),\n          severity: 'error'\n        });\n      }\n    };\n    fetchData();\n  }, [t]);\n\n  // Filter drugs based on selected category\n  useEffect(() => {\n    if (filterCategoryId) {\n      setFilteredDrugs(drugs.filter(drug => drug.CategoryID === filterCategoryId));\n    } else {\n      setFilteredDrugs(drugs);\n    }\n    setFilterDrugId(''); // Reset selected drug when category changes\n  }, [filterCategoryId, drugs]);\n\n  // Fetch comparison data when filters change\n  const fetchComparisonData = async () => {\n    if (selectedIds.length === 0 || !filterDrugId) {\n      setComparisonData([]);\n      return;\n    }\n    setLoading(true);\n    try {\n      const url = `http://localhost:8000/reports/comparison/${compareEntityType === 'branch' ? 'branches' : compareEntityType + 's'}`;\n      const idsParam = `${compareEntityType}_ids=${selectedIds.join(',')}`;\n      const drugParam = `drug_id=${filterDrugId}`;\n      const startDateParam = filterStartDate ? `start_month=${filterStartDate.format('YYYY-MM')}` : '';\n      const endDateParam = filterEndDate ? `end_month=${filterEndDate.format('YYYY-MM')}` : '';\n      const queryParams = [idsParam, drugParam, startDateParam, endDateParam].filter(Boolean).join('&');\n      const fullUrl = `${url}?${queryParams}`;\n      const response = await axios.get(fullUrl);\n      const data = response.data.map((item, index) => ({\n        ...item,\n        id: `${item.EntityID}-${item.DrugID}-${item.Month}-${index}` // Unique ID for DataGrid\n      }));\n      setComparisonData(data);\n\n      // Process data for monthly comparison chart\n      const monthlyAggregatedData = {};\n      data.forEach(item => {\n        if (!monthlyAggregatedData[item.Month]) {\n          monthlyAggregatedData[item.Month] = {\n            Month: item.Month\n          };\n        }\n        monthlyAggregatedData[item.Month][item.EntityName] = (monthlyAggregatedData[item.Month][item.EntityName] || 0) + item.TotalQuantity;\n      });\n      setMonthlyChartData(Object.values(monthlyAggregatedData));\n    } catch (error) {\n      console.error('Error fetching report data:', error);\n      setSnackbar({\n        open: true,\n        message: t('fetchDataError'),\n        severity: 'error'\n      });\n      setComparisonData([]);\n      setMonthlyChartData([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleResetFilters = () => {\n    setCompareEntityType('clinic');\n    setSelectedIds([]);\n    setFilterCategoryId('');\n    setFilterDrugId('');\n    setFilterStartDate(dayjs().subtract(1, 'year'));\n    setFilterEndDate(dayjs());\n    setComparisonData([]);\n  };\n  const kpiData = useMemo(() => {\n    return comparisonData.reduce((acc, item) => {\n      acc.totalCost += item.TotalCost;\n      acc.totalQuantity += item.TotalQuantity;\n      acc.totalCases += item.NumberOfCases;\n      return acc;\n    }, {\n      totalCost: 0,\n      totalQuantity: 0,\n      totalCases: 0\n    });\n  }, [comparisonData]);\n  const entityComparisonData = useMemo(() => {\n    const data = comparisonData.reduce((acc, item) => {\n      const entity = item.EntityName;\n      if (!acc[entity]) {\n        acc[entity] = {\n          name: entity,\n          TotalCost: 0,\n          TotalQuantity: 0,\n          NumberOfCases: 0\n        };\n      }\n      acc[entity].TotalCost += item.TotalCost;\n      acc[entity].TotalQuantity += item.TotalQuantity;\n      acc[entity].NumberOfCases += item.NumberOfCases;\n      return acc;\n    }, {});\n    return Object.values(data);\n  }, [comparisonData]);\n  const renderEntitySelection = () => {\n    const items = compareEntityType === 'clinic' ? clinics : compareEntityType === 'region' ? regions : branches;\n    const label = t(`select${compareEntityType.charAt(0).toUpperCase() + compareEntityType.slice(1)}s`);\n    return /*#__PURE__*/_jsxDEV(FormControl, {\n      sx: {\n        minWidth: 240,\n        maxWidth: 400\n      },\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        multiple: true,\n        value: selectedIds,\n        onChange: e => setSelectedIds(e.target.value),\n        input: /*#__PURE__*/_jsxDEV(OutlinedInput, {\n          label: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 18\n        }, this),\n        renderValue: selected => selected.map(id => {\n          var _items$find;\n          return (_items$find = items.find(i => i.id === id)) === null || _items$find === void 0 ? void 0 : _items$find.name;\n        }).join(', '),\n        children: items.map(item => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: item.id,\n          children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n            checked: selectedIds.indexOf(item.id) > -1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this);\n  };\n  const columns = [{\n    field: 'Month',\n    headerName: t('month'),\n    width: 100\n  }, {\n    field: 'EntityName',\n    headerName: t('entityName'),\n    width: 180\n  }, {\n    field: 'DrugName',\n    headerName: t('drugName'),\n    width: 150\n  }, {\n    field: 'CategoryName',\n    headerName: t('categoryName'),\n    width: 150\n  }, {\n    field: 'TotalQuantity',\n    headerName: t('totalQuantity'),\n    type: 'number',\n    width: 130\n  }, {\n    field: 'TotalCost',\n    headerName: t('totalCost'),\n    type: 'number',\n    width: 130,\n    valueFormatter: params => params.value.toFixed(2)\n  }, {\n    field: 'NumberOfCases',\n    headerName: t('numberOfCases'),\n    type: 'number',\n    width: 130\n  }];\n  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#AF19FF', '#FF1919'];\n  const renderEntityName = id => {\n    var _clinics$find, _regions$find, _branches$find;\n    if (compareEntityType === 'clinic') return ((_clinics$find = clinics.find(c => c.id === id)) === null || _clinics$find === void 0 ? void 0 : _clinics$find.name) || String(id);\n    if (compareEntityType === 'region') return ((_regions$find = regions.find(r => r.id === id)) === null || _regions$find === void 0 ? void 0 : _regions$find.name) || String(id);\n    if (compareEntityType === 'branch') return ((_branches$find = branches.find(b => b.id === id)) === null || _branches$find === void 0 ? void 0 : _branches$find.name) || String(id);\n    return String(id);\n  };\n  const handleExportExcel = () => {\n    if (comparisonData.length === 0) {\n      setSnackbar({\n        open: true,\n        message: t('noDataToExport'),\n        severity: 'error'\n      });\n      return;\n    }\n    const dataToExport = comparisonData.map(row => ({\n      [t('month')]: row.Month,\n      [t('entityName')]: row.EntityName,\n      [t('drugName')]: row.DrugName,\n      [t('categoryName')]: row.CategoryName,\n      [t('totalQuantity')]: row.TotalQuantity,\n      [t('totalCost')]: row.TotalCost,\n      [t('numberOfCases')]: row.NumberOfCases\n    }));\n\n    // Create worksheet with title\n    const reportTitle = t('specificDrugComparison');\n    const aoa = [];\n    aoa.push([reportTitle]); // Main title\n    aoa.push([]); // Spacer row\n\n    // Add headers\n    const headers = Object.keys(dataToExport[0]);\n    aoa.push(headers);\n\n    // Add data rows\n    dataToExport.forEach(row => {\n      aoa.push(Object.values(row));\n    });\n    const ws = XLSX.utils.aoa_to_sheet(aoa);\n    const wb = XLSX.utils.book_new();\n\n    // Calculate column widths\n    const colWidths = headers.map(header => {\n      let maxWidth = header.length;\n      dataToExport.forEach(row => {\n        const cellValue = String(row[header]);\n        maxWidth = Math.max(maxWidth, cellValue.length);\n      });\n      return {\n        wch: Math.min(maxWidth + 2, 50)\n      }; // Add padding and cap at 50\n    });\n    ws['!cols'] = colWidths;\n\n    // Style main title (first row)\n    const mainTitleCell = XLSX.utils.encode_cell({\n      r: 0,\n      c: 0\n    });\n    if (!ws[mainTitleCell]) ws[mainTitleCell] = {\n      v: reportTitle\n    };\n    ws[mainTitleCell].s = {\n      font: {\n        bold: true,\n        size: 16,\n        color: {\n          rgb: \"FF000080\"\n        }\n      },\n      alignment: {\n        horizontal: \"center\",\n        vertical: \"center\"\n      },\n      fill: {\n        fgColor: {\n          rgb: \"FFE6F3FF\"\n        }\n      },\n      // Light blue background\n      border: {\n        top: {\n          style: \"medium\",\n          color: {\n            rgb: \"FF000080\"\n          }\n        },\n        bottom: {\n          style: \"medium\",\n          color: {\n            rgb: \"FF000080\"\n          }\n        },\n        left: {\n          style: \"medium\",\n          color: {\n            rgb: \"FF000080\"\n          }\n        },\n        right: {\n          style: \"medium\",\n          color: {\n            rgb: \"FF000080\"\n          }\n        }\n      }\n    };\n\n    // Merge title cell across all columns\n    const titleMerge = {\n      s: {\n        r: 0,\n        c: 0\n      },\n      e: {\n        r: 0,\n        c: headers.length - 1\n      }\n    };\n    if (!ws['!merges']) ws['!merges'] = [];\n    ws['!merges'].push(titleMerge);\n\n    // Style headers (row 2, index 2)\n    const headerRowIndex = 2;\n    for (let c = 0; c < headers.length; c++) {\n      const cellAddress = XLSX.utils.encode_cell({\n        r: headerRowIndex,\n        c: c\n      });\n      if (!ws[cellAddress]) ws[cellAddress] = {\n        v: headers[c]\n      };\n      ws[cellAddress].s = {\n        font: {\n          bold: true,\n          size: 11,\n          color: {\n            rgb: \"FFFFFFFF\"\n          }\n        },\n        // White text\n        alignment: {\n          horizontal: \"center\",\n          vertical: \"center\"\n        },\n        fill: {\n          fgColor: {\n            rgb: \"FF4472C4\"\n          }\n        },\n        // Blue background\n        border: {\n          top: {\n            style: \"medium\",\n            color: {\n              rgb: \"FF2E4057\"\n            }\n          },\n          bottom: {\n            style: \"medium\",\n            color: {\n              rgb: \"FF2E4057\"\n            }\n          },\n          left: {\n            style: \"medium\",\n            color: {\n              rgb: \"FF2E4057\"\n            }\n          },\n          right: {\n            style: \"medium\",\n            color: {\n              rgb: \"FF2E4057\"\n            }\n          }\n        }\n      };\n    }\n\n    // Style data rows (starting from row 3, index 3)\n    for (let r = 3; r < aoa.length; r++) {\n      for (let c = 0; c < headers.length; c++) {\n        const cellAddress = XLSX.utils.encode_cell({\n          r: r,\n          c: c\n        });\n        if (!ws[cellAddress]) ws[cellAddress] = {\n          v: aoa[r][c]\n        };\n\n        // Alternate row colors for better readability\n        const isEvenRow = (r - 3) % 2 === 0;\n        ws[cellAddress].s = {\n          font: {\n            size: 10\n          },\n          alignment: {\n            horizontal: \"center\",\n            vertical: \"center\"\n          },\n          fill: {\n            fgColor: {\n              rgb: isEvenRow ? \"FFF8F9FA\" : \"FFFFFFFF\"\n            }\n          },\n          // Alternating light gray and white\n          border: {\n            top: {\n              style: \"thin\",\n              color: {\n                rgb: \"FFD0D0D0\"\n              }\n            },\n            bottom: {\n              style: \"thin\",\n              color: {\n                rgb: \"FFD0D0D0\"\n              }\n            },\n            left: {\n              style: \"thin\",\n              color: {\n                rgb: \"FFD0D0D0\"\n              }\n            },\n            right: {\n              style: \"thin\",\n              color: {\n                rgb: \"FFD0D0D0\"\n              }\n            }\n          }\n        };\n      }\n    }\n    XLSX.utils.book_append_sheet(wb, ws, \"تقرير مقارنة الأدوية\");\n    XLSX.writeFile(wb, \"تقرير_مقارنة_الأدوية_المحددة.xlsx\", {\n      cellStyles: true\n    });\n  };\n  const handleExportPdf = async () => {\n    if (comparisonData.length === 0) {\n      setSnackbar({\n        open: true,\n        message: t('noDataToExport'),\n        severity: 'error'\n      });\n      return;\n    }\n    try {\n      var _link$parentNode;\n      const reportTitle = t('specificDrugComparisonReport');\n      const dataToExport = comparisonData.map(item => ({\n        [t('month')]: item.Month,\n        [t('entityName')]: item.EntityName,\n        [t('drugName')]: item.DrugName,\n        [t('categoryName')]: item.CategoryName,\n        [t('totalQuantity')]: item.TotalQuantity,\n        [t('totalCost')]: item.TotalCost.toFixed(2),\n        [t('numberOfCases')]: item.NumberOfCases\n      }));\n      const response = await axios.post('http://localhost:8000/generate-pdf-report', dataToExport,\n      // Send the array directly\n      {\n        responseType: 'blob',\n        // Important for receiving binary data\n        params: {\n          title: reportTitle\n        } // Send title as a query parameter\n      });\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', 'specific-drug-comparison-report.pdf');\n      document.body.appendChild(link);\n      link.click();\n      (_link$parentNode = link.parentNode) === null || _link$parentNode === void 0 ? void 0 : _link$parentNode.removeChild(link);\n      setSnackbar({\n        open: true,\n        message: t('pdfExportSuccess'),\n        severity: 'success'\n      });\n    } catch (error) {\n      console.error('Error exporting PDF:', error);\n      setSnackbar({\n        open: true,\n        message: t('pdfExportError'),\n        severity: 'error'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n    dateAdapter: AdapterDayjs,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      id: \"specific-drug-report-content\",\n      children: [\" \", /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: t('specificDrugComparisonReport')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: t('compareBy')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 69\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: compareEntityType,\n                label: t('compareBy'),\n                onChange: e => {\n                  setCompareEntityType(e.target.value);\n                  setSelectedIds([]);\n                },\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"clinic\",\n                  children: t('clinics')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"region\",\n                  children: t('regions')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"branch\",\n                  children: t('branches')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 110\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: renderEntitySelection()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: t('filterByCategory')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 69\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filterCategoryId,\n                label: t('filterByCategory'),\n                onChange: e => setFilterCategoryId(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: t('allCategories')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 244\n                }, this), categories.map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: cat.CategoryID,\n                  children: cat.CategoryName\n                }, cat.CategoryID, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 319\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 117\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: t('filterByDrug')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 69\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filterDrugId,\n                label: t('filterByDrug'),\n                onChange: e => setFilterDrugId(e.target.value),\n                disabled: filteredDrugs.length === 0 && filterCategoryId !== '',\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: t('allDrugs')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 293\n                }, this), filteredDrugs.map(drug => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: drug.DrugID,\n                  children: drug.DrugName\n                }, drug.DrugID, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 367\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 113\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(DatePicker, {\n              label: t('filterByStartMonth'),\n              views: ['year', 'month'],\n              openTo: \"month\",\n              value: filterStartDate,\n              onChange: setFilterStartDate,\n              slots: {\n                textField: TextField\n              },\n              slotProps: {\n                textField: {\n                  fullWidth: true\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(DatePicker, {\n              label: t('filterByEndMonth'),\n              views: ['year', 'month'],\n              openTo: \"month\",\n              value: filterEndDate,\n              onChange: setFilterEndDate,\n              slots: {\n                textField: TextField\n              },\n              slotProps: {\n                textField: {\n                  fullWidth: true\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: fetchComparisonData,\n              disabled: loading || selectedIds.length === 0 || !filterDrugId,\n              children: t('applyFilters')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"secondary\",\n              onClick: handleResetFilters,\n              children: t('resetFilters')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              onClick: handleExportExcel,\n              disabled: comparisonData.length === 0,\n              children: t('exportToExcel')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              onClick: handleExportPdf,\n              disabled: comparisonData.length === 0,\n              children: t('exportToPDF')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: t('totalCost')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 65\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: kpiData.totalCost.toLocaleString(undefined, {\n                  minimumFractionDigits: 2,\n                  maximumFractionDigits: 2\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 141\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: t('totalQuantity')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 65\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: kpiData.totalQuantity.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 145\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: t('numberOfCases')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 65\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: kpiData.totalCases.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 145\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: t('avgCostPerCase')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 65\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: (kpiData.totalCases > 0 ? kpiData.totalCost / kpiData.totalCases : 0).toLocaleString(undefined, {\n                  minimumFractionDigits: 2,\n                  maximumFractionDigits: 2\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 146\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: 400\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: t('entityComparison')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 64\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: entityComparisonData,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 184\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 223\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 247\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 256\n                }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 267\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"TotalCost\",\n                  fill: \"#82ca9d\",\n                  name: t('totalCost')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 277\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"TotalQuantity\",\n                  fill: \"#8884d8\",\n                  name: t('totalQuantity')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 341\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 146\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 125\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 30\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this), monthlyChartData.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: 400,\n              mt: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: t('monthlyComparison')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 73\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: monthlyChartData,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 190\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"Month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 229\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 254\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 263\n                }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 274\n                }, this), selectedIds.map((id, index) => /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: renderEntityName(id),\n                  fill: COLORS[index % COLORS.length],\n                  name: renderEntityName(id)\n                }, id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 156\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 135\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 32\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          height: 600,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            p: 2\n          },\n          children: t('detailedComparisonData')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: comparisonData,\n          columns: columns,\n          loading: loading,\n          slots: {\n            toolbar: GridToolbar\n          },\n          slotProps: {\n            toolbar: {\n              showQuickFilter: true,\n              quickFilterProps: {\n                debounceMs: 500\n              },\n              csvOptions: {\n                disableToolbarButton: false\n              },\n              printOptions: {\n                disableToolbarButton: false\n              }\n              // pdfExportOptions: { disableToolbarButton: false }, // This might be for Pro/Premium\n            }\n          },\n          initialState: {\n            pagination: {\n              paginationModel: {\n                pageSize: 100,\n                page: 0\n              }\n            }\n          },\n          pageSizeOptions: [10, 50, 100],\n          checkboxSelection: true,\n          disableRowSelectionOnClick: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      onClick: () => window.print(),\n      sx: {\n        mt: 2\n      },\n      children: t('printReport')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar === null || snackbar === void 0 ? void 0 : snackbar.open,\n      autoHideDuration: 6000,\n      onClose: () => setSnackbar(null),\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'left'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: () => setSnackbar(null),\n        severity: snackbar === null || snackbar === void 0 ? void 0 : snackbar.severity,\n        sx: {\n          width: '100%'\n        },\n        children: snackbar === null || snackbar === void 0 ? void 0 : snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 381,\n    columnNumber: 5\n  }, this);\n};\n_s(SpecificDrugComparisonPage, \"x0p0TEhv1ZZ8ouhlTxz9cho90+c=\", false, function () {\n  return [useTranslation];\n});\n_c = SpecificDrugComparisonPage;\nexport default SpecificDrugComparisonPage;\nvar _c;\n$RefreshReg$(_c, \"SpecificDrugComparisonPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useTranslation", "axios", "Box", "Typography", "FormControl", "InputLabel", "Select", "MenuItem", "Snackbar", "<PERSON><PERSON>", "<PERSON><PERSON>", "TextField", "OutlinedInput", "Checkbox", "ListItemText", "Grid", "Paper", "Card", "<PERSON><PERSON><PERSON><PERSON>", "DatePicker", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LocalizationProvider", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "DataGrid", "GridToolbar", "dayjs", "XLSX", "jsxDEV", "_jsxDEV", "SpecificDrugComparisonPage", "_s", "t", "branches", "setBranches", "regions", "setRegions", "clinics", "setClinics", "categories", "setCategories", "drugs", "setDrugs", "filteredDrugs", "setFilteredDrugs", "compareEntityType", "setCompareEntityType", "selectedIds", "setSelectedIds", "filterCategoryId", "setFilterCategoryId", "filterDrugId", "setFilterDrugId", "filterStartDate", "setFilterStartDate", "subtract", "filterEndDate", "setFilterEndDate", "comparisonData", "setComparisonData", "monthlyChartData", "setMonthlyChartData", "loading", "setLoading", "snackbar", "setSnackbar", "fetchData", "branchesRes", "regionsRes", "clinicsRes", "categoriesRes", "drugsRes", "Promise", "all", "get", "data", "error", "console", "open", "message", "severity", "filter", "drug", "CategoryID", "fetchComparisonData", "length", "url", "idsParam", "join", "drugParam", "startDateParam", "format", "endDateParam", "queryParams", "Boolean", "fullUrl", "response", "map", "item", "index", "id", "EntityID", "DrugID", "Month", "monthlyAggregatedData", "for<PERSON>ach", "EntityName", "TotalQuantity", "Object", "values", "handleResetFilters", "kpiData", "reduce", "acc", "totalCost", "TotalCost", "totalQuantity", "totalCases", "NumberOfCases", "entityComparisonData", "entity", "name", "renderEntitySelection", "items", "label", "char<PERSON>t", "toUpperCase", "slice", "sx", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "multiple", "value", "onChange", "e", "target", "input", "renderValue", "selected", "_items$find", "find", "i", "checked", "indexOf", "primary", "columns", "field", "headerName", "width", "type", "valueFormatter", "params", "toFixed", "COLORS", "renderEntityName", "_clinics$find", "_regions$find", "_branches$find", "c", "String", "r", "b", "handleExportExcel", "dataToExport", "row", "DrugName", "CategoryName", "reportTitle", "aoa", "push", "headers", "keys", "ws", "utils", "aoa_to_sheet", "wb", "book_new", "col<PERSON><PERSON><PERSON>", "header", "cellValue", "Math", "max", "wch", "min", "mainTitleCell", "encode_cell", "v", "s", "font", "bold", "size", "color", "rgb", "alignment", "horizontal", "vertical", "fill", "fgColor", "border", "top", "style", "bottom", "left", "right", "titleMerge", "headerRowIndex", "cellAddress", "isEvenRow", "book_append_sheet", "writeFile", "cellStyles", "handleExportPdf", "_link$parentNode", "post", "responseType", "title", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "dateAdapter", "p", "variant", "gutterBottom", "mb", "container", "spacing", "alignItems", "xs", "sm", "md", "fullWidth", "cat", "disabled", "views", "openTo", "slots", "textField", "slotProps", "onClick", "toLocaleString", "undefined", "minimumFractionDigits", "maximumFractionDigits", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "mt", "rows", "toolbar", "showQuickFilter", "quickFilterProps", "debounceMs", "csvOptions", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "printOptions", "initialState", "pagination", "paginationModel", "pageSize", "page", "pageSizeOptions", "checkboxSelection", "disableRowSelectionOnClick", "print", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["E:/Python/cmder/drug_dispensing_app/frontend/src/pages/SpecificDrugComparisonPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useMemo } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport axios from 'axios';\nimport {\n  Box,\n  Typography,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Snackbar,\n  Alert,\n  Button,\n  TextField,\n  OutlinedInput,\n  Checkbox,\n  ListItemText,\n  Grid,\n  Paper,\n  Card,\n  CardContent,\n} from '@mui/material';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { DataGrid, GridColDef, GridToolbar } from '@mui/x-data-grid';\nimport dayjs, { Dayjs } from 'dayjs';\nimport * as XLSX from 'xlsx';\n\n// Interfaces\ninterface Branch { id: number; name: string; }\ninterface Region { id: number; name: string; branch_id: number; }\ninterface Clinic { id: number; name: string; region_id: number; }\ninterface DrugCategory { CategoryID: number; CategoryName: string; }\ninterface Drug { DrugID: number; DrugName: string; CategoryID: number; Unit: string | null; }\ninterface ComparisonDispensedDrugData {\n  id: string; // Required for DataGrid\n  Month: string; // Added for monthly comparison\n  EntityID: number;\n  EntityName: string;\n  DrugID: number;\n  DrugName: string;\n  DrugUnit: string | null;\n  CategoryID: number;\n  CategoryName: string;\n  TotalQuantity: number;\n  TotalCost: number;\n  NumberOfCases: number;\n}\n\nconst SpecificDrugComparisonPage = () => {\n  const { t } = useTranslation();\n  const [branches, setBranches] = useState<Branch[]>([]);\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [clinics, setClinics] = useState<Clinic[]>([]);\n  const [categories, setCategories] = useState<DrugCategory[]>([]);\n  const [drugs, setDrugs] = useState<Drug[]>([]); // All drugs\n  const [filteredDrugs, setFilteredDrugs] = useState<Drug[]>([]); // Drugs filtered by category\n\n  // Filters\n  const [compareEntityType, setCompareEntityType] = useState<'clinic' | 'region' | 'branch'>('clinic');\n  const [selectedIds, setSelectedIds] = useState<number[]>([]);\n  const [filterCategoryId, setFilterCategoryId] = useState<number | ''>('');\n  const [filterDrugId, setFilterDrugId] = useState<number | ''>('');\n  const [filterStartDate, setFilterStartDate] = useState<Dayjs | null>(dayjs().subtract(1, 'month'));\n  const [filterEndDate, setFilterEndDate] = useState<Dayjs | null>(dayjs());\n\n  // Data\n  const [comparisonData, setComparisonData] = useState<ComparisonDispensedDrugData[]>([]);\n  const [monthlyChartData, setMonthlyChartData] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);\n\n  // Fetch initial data\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const [branchesRes, regionsRes, clinicsRes, categoriesRes, drugsRes] = await Promise.all([\n          axios.get<Branch[]>('http://localhost:8000/branches/'),\n          axios.get<Region[]>('http://localhost:8000/regions/'),\n          axios.get<Clinic[]>('http://localhost:8000/clinics/'),\n          axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/'),\n          axios.get<Drug[]>('http://localhost:8000/drugs/')\n        ]);\n        setBranches(branchesRes.data);\n        setRegions(regionsRes.data);\n        setClinics(clinicsRes.data);\n        setCategories(categoriesRes.data);\n        setDrugs(drugsRes.data);\n      } catch (error) {\n        console.error('Error fetching initial data:', error);\n        setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });\n      }\n    };\n    fetchData();\n  }, [t]);\n\n  // Filter drugs based on selected category\n  useEffect(() => {\n    if (filterCategoryId) {\n      setFilteredDrugs(drugs.filter(drug => drug.CategoryID === filterCategoryId));\n    } else {\n      setFilteredDrugs(drugs);\n    }\n    setFilterDrugId(''); // Reset selected drug when category changes\n  }, [filterCategoryId, drugs]);\n\n  // Fetch comparison data when filters change\n  const fetchComparisonData = async () => {\n    if (selectedIds.length === 0 || !filterDrugId) {\n      setComparisonData([]);\n      return;\n    }\n    setLoading(true);\n    try {\n      const url = `http://localhost:8000/reports/comparison/${compareEntityType === 'branch' ? 'branches' : compareEntityType + 's'}`;\n      const idsParam = `${compareEntityType}_ids=${selectedIds.join(',')}`;\n      const drugParam = `drug_id=${filterDrugId}`;\n      const startDateParam = filterStartDate ? `start_month=${filterStartDate.format('YYYY-MM')}` : '';\n      const endDateParam = filterEndDate ? `end_month=${filterEndDate.format('YYYY-MM')}` : '';\n      const queryParams = [idsParam, drugParam, startDateParam, endDateParam].filter(Boolean).join('&');\n      const fullUrl = `${url}?${queryParams}`;\n      const response = await axios.get<ComparisonDispensedDrugData[]>(fullUrl);\n      \n      const data = response.data.map((item, index) => ({\n        ...item,\n        id: `${item.EntityID}-${item.DrugID}-${item.Month}-${index}` // Unique ID for DataGrid\n      }));\n      setComparisonData(data);\n\n      // Process data for monthly comparison chart\n      const monthlyAggregatedData: { [key: string]: any } = {};\n      data.forEach(item => {\n        if (!monthlyAggregatedData[item.Month]) {\n          monthlyAggregatedData[item.Month] = { Month: item.Month };\n        }\n        monthlyAggregatedData[item.Month][item.EntityName] = (\n          monthlyAggregatedData[item.Month][item.EntityName] || 0\n        ) + item.TotalQuantity;\n      });\n      setMonthlyChartData(Object.values(monthlyAggregatedData));\n\n    } catch (error) {\n      console.error('Error fetching report data:', error);\n      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });\n      setComparisonData([]);\n      setMonthlyChartData([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleResetFilters = () => {\n    setCompareEntityType('clinic');\n    setSelectedIds([]);\n    setFilterCategoryId('');\n    setFilterDrugId('');\n    setFilterStartDate(dayjs().subtract(1, 'year'));\n    setFilterEndDate(dayjs());\n    setComparisonData([]);\n  };\n\n  const kpiData = useMemo(() => {\n    return comparisonData.reduce((acc, item) => {\n      acc.totalCost += item.TotalCost;\n      acc.totalQuantity += item.TotalQuantity;\n      acc.totalCases += item.NumberOfCases;\n      return acc;\n    }, { totalCost: 0, totalQuantity: 0, totalCases: 0 });\n  }, [comparisonData]);\n\n  const entityComparisonData = useMemo(() => {\n    const data = comparisonData.reduce((acc, item) => {\n        const entity = item.EntityName;\n        if (!acc[entity]) {\n            acc[entity] = { name: entity, TotalCost: 0, TotalQuantity: 0, NumberOfCases: 0 };\n        }\n        acc[entity].TotalCost += item.TotalCost;\n        acc[entity].TotalQuantity += item.TotalQuantity;\n        acc[entity].NumberOfCases += item.NumberOfCases;\n        return acc;\n    }, {} as { [key: string]: { name: string, TotalCost: number, TotalQuantity: number, NumberOfCases: number } });\n    return Object.values(data);\n}, [comparisonData]);\n\n  const renderEntitySelection = () => {\n    const items = compareEntityType === 'clinic' ? clinics : compareEntityType === 'region' ? regions : branches;\n    const label = t(`select${compareEntityType.charAt(0).toUpperCase() + compareEntityType.slice(1)}s`);\n    return (\n      <FormControl sx={{ minWidth: 240, maxWidth: 400 }}>\n        <InputLabel>{label}</InputLabel>\n        <Select\n          multiple\n          value={selectedIds}\n          onChange={(e) => setSelectedIds(e.target.value as number[])}\n          input={<OutlinedInput label={label} />}\n          renderValue={(selected) => selected.map(id => items.find(i => i.id === id)?.name).join(', ')}\n        >\n          {items.map((item) => (\n            <MenuItem key={item.id} value={item.id}>\n              <Checkbox checked={selectedIds.indexOf(item.id) > -1} />\n              <ListItemText primary={item.name} />\n            </MenuItem>\n          ))}\n        </Select>\n      </FormControl>\n    );\n  };\n\n  const columns: GridColDef[] = [\n    { field: 'Month', headerName: t('month'), width: 100 },\n    { field: 'EntityName', headerName: t('entityName'), width: 180 },\n    { field: 'DrugName', headerName: t('drugName'), width: 150 },\n    { field: 'CategoryName', headerName: t('categoryName'), width: 150 },\n    { field: 'TotalQuantity', headerName: t('totalQuantity'), type: 'number', width: 130 },\n    { field: 'TotalCost', headerName: t('totalCost'), type: 'number', width: 130, valueFormatter: (params: { value: number }) => params.value.toFixed(2) },\n    { field: 'NumberOfCases', headerName: t('numberOfCases'), type: 'number', width: 130 },\n  ];\n\n  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#AF19FF', '#FF1919'];\n\n  const renderEntityName = (id: number): string => {\n    if (compareEntityType === 'clinic') return clinics.find(c => c.id === id)?.name || String(id);\n    if (compareEntityType === 'region') return regions.find(r => r.id === id)?.name || String(id);\n    if (compareEntityType === 'branch') return branches.find(b => b.id === id)?.name || String(id);\n    return String(id);\n  };\n\n  const handleExportExcel = () => {\n    if (comparisonData.length === 0) {\n      setSnackbar({ open: true, message: t('noDataToExport'), severity: 'error' });\n      return;\n    }\n\n    const dataToExport = comparisonData.map(row => ({\n      [t('month')]: row.Month,\n      [t('entityName')]: row.EntityName,\n      [t('drugName')]: row.DrugName,\n      [t('categoryName')]: row.CategoryName,\n      [t('totalQuantity')]: row.TotalQuantity,\n      [t('totalCost')]: row.TotalCost,\n      [t('numberOfCases')]: row.NumberOfCases,\n    }));\n\n    // Create worksheet with title\n    const reportTitle = t('specificDrugComparison');\n    const aoa: (string | number)[][] = [];\n    aoa.push([reportTitle]); // Main title\n    aoa.push([]); // Spacer row\n\n    // Add headers\n    const headers = Object.keys(dataToExport[0]);\n    aoa.push(headers);\n\n    // Add data rows\n    dataToExport.forEach(row => {\n      aoa.push(Object.values(row));\n    });\n\n    const ws = XLSX.utils.aoa_to_sheet(aoa);\n    const wb = XLSX.utils.book_new();\n\n    // Calculate column widths\n    const colWidths = headers.map(header => {\n      let maxWidth = header.length;\n      dataToExport.forEach(row => {\n        const cellValue = String(row[header]);\n        maxWidth = Math.max(maxWidth, cellValue.length);\n      });\n      return { wch: Math.min(maxWidth + 2, 50) }; // Add padding and cap at 50\n    });\n    ws['!cols'] = colWidths;\n\n    // Style main title (first row)\n    const mainTitleCell = XLSX.utils.encode_cell({ r: 0, c: 0 });\n    if (!ws[mainTitleCell]) ws[mainTitleCell] = { v: reportTitle };\n    ws[mainTitleCell].s = {\n      font: { bold: true, size: 16, color: { rgb: \"FF000080\" } },\n      alignment: { horizontal: \"center\", vertical: \"center\" },\n      fill: { fgColor: { rgb: \"FFE6F3FF\" } }, // Light blue background\n      border: {\n        top: { style: \"medium\", color: { rgb: \"FF000080\" } },\n        bottom: { style: \"medium\", color: { rgb: \"FF000080\" } },\n        left: { style: \"medium\", color: { rgb: \"FF000080\" } },\n        right: { style: \"medium\", color: { rgb: \"FF000080\" } }\n      }\n    };\n\n    // Merge title cell across all columns\n    const titleMerge = { s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } };\n    if (!ws['!merges']) ws['!merges'] = [];\n    ws['!merges'].push(titleMerge);\n\n    // Style headers (row 2, index 2)\n    const headerRowIndex = 2;\n    for (let c = 0; c < headers.length; c++) {\n      const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: c });\n      if (!ws[cellAddress]) ws[cellAddress] = { v: headers[c] };\n      ws[cellAddress].s = {\n        font: { bold: true, size: 11, color: { rgb: \"FFFFFFFF\" } }, // White text\n        alignment: { horizontal: \"center\", vertical: \"center\" },\n        fill: { fgColor: { rgb: \"FF4472C4\" } }, // Blue background\n        border: {\n          top: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n          bottom: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n          left: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n          right: { style: \"medium\", color: { rgb: \"FF2E4057\" } }\n        }\n      };\n    }\n\n    // Style data rows (starting from row 3, index 3)\n    for (let r = 3; r < aoa.length; r++) {\n      for (let c = 0; c < headers.length; c++) {\n        const cellAddress = XLSX.utils.encode_cell({ r: r, c: c });\n        if (!ws[cellAddress]) ws[cellAddress] = { v: aoa[r][c] };\n\n        // Alternate row colors for better readability\n        const isEvenRow = (r - 3) % 2 === 0;\n        ws[cellAddress].s = {\n          font: { size: 10 },\n          alignment: { horizontal: \"center\", vertical: \"center\" },\n          fill: { fgColor: { rgb: isEvenRow ? \"FFF8F9FA\" : \"FFFFFFFF\" } }, // Alternating light gray and white\n          border: {\n            top: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } },\n            bottom: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } },\n            left: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } },\n            right: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } }\n          }\n        };\n      }\n    }\n\n    XLSX.utils.book_append_sheet(wb, ws, \"تقرير مقارنة الأدوية\");\n    XLSX.writeFile(wb, \"تقرير_مقارنة_الأدوية_المحددة.xlsx\", { cellStyles: true });\n  };\n\n  const handleExportPdf = async () => {\n    if (comparisonData.length === 0) {\n      setSnackbar({ open: true, message: t('noDataToExport'), severity: 'error' });\n      return;\n    }\n\n    try {\n      const reportTitle = t('specificDrugComparisonReport');\n      const dataToExport = comparisonData.map(item => ({\n        [t('month')]: item.Month,\n        [t('entityName')]: item.EntityName,\n        [t('drugName')]: item.DrugName,\n        [t('categoryName')]: item.CategoryName,\n        [t('totalQuantity')]: item.TotalQuantity,\n        [t('totalCost')]: item.TotalCost.toFixed(2),\n        [t('numberOfCases')]: item.NumberOfCases,\n      }));\n\n      const response = await axios.post(\n        'http://localhost:8000/generate-pdf-report',\n        dataToExport, // Send the array directly\n        {\n          responseType: 'blob', // Important for receiving binary data\n          params: { title: reportTitle } // Send title as a query parameter\n        }\n      );\n\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', 'specific-drug-comparison-report.pdf');\n      document.body.appendChild(link);\n      link.click();\n      link.parentNode?.removeChild(link);\n      setSnackbar({ open: true, message: t('pdfExportSuccess'), severity: 'success' });\n    } catch (error) {\n      console.error('Error exporting PDF:', error);\n      setSnackbar({ open: true, message: t('pdfExportError'), severity: 'error' });\n    }\n  };\n\n  return (\n    <LocalizationProvider dateAdapter={AdapterDayjs}>\n      <Box sx={{ p: 3 }} id=\"specific-drug-report-content\"> {/* Added ID here */}\n        <Typography variant=\"h4\" gutterBottom>{t('specificDrugComparisonReport')}</Typography>\n        \n        {/* --- FILTERS --- */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth><InputLabel>{t('compareBy')}</InputLabel><Select value={compareEntityType} label={t('compareBy')} onChange={(e) => { setCompareEntityType(e.target.value as any); setSelectedIds([]); }}>\n              <MenuItem value=\"clinic\">{t('clinics')}</MenuItem>\n              <MenuItem value=\"region\">{t('regions')}</MenuItem>\n              <MenuItem value=\"branch\">{t('branches')}</MenuItem>\n            </Select></FormControl></Grid>\n            <Grid item xs={12} sm={6} md={3}>{renderEntitySelection()}</Grid>\n            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth><InputLabel>{t('filterByCategory')}</InputLabel><Select value={filterCategoryId} label={t('filterByCategory')} onChange={(e) => setFilterCategoryId(e.target.value as number)}><MenuItem value=\"\">{t('allCategories')}</MenuItem>{categories.map((cat) => <MenuItem key={cat.CategoryID} value={cat.CategoryID}>{cat.CategoryName}</MenuItem>)}</Select></FormControl></Grid>\n            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth><InputLabel>{t('filterByDrug')}</InputLabel><Select value={filterDrugId} label={t('filterByDrug')} onChange={(e) => setFilterDrugId(e.target.value as number)} disabled={filteredDrugs.length === 0 && filterCategoryId !== ''}><MenuItem value=\"\">{t('allDrugs')}</MenuItem>{filteredDrugs.map((drug) => <MenuItem key={drug.DrugID} value={drug.DrugID}>{drug.DrugName}</MenuItem>)}</Select></FormControl></Grid>\n            <Grid item xs={12} sm={6} md={3}><DatePicker label={t('filterByStartMonth')} views={['year', 'month']} openTo=\"month\" value={filterStartDate} onChange={setFilterStartDate} slots={{ textField: TextField }} slotProps={{ textField: { fullWidth: true } }} /></Grid>\n            <Grid item xs={12} sm={6} md={3}><DatePicker label={t('filterByEndMonth')} views={['year', 'month']} openTo=\"month\" value={filterEndDate} onChange={setFilterEndDate} slots={{ textField: TextField }} slotProps={{ textField: { fullWidth: true } }} /></Grid>\n            <Grid item xs={12} sm={6} md={3}><Button variant=\"contained\" color=\"primary\" onClick={fetchComparisonData} disabled={loading || selectedIds.length === 0 || !filterDrugId}>{t('applyFilters')}</Button></Grid>\n            <Grid item xs={12} sm={6} md={3}><Button variant=\"outlined\" color=\"secondary\" onClick={handleResetFilters}>{t('resetFilters')}</Button></Grid>\n            <Grid item xs={12} sm={6} md={3}><Button variant=\"contained\" color=\"success\" onClick={handleExportExcel} disabled={comparisonData.length === 0}>{t('exportToExcel')}</Button></Grid>\n            <Grid item xs={12} sm={6} md={3}><Button variant=\"contained\" color=\"error\" onClick={handleExportPdf} disabled={comparisonData.length === 0}>{t('exportToPDF')}</Button></Grid>\n          </Grid>\n        </Paper>\n\n        {/* --- KPIs --- */}\n        <Grid container spacing={3} sx={{ mb: 3 }}>\n            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color=\"textSecondary\" gutterBottom>{t('totalCost')}</Typography><Typography variant=\"h5\">{kpiData.totalCost.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Typography></CardContent></Card></Grid>\n            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color=\"textSecondary\" gutterBottom>{t('totalQuantity')}</Typography><Typography variant=\"h5\">{kpiData.totalQuantity.toLocaleString()}</Typography></CardContent></Card></Grid>\n            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color=\"textSecondary\" gutterBottom>{t('numberOfCases')}</Typography><Typography variant=\"h5\">{kpiData.totalCases.toLocaleString()}</Typography></CardContent></Card></Grid>\n            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color=\"textSecondary\" gutterBottom>{t('avgCostPerCase')}</Typography><Typography variant=\"h5\">{(kpiData.totalCases > 0 ? kpiData.totalCost / kpiData.totalCases : 0).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Typography></CardContent></Card></Grid>\n        </Grid>\n\n        {/* --- CHARTS --- */}\n        <Grid container spacing={3} sx={{ mb: 3 }}>\n          <Grid item xs={12}><Paper sx={{ p: 2, height: 400 }}><Typography variant=\"h6\">{t('entityComparison')}</Typography><ResponsiveContainer><BarChart data={entityComparisonData}><CartesianGrid strokeDasharray=\"3 3\" /><XAxis dataKey=\"name\" /><YAxis /><Tooltip /><Legend /><Bar dataKey=\"TotalCost\" fill=\"#82ca9d\" name={t('totalCost')} /><Bar dataKey=\"TotalQuantity\" fill=\"#8884d8\" name={t('totalQuantity')} /></BarChart></ResponsiveContainer></Paper></Grid>\n          {monthlyChartData.length > 0 && (\n            <Grid item xs={12}><Paper sx={{ p: 2, height: 400, mt: 3 }}><Typography variant=\"h6\">{t('monthlyComparison')}</Typography><ResponsiveContainer><BarChart data={monthlyChartData}><CartesianGrid strokeDasharray=\"3 3\" /><XAxis dataKey=\"Month\" /><YAxis /><Tooltip /><Legend />\n                {selectedIds.map((id, index) => (\n                  <Bar key={id} dataKey={renderEntityName(id)} fill={COLORS[index % COLORS.length]} name={renderEntityName(id)} />\n                ))}\n              </BarChart></ResponsiveContainer></Paper></Grid>\n          )}\n        </Grid>\n\n        {/* --- DATA GRID --- */}\n        <Paper sx={{ height: 600, width: '100%' }}>\n          <Typography variant=\"h6\" sx={{ p: 2 }}>{t('detailedComparisonData')}</Typography>\n          <DataGrid\n            rows={comparisonData}\n            columns={columns}\n            loading={loading}\n            slots={{ toolbar: GridToolbar }}\n            slotProps={{\n              toolbar: {\n                showQuickFilter: true,\n                quickFilterProps: { debounceMs: 500 },\n                csvOptions: { disableToolbarButton: false },\n                printOptions: { disableToolbarButton: false },\n                // pdfExportOptions: { disableToolbarButton: false }, // This might be for Pro/Premium\n              },\n            }}\n            initialState={{\n              pagination: {\n                paginationModel: { pageSize: 100, page: 0 },\n              },\n            }}\n            pageSizeOptions={[10, 50, 100]}\n            checkboxSelection\n            disableRowSelectionOnClick\n          />\n        </Paper>\n      </Box>\n      <Button variant=\"outlined\" onClick={() => window.print()} sx={{ mt: 2 }}>\n        {t('printReport')}\n      </Button>\n      <Snackbar\n        open={snackbar?.open}\n        autoHideDuration={6000}\n        onClose={() => setSnackbar(null)}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}\n      >\n        <Alert onClose={() => setSnackbar(null)} severity={snackbar?.severity} sx={{ width: '100%' }}>\n          {snackbar?.message}\n        </Alert>\n      </Snackbar>\n    </LocalizationProvider>\n  );\n};\n\nexport default SpecificDrugComparisonPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC3D,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,QAAQ,EACRC,YAAY,EACZC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,WAAW,QACN,eAAe;AACtB,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAC3G,SAASC,QAAQ,EAAcC,WAAW,QAAQ,kBAAkB;AACpE,OAAOC,KAAK,MAAiB,OAAO;AACpC,OAAO,KAAKC,IAAI,MAAM,MAAM;;AAE5B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAqBA,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAE,CAAC,GAAGtC,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAW,EAAE,CAAC;EACtD,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAiB,EAAE,CAAC;EAChE,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAS,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAS,EAAE,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAM,CAACsD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvD,QAAQ,CAAiC,QAAQ,CAAC;EACpG,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAW,EAAE,CAAC;EAC5D,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAc,EAAE,CAAC;EACzE,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAc,EAAE,CAAC;EACjE,MAAM,CAAC8D,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAemC,KAAK,CAAC,CAAC,CAAC6B,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAClG,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAemC,KAAK,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAgC,EAAE,CAAC;EACvF,MAAM,CAACqE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAQ,EAAE,CAAC;EACnE,MAAM,CAACuE,OAAO,EAAEC,UAAU,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyE,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAA2E,IAAI,CAAC;;EAExH;EACAC,SAAS,CAAC,MAAM;IACd,MAAM0E,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAM,CAACC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAEC,QAAQ,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACvF9E,KAAK,CAAC+E,GAAG,CAAW,iCAAiC,CAAC,EACtD/E,KAAK,CAAC+E,GAAG,CAAW,gCAAgC,CAAC,EACrD/E,KAAK,CAAC+E,GAAG,CAAW,gCAAgC,CAAC,EACrD/E,KAAK,CAAC+E,GAAG,CAAiB,wCAAwC,CAAC,EACnE/E,KAAK,CAAC+E,GAAG,CAAS,8BAA8B,CAAC,CAClD,CAAC;QACFxC,WAAW,CAACiC,WAAW,CAACQ,IAAI,CAAC;QAC7BvC,UAAU,CAACgC,UAAU,CAACO,IAAI,CAAC;QAC3BrC,UAAU,CAAC+B,UAAU,CAACM,IAAI,CAAC;QAC3BnC,aAAa,CAAC8B,aAAa,CAACK,IAAI,CAAC;QACjCjC,QAAQ,CAAC6B,QAAQ,CAACI,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDX,WAAW,CAAC;UAAEa,IAAI,EAAE,IAAI;UAAEC,OAAO,EAAE/C,CAAC,CAAC,gBAAgB,CAAC;UAAEgD,QAAQ,EAAE;QAAQ,CAAC,CAAC;MAC9E;IACF,CAAC;IACDd,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAClC,CAAC,CAAC,CAAC;;EAEP;EACAxC,SAAS,CAAC,MAAM;IACd,IAAIyD,gBAAgB,EAAE;MACpBL,gBAAgB,CAACH,KAAK,CAACwC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,UAAU,KAAKlC,gBAAgB,CAAC,CAAC;IAC9E,CAAC,MAAM;MACLL,gBAAgB,CAACH,KAAK,CAAC;IACzB;IACAW,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;EACvB,CAAC,EAAE,CAACH,gBAAgB,EAAER,KAAK,CAAC,CAAC;;EAE7B;EACA,MAAM2C,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAIrC,WAAW,CAACsC,MAAM,KAAK,CAAC,IAAI,CAAClC,YAAY,EAAE;MAC7CQ,iBAAiB,CAAC,EAAE,CAAC;MACrB;IACF;IACAI,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMuB,GAAG,GAAG,4CAA4CzC,iBAAiB,KAAK,QAAQ,GAAG,UAAU,GAAGA,iBAAiB,GAAG,GAAG,EAAE;MAC/H,MAAM0C,QAAQ,GAAG,GAAG1C,iBAAiB,QAAQE,WAAW,CAACyC,IAAI,CAAC,GAAG,CAAC,EAAE;MACpE,MAAMC,SAAS,GAAG,WAAWtC,YAAY,EAAE;MAC3C,MAAMuC,cAAc,GAAGrC,eAAe,GAAG,eAAeA,eAAe,CAACsC,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE;MAChG,MAAMC,YAAY,GAAGpC,aAAa,GAAG,aAAaA,aAAa,CAACmC,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE;MACxF,MAAME,WAAW,GAAG,CAACN,QAAQ,EAAEE,SAAS,EAAEC,cAAc,EAAEE,YAAY,CAAC,CAACX,MAAM,CAACa,OAAO,CAAC,CAACN,IAAI,CAAC,GAAG,CAAC;MACjG,MAAMO,OAAO,GAAG,GAAGT,GAAG,IAAIO,WAAW,EAAE;MACvC,MAAMG,QAAQ,GAAG,MAAMrG,KAAK,CAAC+E,GAAG,CAAgCqB,OAAO,CAAC;MAExE,MAAMpB,IAAI,GAAGqB,QAAQ,CAACrB,IAAI,CAACsB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;QAC/C,GAAGD,IAAI;QACPE,EAAE,EAAE,GAAGF,IAAI,CAACG,QAAQ,IAAIH,IAAI,CAACI,MAAM,IAAIJ,IAAI,CAACK,KAAK,IAAIJ,KAAK,EAAE,CAAC;MAC/D,CAAC,CAAC,CAAC;MACHxC,iBAAiB,CAACgB,IAAI,CAAC;;MAEvB;MACA,MAAM6B,qBAA6C,GAAG,CAAC,CAAC;MACxD7B,IAAI,CAAC8B,OAAO,CAACP,IAAI,IAAI;QACnB,IAAI,CAACM,qBAAqB,CAACN,IAAI,CAACK,KAAK,CAAC,EAAE;UACtCC,qBAAqB,CAACN,IAAI,CAACK,KAAK,CAAC,GAAG;YAAEA,KAAK,EAAEL,IAAI,CAACK;UAAM,CAAC;QAC3D;QACAC,qBAAqB,CAACN,IAAI,CAACK,KAAK,CAAC,CAACL,IAAI,CAACQ,UAAU,CAAC,GAAG,CACnDF,qBAAqB,CAACN,IAAI,CAACK,KAAK,CAAC,CAACL,IAAI,CAACQ,UAAU,CAAC,IAAI,CAAC,IACrDR,IAAI,CAACS,aAAa;MACxB,CAAC,CAAC;MACF9C,mBAAmB,CAAC+C,MAAM,CAACC,MAAM,CAACL,qBAAqB,CAAC,CAAC;IAE3D,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDX,WAAW,CAAC;QAAEa,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE/C,CAAC,CAAC,gBAAgB,CAAC;QAAEgD,QAAQ,EAAE;MAAQ,CAAC,CAAC;MAC5ErB,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;IACzB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+C,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhE,oBAAoB,CAAC,QAAQ,CAAC;IAC9BE,cAAc,CAAC,EAAE,CAAC;IAClBE,mBAAmB,CAAC,EAAE,CAAC;IACvBE,eAAe,CAAC,EAAE,CAAC;IACnBE,kBAAkB,CAAC5B,KAAK,CAAC,CAAC,CAAC6B,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAC/CE,gBAAgB,CAAC/B,KAAK,CAAC,CAAC,CAAC;IACzBiC,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;EAED,MAAMoD,OAAO,GAAGtH,OAAO,CAAC,MAAM;IAC5B,OAAOiE,cAAc,CAACsD,MAAM,CAAC,CAACC,GAAG,EAAEf,IAAI,KAAK;MAC1Ce,GAAG,CAACC,SAAS,IAAIhB,IAAI,CAACiB,SAAS;MAC/BF,GAAG,CAACG,aAAa,IAAIlB,IAAI,CAACS,aAAa;MACvCM,GAAG,CAACI,UAAU,IAAInB,IAAI,CAACoB,aAAa;MACpC,OAAOL,GAAG;IACZ,CAAC,EAAE;MAAEC,SAAS,EAAE,CAAC;MAAEE,aAAa,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAE,CAAC,CAAC;EACvD,CAAC,EAAE,CAAC3D,cAAc,CAAC,CAAC;EAEpB,MAAM6D,oBAAoB,GAAG9H,OAAO,CAAC,MAAM;IACzC,MAAMkF,IAAI,GAAGjB,cAAc,CAACsD,MAAM,CAAC,CAACC,GAAG,EAAEf,IAAI,KAAK;MAC9C,MAAMsB,MAAM,GAAGtB,IAAI,CAACQ,UAAU;MAC9B,IAAI,CAACO,GAAG,CAACO,MAAM,CAAC,EAAE;QACdP,GAAG,CAACO,MAAM,CAAC,GAAG;UAAEC,IAAI,EAAED,MAAM;UAAEL,SAAS,EAAE,CAAC;UAAER,aAAa,EAAE,CAAC;UAAEW,aAAa,EAAE;QAAE,CAAC;MACpF;MACAL,GAAG,CAACO,MAAM,CAAC,CAACL,SAAS,IAAIjB,IAAI,CAACiB,SAAS;MACvCF,GAAG,CAACO,MAAM,CAAC,CAACb,aAAa,IAAIT,IAAI,CAACS,aAAa;MAC/CM,GAAG,CAACO,MAAM,CAAC,CAACF,aAAa,IAAIpB,IAAI,CAACoB,aAAa;MAC/C,OAAOL,GAAG;IACd,CAAC,EAAE,CAAC,CAAyG,CAAC;IAC9G,OAAOL,MAAM,CAACC,MAAM,CAAClC,IAAI,CAAC;EAC9B,CAAC,EAAE,CAACjB,cAAc,CAAC,CAAC;EAElB,MAAMgE,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,KAAK,GAAG9E,iBAAiB,KAAK,QAAQ,GAAGR,OAAO,GAAGQ,iBAAiB,KAAK,QAAQ,GAAGV,OAAO,GAAGF,QAAQ;IAC5G,MAAM2F,KAAK,GAAG5F,CAAC,CAAC,SAASa,iBAAiB,CAACgF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGjF,iBAAiB,CAACkF,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;IACnG,oBACElG,OAAA,CAAC/B,WAAW;MAACkI,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAC,QAAA,gBAChDtG,OAAA,CAAC9B,UAAU;QAAAoI,QAAA,EAAEP;MAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChC1G,OAAA,CAAC7B,MAAM;QACLwI,QAAQ;QACRC,KAAK,EAAE1F,WAAY;QACnB2F,QAAQ,EAAGC,CAAC,IAAK3F,cAAc,CAAC2F,CAAC,CAACC,MAAM,CAACH,KAAiB,CAAE;QAC5DI,KAAK,eAAEhH,OAAA,CAACvB,aAAa;UAACsH,KAAK,EAAEA;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvCO,WAAW,EAAGC,QAAQ,IAAKA,QAAQ,CAAC9C,GAAG,CAACG,EAAE;UAAA,IAAA4C,WAAA;UAAA,QAAAA,WAAA,GAAIrB,KAAK,CAACsB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKA,EAAE,CAAC,cAAA4C,WAAA,uBAA5BA,WAAA,CAA8BvB,IAAI;QAAA,EAAC,CAACjC,IAAI,CAAC,IAAI,CAAE;QAAA2C,QAAA,EAE5FR,KAAK,CAAC1B,GAAG,CAAEC,IAAI,iBACdrE,OAAA,CAAC5B,QAAQ;UAAewI,KAAK,EAAEvC,IAAI,CAACE,EAAG;UAAA+B,QAAA,gBACrCtG,OAAA,CAACtB,QAAQ;YAAC4I,OAAO,EAAEpG,WAAW,CAACqG,OAAO,CAAClD,IAAI,CAACE,EAAE,CAAC,GAAG,CAAC;UAAE;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxD1G,OAAA,CAACrB,YAAY;YAAC6I,OAAO,EAAEnD,IAAI,CAACuB;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GAFvBrC,IAAI,CAACE,EAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGZ,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAElB,CAAC;EAED,MAAMe,OAAqB,GAAG,CAC5B;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAExH,CAAC,CAAC,OAAO,CAAC;IAAEyH,KAAK,EAAE;EAAI,CAAC,EACtD;IAAEF,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAExH,CAAC,CAAC,YAAY,CAAC;IAAEyH,KAAK,EAAE;EAAI,CAAC,EAChE;IAAEF,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAExH,CAAC,CAAC,UAAU,CAAC;IAAEyH,KAAK,EAAE;EAAI,CAAC,EAC5D;IAAEF,KAAK,EAAE,cAAc;IAAEC,UAAU,EAAExH,CAAC,CAAC,cAAc,CAAC;IAAEyH,KAAK,EAAE;EAAI,CAAC,EACpE;IAAEF,KAAK,EAAE,eAAe;IAAEC,UAAU,EAAExH,CAAC,CAAC,eAAe,CAAC;IAAE0H,IAAI,EAAE,QAAQ;IAAED,KAAK,EAAE;EAAI,CAAC,EACtF;IAAEF,KAAK,EAAE,WAAW;IAAEC,UAAU,EAAExH,CAAC,CAAC,WAAW,CAAC;IAAE0H,IAAI,EAAE,QAAQ;IAAED,KAAK,EAAE,GAAG;IAAEE,cAAc,EAAGC,MAAyB,IAAKA,MAAM,CAACnB,KAAK,CAACoB,OAAO,CAAC,CAAC;EAAE,CAAC,EACtJ;IAAEN,KAAK,EAAE,eAAe;IAAEC,UAAU,EAAExH,CAAC,CAAC,eAAe,CAAC;IAAE0H,IAAI,EAAE,QAAQ;IAAED,KAAK,EAAE;EAAI,CAAC,CACvF;EAED,MAAMK,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAEjF,MAAMC,gBAAgB,GAAI3D,EAAU,IAAa;IAAA,IAAA4D,aAAA,EAAAC,aAAA,EAAAC,cAAA;IAC/C,IAAIrH,iBAAiB,KAAK,QAAQ,EAAE,OAAO,EAAAmH,aAAA,GAAA3H,OAAO,CAAC4G,IAAI,CAACkB,CAAC,IAAIA,CAAC,CAAC/D,EAAE,KAAKA,EAAE,CAAC,cAAA4D,aAAA,uBAA9BA,aAAA,CAAgCvC,IAAI,KAAI2C,MAAM,CAAChE,EAAE,CAAC;IAC7F,IAAIvD,iBAAiB,KAAK,QAAQ,EAAE,OAAO,EAAAoH,aAAA,GAAA9H,OAAO,CAAC8G,IAAI,CAACoB,CAAC,IAAIA,CAAC,CAACjE,EAAE,KAAKA,EAAE,CAAC,cAAA6D,aAAA,uBAA9BA,aAAA,CAAgCxC,IAAI,KAAI2C,MAAM,CAAChE,EAAE,CAAC;IAC7F,IAAIvD,iBAAiB,KAAK,QAAQ,EAAE,OAAO,EAAAqH,cAAA,GAAAjI,QAAQ,CAACgH,IAAI,CAACqB,CAAC,IAAIA,CAAC,CAAClE,EAAE,KAAKA,EAAE,CAAC,cAAA8D,cAAA,uBAA/BA,cAAA,CAAiCzC,IAAI,KAAI2C,MAAM,CAAChE,EAAE,CAAC;IAC9F,OAAOgE,MAAM,CAAChE,EAAE,CAAC;EACnB,CAAC;EAED,MAAMmE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI7G,cAAc,CAAC2B,MAAM,KAAK,CAAC,EAAE;MAC/BpB,WAAW,CAAC;QAAEa,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE/C,CAAC,CAAC,gBAAgB,CAAC;QAAEgD,QAAQ,EAAE;MAAQ,CAAC,CAAC;MAC5E;IACF;IAEA,MAAMwF,YAAY,GAAG9G,cAAc,CAACuC,GAAG,CAACwE,GAAG,KAAK;MAC9C,CAACzI,CAAC,CAAC,OAAO,CAAC,GAAGyI,GAAG,CAAClE,KAAK;MACvB,CAACvE,CAAC,CAAC,YAAY,CAAC,GAAGyI,GAAG,CAAC/D,UAAU;MACjC,CAAC1E,CAAC,CAAC,UAAU,CAAC,GAAGyI,GAAG,CAACC,QAAQ;MAC7B,CAAC1I,CAAC,CAAC,cAAc,CAAC,GAAGyI,GAAG,CAACE,YAAY;MACrC,CAAC3I,CAAC,CAAC,eAAe,CAAC,GAAGyI,GAAG,CAAC9D,aAAa;MACvC,CAAC3E,CAAC,CAAC,WAAW,CAAC,GAAGyI,GAAG,CAACtD,SAAS;MAC/B,CAACnF,CAAC,CAAC,eAAe,CAAC,GAAGyI,GAAG,CAACnD;IAC5B,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMsD,WAAW,GAAG5I,CAAC,CAAC,wBAAwB,CAAC;IAC/C,MAAM6I,GAA0B,GAAG,EAAE;IACrCA,GAAG,CAACC,IAAI,CAAC,CAACF,WAAW,CAAC,CAAC,CAAC,CAAC;IACzBC,GAAG,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd;IACA,MAAMC,OAAO,GAAGnE,MAAM,CAACoE,IAAI,CAACR,YAAY,CAAC,CAAC,CAAC,CAAC;IAC5CK,GAAG,CAACC,IAAI,CAACC,OAAO,CAAC;;IAEjB;IACAP,YAAY,CAAC/D,OAAO,CAACgE,GAAG,IAAI;MAC1BI,GAAG,CAACC,IAAI,CAAClE,MAAM,CAACC,MAAM,CAAC4D,GAAG,CAAC,CAAC;IAC9B,CAAC,CAAC;IAEF,MAAMQ,EAAE,GAAGtJ,IAAI,CAACuJ,KAAK,CAACC,YAAY,CAACN,GAAG,CAAC;IACvC,MAAMO,EAAE,GAAGzJ,IAAI,CAACuJ,KAAK,CAACG,QAAQ,CAAC,CAAC;;IAEhC;IACA,MAAMC,SAAS,GAAGP,OAAO,CAAC9E,GAAG,CAACsF,MAAM,IAAI;MACtC,IAAIrD,QAAQ,GAAGqD,MAAM,CAAClG,MAAM;MAC5BmF,YAAY,CAAC/D,OAAO,CAACgE,GAAG,IAAI;QAC1B,MAAMe,SAAS,GAAGpB,MAAM,CAACK,GAAG,CAACc,MAAM,CAAC,CAAC;QACrCrD,QAAQ,GAAGuD,IAAI,CAACC,GAAG,CAACxD,QAAQ,EAAEsD,SAAS,CAACnG,MAAM,CAAC;MACjD,CAAC,CAAC;MACF,OAAO;QAAEsG,GAAG,EAAEF,IAAI,CAACG,GAAG,CAAC1D,QAAQ,GAAG,CAAC,EAAE,EAAE;MAAE,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;IACF+C,EAAE,CAAC,OAAO,CAAC,GAAGK,SAAS;;IAEvB;IACA,MAAMO,aAAa,GAAGlK,IAAI,CAACuJ,KAAK,CAACY,WAAW,CAAC;MAAEzB,CAAC,EAAE,CAAC;MAAEF,CAAC,EAAE;IAAE,CAAC,CAAC;IAC5D,IAAI,CAACc,EAAE,CAACY,aAAa,CAAC,EAAEZ,EAAE,CAACY,aAAa,CAAC,GAAG;MAAEE,CAAC,EAAEnB;IAAY,CAAC;IAC9DK,EAAE,CAACY,aAAa,CAAC,CAACG,CAAC,GAAG;MACpBC,IAAI,EAAE;QAAEC,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE;UAAEC,GAAG,EAAE;QAAW;MAAE,CAAC;MAC1DC,SAAS,EAAE;QAAEC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;MACvDC,IAAI,EAAE;QAAEC,OAAO,EAAE;UAAEL,GAAG,EAAE;QAAW;MAAE,CAAC;MAAE;MACxCM,MAAM,EAAE;QACNC,GAAG,EAAE;UAAEC,KAAK,EAAE,QAAQ;UAAET,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAW;QAAE,CAAC;QACpDS,MAAM,EAAE;UAAED,KAAK,EAAE,QAAQ;UAAET,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAW;QAAE,CAAC;QACvDU,IAAI,EAAE;UAAEF,KAAK,EAAE,QAAQ;UAAET,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAW;QAAE,CAAC;QACrDW,KAAK,EAAE;UAAEH,KAAK,EAAE,QAAQ;UAAET,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAW;QAAE;MACvD;IACF,CAAC;;IAED;IACA,MAAMY,UAAU,GAAG;MAAEjB,CAAC,EAAE;QAAE3B,CAAC,EAAE,CAAC;QAAEF,CAAC,EAAE;MAAE,CAAC;MAAExB,CAAC,EAAE;QAAE0B,CAAC,EAAE,CAAC;QAAEF,CAAC,EAAEY,OAAO,CAAC1F,MAAM,GAAG;MAAE;IAAE,CAAC;IAC5E,IAAI,CAAC4F,EAAE,CAAC,SAAS,CAAC,EAAEA,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE;IACtCA,EAAE,CAAC,SAAS,CAAC,CAACH,IAAI,CAACmC,UAAU,CAAC;;IAE9B;IACA,MAAMC,cAAc,GAAG,CAAC;IACxB,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,OAAO,CAAC1F,MAAM,EAAE8E,CAAC,EAAE,EAAE;MACvC,MAAMgD,WAAW,GAAGxL,IAAI,CAACuJ,KAAK,CAACY,WAAW,CAAC;QAAEzB,CAAC,EAAE6C,cAAc;QAAE/C,CAAC,EAAEA;MAAE,CAAC,CAAC;MACvE,IAAI,CAACc,EAAE,CAACkC,WAAW,CAAC,EAAElC,EAAE,CAACkC,WAAW,CAAC,GAAG;QAAEpB,CAAC,EAAEhB,OAAO,CAACZ,CAAC;MAAE,CAAC;MACzDc,EAAE,CAACkC,WAAW,CAAC,CAACnB,CAAC,GAAG;QAClBC,IAAI,EAAE;UAAEC,IAAI,EAAE,IAAI;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAW;QAAE,CAAC;QAAE;QAC5DC,SAAS,EAAE;UAAEC,UAAU,EAAE,QAAQ;UAAEC,QAAQ,EAAE;QAAS,CAAC;QACvDC,IAAI,EAAE;UAAEC,OAAO,EAAE;YAAEL,GAAG,EAAE;UAAW;QAAE,CAAC;QAAE;QACxCM,MAAM,EAAE;UACNC,GAAG,EAAE;YAAEC,KAAK,EAAE,QAAQ;YAAET,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE,CAAC;UACpDS,MAAM,EAAE;YAAED,KAAK,EAAE,QAAQ;YAAET,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE,CAAC;UACvDU,IAAI,EAAE;YAAEF,KAAK,EAAE,QAAQ;YAAET,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE,CAAC;UACrDW,KAAK,EAAE;YAAEH,KAAK,EAAE,QAAQ;YAAET,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE;QACvD;MACF,CAAC;IACH;;IAEA;IACA,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,GAAG,CAACxF,MAAM,EAAEgF,CAAC,EAAE,EAAE;MACnC,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,OAAO,CAAC1F,MAAM,EAAE8E,CAAC,EAAE,EAAE;QACvC,MAAMgD,WAAW,GAAGxL,IAAI,CAACuJ,KAAK,CAACY,WAAW,CAAC;UAAEzB,CAAC,EAAEA,CAAC;UAAEF,CAAC,EAAEA;QAAE,CAAC,CAAC;QAC1D,IAAI,CAACc,EAAE,CAACkC,WAAW,CAAC,EAAElC,EAAE,CAACkC,WAAW,CAAC,GAAG;UAAEpB,CAAC,EAAElB,GAAG,CAACR,CAAC,CAAC,CAACF,CAAC;QAAE,CAAC;;QAExD;QACA,MAAMiD,SAAS,GAAG,CAAC/C,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;QACnCY,EAAE,CAACkC,WAAW,CAAC,CAACnB,CAAC,GAAG;UAClBC,IAAI,EAAE;YAAEE,IAAI,EAAE;UAAG,CAAC;UAClBG,SAAS,EAAE;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAC;UACvDC,IAAI,EAAE;YAAEC,OAAO,EAAE;cAAEL,GAAG,EAAEe,SAAS,GAAG,UAAU,GAAG;YAAW;UAAE,CAAC;UAAE;UACjET,MAAM,EAAE;YACNC,GAAG,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YAClDS,MAAM,EAAE;cAAED,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACrDU,IAAI,EAAE;cAAEF,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACnDW,KAAK,EAAE;cAAEH,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE;UACrD;QACF,CAAC;MACH;IACF;IAEA1K,IAAI,CAACuJ,KAAK,CAACmC,iBAAiB,CAACjC,EAAE,EAAEH,EAAE,EAAE,sBAAsB,CAAC;IAC5DtJ,IAAI,CAAC2L,SAAS,CAAClC,EAAE,EAAE,mCAAmC,EAAE;MAAEmC,UAAU,EAAE;IAAK,CAAC,CAAC;EAC/E,CAAC;EAED,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI9J,cAAc,CAAC2B,MAAM,KAAK,CAAC,EAAE;MAC/BpB,WAAW,CAAC;QAAEa,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE/C,CAAC,CAAC,gBAAgB,CAAC;QAAEgD,QAAQ,EAAE;MAAQ,CAAC,CAAC;MAC5E;IACF;IAEA,IAAI;MAAA,IAAAyI,gBAAA;MACF,MAAM7C,WAAW,GAAG5I,CAAC,CAAC,8BAA8B,CAAC;MACrD,MAAMwI,YAAY,GAAG9G,cAAc,CAACuC,GAAG,CAACC,IAAI,KAAK;QAC/C,CAAClE,CAAC,CAAC,OAAO,CAAC,GAAGkE,IAAI,CAACK,KAAK;QACxB,CAACvE,CAAC,CAAC,YAAY,CAAC,GAAGkE,IAAI,CAACQ,UAAU;QAClC,CAAC1E,CAAC,CAAC,UAAU,CAAC,GAAGkE,IAAI,CAACwE,QAAQ;QAC9B,CAAC1I,CAAC,CAAC,cAAc,CAAC,GAAGkE,IAAI,CAACyE,YAAY;QACtC,CAAC3I,CAAC,CAAC,eAAe,CAAC,GAAGkE,IAAI,CAACS,aAAa;QACxC,CAAC3E,CAAC,CAAC,WAAW,CAAC,GAAGkE,IAAI,CAACiB,SAAS,CAAC0C,OAAO,CAAC,CAAC,CAAC;QAC3C,CAAC7H,CAAC,CAAC,eAAe,CAAC,GAAGkE,IAAI,CAACoB;MAC7B,CAAC,CAAC,CAAC;MAEH,MAAMtB,QAAQ,GAAG,MAAMrG,KAAK,CAAC+N,IAAI,CAC/B,2CAA2C,EAC3ClD,YAAY;MAAE;MACd;QACEmD,YAAY,EAAE,MAAM;QAAE;QACtB/D,MAAM,EAAE;UAAEgE,KAAK,EAAEhD;QAAY,CAAC,CAAC;MACjC,CACF,CAAC;MAED,MAAMtF,GAAG,GAAGuI,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAChI,QAAQ,CAACrB,IAAI,CAAC,CAAC,CAAC;MACjE,MAAMsJ,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG9I,GAAG;MACf2I,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,qCAAqC,CAAC;MACpEH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZ,CAAAf,gBAAA,GAAAQ,IAAI,CAACQ,UAAU,cAAAhB,gBAAA,uBAAfA,gBAAA,CAAiBiB,WAAW,CAACT,IAAI,CAAC;MAClChK,WAAW,CAAC;QAAEa,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE/C,CAAC,CAAC,kBAAkB,CAAC;QAAEgD,QAAQ,EAAE;MAAU,CAAC,CAAC;IAClF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CX,WAAW,CAAC;QAAEa,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE/C,CAAC,CAAC,gBAAgB,CAAC;QAAEgD,QAAQ,EAAE;MAAQ,CAAC,CAAC;IAC9E;EACF,CAAC;EAED,oBACEnD,OAAA,CAACd,oBAAoB;IAAC4N,WAAW,EAAE7N,YAAa;IAAAqH,QAAA,gBAC9CtG,OAAA,CAACjC,GAAG;MAACoI,EAAE,EAAE;QAAE4G,CAAC,EAAE;MAAE,CAAE;MAACxI,EAAE,EAAC,8BAA8B;MAAA+B,QAAA,GAAC,GAAC,eACpDtG,OAAA,CAAChC,UAAU;QAACgP,OAAO,EAAC,IAAI;QAACC,YAAY;QAAA3G,QAAA,EAAEnG,CAAC,CAAC,8BAA8B;MAAC;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAGtF1G,OAAA,CAACnB,KAAK;QAACsH,EAAE,EAAE;UAAE4G,CAAC,EAAE,CAAC;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAA5G,QAAA,eACzBtG,OAAA,CAACpB,IAAI;UAACuO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAA/G,QAAA,gBAC7CtG,OAAA,CAACpB,IAAI;YAACyF,IAAI;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlH,QAAA,eAACtG,OAAA,CAAC/B,WAAW;cAACwP,SAAS;cAAAnH,QAAA,gBAACtG,OAAA,CAAC9B,UAAU;gBAAAoI,QAAA,EAAEnG,CAAC,CAAC,WAAW;cAAC;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA1G,OAAA,CAAC7B,MAAM;gBAACyI,KAAK,EAAE5F,iBAAkB;gBAAC+E,KAAK,EAAE5F,CAAC,CAAC,WAAW,CAAE;gBAAC0G,QAAQ,EAAGC,CAAC,IAAK;kBAAE7F,oBAAoB,CAAC6F,CAAC,CAACC,MAAM,CAACH,KAAY,CAAC;kBAAEzF,cAAc,CAAC,EAAE,CAAC;gBAAE,CAAE;gBAAAmF,QAAA,gBAC9OtG,OAAA,CAAC5B,QAAQ;kBAACwI,KAAK,EAAC,QAAQ;kBAAAN,QAAA,EAAEnG,CAAC,CAAC,SAAS;gBAAC;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClD1G,OAAA,CAAC5B,QAAQ;kBAACwI,KAAK,EAAC,QAAQ;kBAAAN,QAAA,EAAEnG,CAAC,CAAC,SAAS;gBAAC;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClD1G,OAAA,CAAC5B,QAAQ;kBAACwI,KAAK,EAAC,QAAQ;kBAAAN,QAAA,EAAEnG,CAAC,CAAC,UAAU;gBAAC;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9B1G,OAAA,CAACpB,IAAI;YAACyF,IAAI;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlH,QAAA,EAAET,qBAAqB,CAAC;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjE1G,OAAA,CAACpB,IAAI;YAACyF,IAAI;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlH,QAAA,eAACtG,OAAA,CAAC/B,WAAW;cAACwP,SAAS;cAAAnH,QAAA,gBAACtG,OAAA,CAAC9B,UAAU;gBAAAoI,QAAA,EAAEnG,CAAC,CAAC,kBAAkB;cAAC;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA1G,OAAA,CAAC7B,MAAM;gBAACyI,KAAK,EAAExF,gBAAiB;gBAAC2E,KAAK,EAAE5F,CAAC,CAAC,kBAAkB,CAAE;gBAAC0G,QAAQ,EAAGC,CAAC,IAAKzF,mBAAmB,CAACyF,CAAC,CAACC,MAAM,CAACH,KAAe,CAAE;gBAAAN,QAAA,gBAACtG,OAAA,CAAC5B,QAAQ;kBAACwI,KAAK,EAAC,EAAE;kBAAAN,QAAA,EAAEnG,CAAC,CAAC,eAAe;gBAAC;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,EAAChG,UAAU,CAAC0D,GAAG,CAAEsJ,GAAG,iBAAK1N,OAAA,CAAC5B,QAAQ;kBAAsBwI,KAAK,EAAE8G,GAAG,CAACpK,UAAW;kBAAAgD,QAAA,EAAEoH,GAAG,CAAC5E;gBAAY,GAAxD4E,GAAG,CAACpK,UAAU;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqD,CAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACra1G,OAAA,CAACpB,IAAI;YAACyF,IAAI;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlH,QAAA,eAACtG,OAAA,CAAC/B,WAAW;cAACwP,SAAS;cAAAnH,QAAA,gBAACtG,OAAA,CAAC9B,UAAU;gBAAAoI,QAAA,EAAEnG,CAAC,CAAC,cAAc;cAAC;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA1G,OAAA,CAAC7B,MAAM;gBAACyI,KAAK,EAAEtF,YAAa;gBAACyE,KAAK,EAAE5F,CAAC,CAAC,cAAc,CAAE;gBAAC0G,QAAQ,EAAGC,CAAC,IAAKvF,eAAe,CAACuF,CAAC,CAACC,MAAM,CAACH,KAAe,CAAE;gBAAC+G,QAAQ,EAAE7M,aAAa,CAAC0C,MAAM,KAAK,CAAC,IAAIpC,gBAAgB,KAAK,EAAG;gBAAAkF,QAAA,gBAACtG,OAAA,CAAC5B,QAAQ;kBAACwI,KAAK,EAAC,EAAE;kBAAAN,QAAA,EAAEnG,CAAC,CAAC,UAAU;gBAAC;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,EAAC5F,aAAa,CAACsD,GAAG,CAAEf,IAAI,iBAAKrD,OAAA,CAAC5B,QAAQ;kBAAmBwI,KAAK,EAAEvD,IAAI,CAACoB,MAAO;kBAAA6B,QAAA,EAAEjD,IAAI,CAACwF;gBAAQ,GAA/CxF,IAAI,CAACoB,MAAM;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+C,CAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5c1G,OAAA,CAACpB,IAAI;YAACyF,IAAI;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlH,QAAA,eAACtG,OAAA,CAAChB,UAAU;cAAC+G,KAAK,EAAE5F,CAAC,CAAC,oBAAoB,CAAE;cAACyN,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAE;cAACC,MAAM,EAAC,OAAO;cAACjH,KAAK,EAAEpF,eAAgB;cAACqF,QAAQ,EAAEpF,kBAAmB;cAACqM,KAAK,EAAE;gBAAEC,SAAS,EAAEvP;cAAU,CAAE;cAACwP,SAAS,EAAE;gBAAED,SAAS,EAAE;kBAAEN,SAAS,EAAE;gBAAK;cAAE;YAAE;cAAAlH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrQ1G,OAAA,CAACpB,IAAI;YAACyF,IAAI;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlH,QAAA,eAACtG,OAAA,CAAChB,UAAU;cAAC+G,KAAK,EAAE5F,CAAC,CAAC,kBAAkB,CAAE;cAACyN,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAE;cAACC,MAAM,EAAC,OAAO;cAACjH,KAAK,EAAEjF,aAAc;cAACkF,QAAQ,EAAEjF,gBAAiB;cAACkM,KAAK,EAAE;gBAAEC,SAAS,EAAEvP;cAAU,CAAE;cAACwP,SAAS,EAAE;gBAAED,SAAS,EAAE;kBAAEN,SAAS,EAAE;gBAAK;cAAE;YAAE;cAAAlH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/P1G,OAAA,CAACpB,IAAI;YAACyF,IAAI;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlH,QAAA,eAACtG,OAAA,CAACzB,MAAM;cAACyO,OAAO,EAAC,WAAW;cAACzC,KAAK,EAAC,SAAS;cAAC0D,OAAO,EAAE1K,mBAAoB;cAACoK,QAAQ,EAAE1L,OAAO,IAAIf,WAAW,CAACsC,MAAM,KAAK,CAAC,IAAI,CAAClC,YAAa;cAAAgF,QAAA,EAAEnG,CAAC,CAAC,cAAc;YAAC;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9M1G,OAAA,CAACpB,IAAI;YAACyF,IAAI;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlH,QAAA,eAACtG,OAAA,CAACzB,MAAM;cAACyO,OAAO,EAAC,UAAU;cAACzC,KAAK,EAAC,WAAW;cAAC0D,OAAO,EAAEhJ,kBAAmB;cAAAqB,QAAA,EAAEnG,CAAC,CAAC,cAAc;YAAC;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9I1G,OAAA,CAACpB,IAAI;YAACyF,IAAI;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlH,QAAA,eAACtG,OAAA,CAACzB,MAAM;cAACyO,OAAO,EAAC,WAAW;cAACzC,KAAK,EAAC,SAAS;cAAC0D,OAAO,EAAEvF,iBAAkB;cAACiF,QAAQ,EAAE9L,cAAc,CAAC2B,MAAM,KAAK,CAAE;cAAA8C,QAAA,EAAEnG,CAAC,CAAC,eAAe;YAAC;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpL1G,OAAA,CAACpB,IAAI;YAACyF,IAAI;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlH,QAAA,eAACtG,OAAA,CAACzB,MAAM;cAACyO,OAAO,EAAC,WAAW;cAACzC,KAAK,EAAC,OAAO;cAAC0D,OAAO,EAAEtC,eAAgB;cAACgC,QAAQ,EAAE9L,cAAc,CAAC2B,MAAM,KAAK,CAAE;cAAA8C,QAAA,EAAEnG,CAAC,CAAC,aAAa;YAAC;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1K;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR1G,OAAA,CAACpB,IAAI;QAACuO,SAAS;QAACC,OAAO,EAAE,CAAE;QAACjH,EAAE,EAAE;UAAE+G,EAAE,EAAE;QAAE,CAAE;QAAA5G,QAAA,gBACtCtG,OAAA,CAACpB,IAAI;UAACyF,IAAI;UAACiJ,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlH,QAAA,eAACtG,OAAA,CAAClB,IAAI;YAAAwH,QAAA,eAACtG,OAAA,CAACjB,WAAW;cAAAuH,QAAA,gBAACtG,OAAA,CAAChC,UAAU;gBAACuM,KAAK,EAAC,eAAe;gBAAC0C,YAAY;gBAAA3G,QAAA,EAAEnG,CAAC,CAAC,WAAW;cAAC;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA1G,OAAA,CAAChC,UAAU;gBAACgP,OAAO,EAAC,IAAI;gBAAA1G,QAAA,EAAEpB,OAAO,CAACG,SAAS,CAAC6I,cAAc,CAACC,SAAS,EAAE;kBAACC,qBAAqB,EAAE,CAAC;kBAAEC,qBAAqB,EAAE;gBAAC,CAAC;cAAC;gBAAA9H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrS1G,OAAA,CAACpB,IAAI;UAACyF,IAAI;UAACiJ,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlH,QAAA,eAACtG,OAAA,CAAClB,IAAI;YAAAwH,QAAA,eAACtG,OAAA,CAACjB,WAAW;cAAAuH,QAAA,gBAACtG,OAAA,CAAChC,UAAU;gBAACuM,KAAK,EAAC,eAAe;gBAAC0C,YAAY;gBAAA3G,QAAA,EAAEnG,CAAC,CAAC,eAAe;cAAC;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA1G,OAAA,CAAChC,UAAU;gBAACgP,OAAO,EAAC,IAAI;gBAAA1G,QAAA,EAAEpB,OAAO,CAACK,aAAa,CAAC2I,cAAc,CAAC;cAAC;gBAAA3H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9O1G,OAAA,CAACpB,IAAI;UAACyF,IAAI;UAACiJ,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlH,QAAA,eAACtG,OAAA,CAAClB,IAAI;YAAAwH,QAAA,eAACtG,OAAA,CAACjB,WAAW;cAAAuH,QAAA,gBAACtG,OAAA,CAAChC,UAAU;gBAACuM,KAAK,EAAC,eAAe;gBAAC0C,YAAY;gBAAA3G,QAAA,EAAEnG,CAAC,CAAC,eAAe;cAAC;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA1G,OAAA,CAAChC,UAAU;gBAACgP,OAAO,EAAC,IAAI;gBAAA1G,QAAA,EAAEpB,OAAO,CAACM,UAAU,CAAC0I,cAAc,CAAC;cAAC;gBAAA3H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3O1G,OAAA,CAACpB,IAAI;UAACyF,IAAI;UAACiJ,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlH,QAAA,eAACtG,OAAA,CAAClB,IAAI;YAAAwH,QAAA,eAACtG,OAAA,CAACjB,WAAW;cAAAuH,QAAA,gBAACtG,OAAA,CAAChC,UAAU;gBAACuM,KAAK,EAAC,eAAe;gBAAC0C,YAAY;gBAAA3G,QAAA,EAAEnG,CAAC,CAAC,gBAAgB;cAAC;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAAA1G,OAAA,CAAChC,UAAU;gBAACgP,OAAO,EAAC,IAAI;gBAAA1G,QAAA,EAAE,CAACpB,OAAO,CAACM,UAAU,GAAG,CAAC,GAAGN,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE0I,cAAc,CAACC,SAAS,EAAE;kBAACC,qBAAqB,EAAE,CAAC;kBAAEC,qBAAqB,EAAE;gBAAC,CAAC;cAAC;gBAAA9H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5V,CAAC,eAGP1G,OAAA,CAACpB,IAAI;QAACuO,SAAS;QAACC,OAAO,EAAE,CAAE;QAACjH,EAAE,EAAE;UAAE+G,EAAE,EAAE;QAAE,CAAE;QAAA5G,QAAA,gBACxCtG,OAAA,CAACpB,IAAI;UAACyF,IAAI;UAACiJ,EAAE,EAAE,EAAG;UAAAhH,QAAA,eAACtG,OAAA,CAACnB,KAAK;YAACsH,EAAE,EAAE;cAAE4G,CAAC,EAAE,CAAC;cAAEuB,MAAM,EAAE;YAAI,CAAE;YAAAhI,QAAA,gBAACtG,OAAA,CAAChC,UAAU;cAACgP,OAAO,EAAC,IAAI;cAAA1G,QAAA,EAAEnG,CAAC,CAAC,kBAAkB;YAAC;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAAA1G,OAAA,CAACN,mBAAmB;cAAA4G,QAAA,eAACtG,OAAA,CAACb,QAAQ;gBAAC2D,IAAI,EAAE4C,oBAAqB;gBAAAY,QAAA,gBAACtG,OAAA,CAACT,aAAa;kBAACgP,eAAe,EAAC;gBAAK;kBAAAhI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA1G,OAAA,CAACX,KAAK;kBAACmP,OAAO,EAAC;gBAAM;kBAAAjI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA1G,OAAA,CAACV,KAAK;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA1G,OAAA,CAACR,OAAO;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA1G,OAAA,CAACP,MAAM;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA1G,OAAA,CAACZ,GAAG;kBAACoP,OAAO,EAAC,WAAW;kBAAC5D,IAAI,EAAC,SAAS;kBAAChF,IAAI,EAAEzF,CAAC,CAAC,WAAW;gBAAE;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA1G,OAAA,CAACZ,GAAG;kBAACoP,OAAO,EAAC,eAAe;kBAAC5D,IAAI,EAAC,SAAS;kBAAChF,IAAI,EAAEzF,CAAC,CAAC,eAAe;gBAAE;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACjc3E,gBAAgB,CAACyB,MAAM,GAAG,CAAC,iBAC1BxD,OAAA,CAACpB,IAAI;UAACyF,IAAI;UAACiJ,EAAE,EAAE,EAAG;UAAAhH,QAAA,eAACtG,OAAA,CAACnB,KAAK;YAACsH,EAAE,EAAE;cAAE4G,CAAC,EAAE,CAAC;cAAEuB,MAAM,EAAE,GAAG;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAAnI,QAAA,gBAACtG,OAAA,CAAChC,UAAU;cAACgP,OAAO,EAAC,IAAI;cAAA1G,QAAA,EAAEnG,CAAC,CAAC,mBAAmB;YAAC;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAAA1G,OAAA,CAACN,mBAAmB;cAAA4G,QAAA,eAACtG,OAAA,CAACb,QAAQ;gBAAC2D,IAAI,EAAEf,gBAAiB;gBAAAuE,QAAA,gBAACtG,OAAA,CAACT,aAAa;kBAACgP,eAAe,EAAC;gBAAK;kBAAAhI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA1G,OAAA,CAACX,KAAK;kBAACmP,OAAO,EAAC;gBAAO;kBAAAjI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA1G,OAAA,CAACV,KAAK;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA1G,OAAA,CAACR,OAAO;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAAA1G,OAAA,CAACP,MAAM;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC1QxF,WAAW,CAACkD,GAAG,CAAC,CAACG,EAAE,EAAED,KAAK,kBACzBtE,OAAA,CAACZ,GAAG;kBAAUoP,OAAO,EAAEtG,gBAAgB,CAAC3D,EAAE,CAAE;kBAACqG,IAAI,EAAE3C,MAAM,CAAC3D,KAAK,GAAG2D,MAAM,CAACzE,MAAM,CAAE;kBAACoC,IAAI,EAAEsC,gBAAgB,CAAC3D,EAAE;gBAAE,GAAnGA,EAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmG,CAChH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAClD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGP1G,OAAA,CAACnB,KAAK;QAACsH,EAAE,EAAE;UAAEmI,MAAM,EAAE,GAAG;UAAE1G,KAAK,EAAE;QAAO,CAAE;QAAAtB,QAAA,gBACxCtG,OAAA,CAAChC,UAAU;UAACgP,OAAO,EAAC,IAAI;UAAC7G,EAAE,EAAE;YAAE4G,CAAC,EAAE;UAAE,CAAE;UAAAzG,QAAA,EAAEnG,CAAC,CAAC,wBAAwB;QAAC;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjF1G,OAAA,CAACL,QAAQ;UACP+O,IAAI,EAAE7M,cAAe;UACrB4F,OAAO,EAAEA,OAAQ;UACjBxF,OAAO,EAAEA,OAAQ;UACjB6L,KAAK,EAAE;YAAEa,OAAO,EAAE/O;UAAY,CAAE;UAChCoO,SAAS,EAAE;YACTW,OAAO,EAAE;cACPC,eAAe,EAAE,IAAI;cACrBC,gBAAgB,EAAE;gBAAEC,UAAU,EAAE;cAAI,CAAC;cACrCC,UAAU,EAAE;gBAAEC,oBAAoB,EAAE;cAAM,CAAC;cAC3CC,YAAY,EAAE;gBAAED,oBAAoB,EAAE;cAAM;cAC5C;YACF;UACF,CAAE;UACFE,YAAY,EAAE;YACZC,UAAU,EAAE;cACVC,eAAe,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,IAAI,EAAE;cAAE;YAC5C;UACF,CAAE;UACFC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UAC/BC,iBAAiB;UACjBC,0BAA0B;QAAA;UAAAlJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN1G,OAAA,CAACzB,MAAM;MAACyO,OAAO,EAAC,UAAU;MAACiB,OAAO,EAAEA,CAAA,KAAMjC,MAAM,CAAC0D,KAAK,CAAC,CAAE;MAACvJ,EAAE,EAAE;QAAEsI,EAAE,EAAE;MAAE,CAAE;MAAAnI,QAAA,EACrEnG,CAAC,CAAC,aAAa;IAAC;MAAAoG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACT1G,OAAA,CAAC3B,QAAQ;MACP4E,IAAI,EAAEd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,IAAK;MACrB0M,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAMxN,WAAW,CAAC,IAAI,CAAE;MACjCyN,YAAY,EAAE;QAAElF,QAAQ,EAAE,QAAQ;QAAED,UAAU,EAAE;MAAO,CAAE;MAAApE,QAAA,eAEzDtG,OAAA,CAAC1B,KAAK;QAACsR,OAAO,EAAEA,CAAA,KAAMxN,WAAW,CAAC,IAAI,CAAE;QAACe,QAAQ,EAAEhB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgB,QAAS;QAACgD,EAAE,EAAE;UAAEyB,KAAK,EAAE;QAAO,CAAE;QAAAtB,QAAA,EAC1FnE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe;MAAO;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAE3B,CAAC;AAACxG,EAAA,CAhaID,0BAA0B;EAAA,QAChBpC,cAAc;AAAA;AAAAiS,EAAA,GADxB7P,0BAA0B;AAkahC,eAAeA,0BAA0B;AAAC,IAAA6P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}