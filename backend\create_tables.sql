
CREATE TABLE branches (
    id INTEGER NOT NULL,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE regions (
    id INTEGER NOT NULL,
    branch_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY(branch_id) REFERENCES branches (id)
);

CREATE TABLE clinics (
    id INTEGER NOT NULL,
    region_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY(region_id) REFERENCES regions (id)
);

CREATE TABLE drug_categories (
    CategoryID INTEGER NOT NULL,
    CategoryName VARCHAR(100) NOT NULL,
    PRIMARY KEY (CategoryID)
);

CREATE TABLE drugs (
    DrugID INTEGER NOT NULL,
    CategoryID INTEGER NOT NULL,
    DrugName VARCHAR(100) NOT NULL,
    Unit VARCHAR(50),
    PRIMARY KEY (DrugID),
    FOREIGN KEY(CategoryID) REFERENCES drug_categories (CategoryID)
);

CREATE TABLE dispensed_drugs (
    DispenseID INTEGER NOT NULL,
    ClinicID INTEGER NOT NULL,
    DrugID INTEGER NOT NULL,
    Quantity INTEGER NOT NULL,
    UnitPrice DECIMAL NOT NULL,
    Cases INTEGER NOT NULL,
    TotalCost DECIMAL GENERATED ALWAYS AS (Quantity * UnitPrice) STORED,
    DispenseDate DATE NOT NULL,
    PRIMARY KEY (DispenseID),
    FOREIGN KEY(ClinicID) REFERENCES clinics (id),
    FOREIGN KEY(DrugID) REFERENCES drugs (DrugID)
);

CREATE TABLE insulin_types (
    InsulinID INTEGER NOT NULL,
    InsulinName VARCHAR(100) NOT NULL,
    SupportType VARCHAR(50),
    Unit VARCHAR(50),
    Balance INTEGER,
    PRIMARY KEY (InsulinID)
);

CREATE TABLE dispensed_insulin (
    ID INTEGER NOT NULL,
    ClinicID INTEGER NOT NULL,
    InsulinID INTEGER NOT NULL,
    Quantity INTEGER NOT NULL,
    Cases INTEGER NOT NULL,
    UnitPrice DECIMAL NOT NULL,
    TotalCost DECIMAL GENERATED ALWAYS AS (Quantity * UnitPrice) STORED,
    Date DATE NOT NULL,
    PRIMARY KEY (ID),
    FOREIGN KEY(ClinicID) REFERENCES clinics (id),
    FOREIGN KEY(InsulinID) REFERENCES insulin_types (InsulinID)
);

CREATE TABLE drug_groups (
    GroupID INTEGER NOT NULL,
    GroupName VARCHAR(100) NOT NULL,
    PRIMARY KEY (GroupID)
);

CREATE TABLE group_costs (
    ID INTEGER NOT NULL,
    GroupID INTEGER NOT NULL,
    Cost DECIMAL NOT NULL,
    Date DATE NOT NULL,
    PRIMARY KEY (ID),
    FOREIGN KEY(GroupID) REFERENCES drug_groups (GroupID)
);

CREATE TABLE monthly_dispense (
    ID INTEGER NOT NULL,
    ClinicID INTEGER NOT NULL,
    Year INTEGER NOT NULL,
    Month INTEGER NOT NULL,
    "هيئة" DECIMAL,
    "طلاب" DECIMAL,
    "رضع" DECIMAL,
    "امرأة_معيلة" DECIMAL,
    PRIMARY KEY (ID),
    FOREIGN KEY(ClinicID) REFERENCES clinics (id)
);

CREATE TABLE monthly_supplies (
    ID INTEGER NOT NULL,
    ClinicID INTEGER NOT NULL,
    Year INTEGER NOT NULL,
    Month INTEGER NOT NULL,
    "مستلزمات_عامة" DECIMAL,
    "أشعة" DECIMAL,
    "معامل" DECIMAL,
    "مستلزمات_عيادات" DECIMAL,
    PRIMARY KEY (ID),
    FOREIGN KEY(ClinicID) REFERENCES clinics (id)
);

CREATE TABLE medical_tickets (
    TicketID INTEGER NOT NULL,
    ClinicID INTEGER NOT NULL,
    Year INTEGER NOT NULL,
    Month INTEGER NOT NULL,
    Week INTEGER NOT NULL,
    "فئة" VARCHAR(50) NOT NULL,
    "تذاكر_56ب" INTEGER,
    "تذاكر_56ج" INTEGER,
    PRIMARY KEY (TicketID),
    FOREIGN KEY(ClinicID) REFERENCES clinics (id)
);

CREATE TABLE subsidized_pharmacy_tickets (
    ID INTEGER NOT NULL,
    ClinicID INTEGER NOT NULL,
    Year INTEGER NOT NULL,
    Month INTEGER NOT NULL,
    Week INTEGER NOT NULL,
    "هيئة" INTEGER,
    "طلاب" INTEGER,
    "مدعّم" INTEGER,
    PRIMARY KEY (ID),
    FOREIGN KEY(ClinicID) REFERENCES clinics (id)
);

CREATE TABLE court_rulings_drugs (
    ID INTEGER NOT NULL,
    branch_id INTEGER NOT NULL,
    "جهة" VARCHAR(100),
    "اسم_المريض" VARCHAR(100),
    "التشخيص" VARCHAR(200),
    "صنف_العلاج" VARCHAR(100),
    "الوحدة" VARCHAR(50),
    "سعر_الوحدة" DECIMAL,
    "الجرعة_شهرياً" INTEGER,
    "التكلفة_شهرياً" DECIMAL GENERATED ALWAYS AS ("سعر_الوحدة" * "الجرعة_شهرياً") STORED,
    "تاريخ_الحكم" DATE,
    "تاريخ_أول_صرف" DATE,
    "شهر_الصرف" DATE,
    PRIMARY KEY (ID),
    FOREIGN KEY(branch_id) REFERENCES branches (id)
);
