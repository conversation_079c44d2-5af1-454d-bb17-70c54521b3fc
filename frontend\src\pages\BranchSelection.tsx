import React, { useState, useEffect } from 'react';
import {
  <PERSON>po<PERSON>,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Button,
  CircularProgress,
  Alert,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import axios from 'axios';

// Define interfaces for data fetched from backend
interface Branch {
  BranchID: number;
  BranchName: string;
}

interface Region {
  RegionID: number;
  BranchID: number;
  RegionName: string;
}

interface Clinic {
  ClinicID: number;
  RegionID: number;
  ClinicName: string;
}

const API_BASE_URL = 'http://127.0.0.1:8000'; // Assuming backend runs on this URL

const BranchSelection: React.FC = () => {
  const { t } = useTranslation();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [clinics, setClinics] = useState<Clinic[]>([]);

  const [selectedBranch, setSelectedBranch] = useState<string>('');
  const [selectedRegion, setSelectedRegion] = useState<string>('');
  const [selectedClinic, setSelectedClinic] = useState<string>('');

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [branchesRes, regionsRes, clinicsRes] = await Promise.all([
          axios.get<Branch[]>(`${API_BASE_URL}/branches/`),
          axios.get<Region[]>(`${API_BASE_URL}/regions/`),
          axios.get<Clinic[]>(`${API_BASE_URL}/clinics/`),
        ]);
        setBranches(branchesRes.data);
        setRegions(regionsRes.data);
        setClinics(clinicsRes.data);
      } catch (err) {
        setError('Failed to fetch data. Please ensure the backend server is running.');
        console.error('Error fetching data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleBranchChange = (event: SelectChangeEvent) => {
    setSelectedBranch(event.target.value as string);
    setSelectedRegion(''); // Reset region when branch changes
    setSelectedClinic(''); // Reset clinic when branch changes
  };

  const handleRegionChange = (event: SelectChangeEvent) => {
    setSelectedRegion(event.target.value as string);
    setSelectedClinic(''); // Reset clinic when region changes
  };

  const handleClinicChange = (event: SelectChangeEvent) => {
    setSelectedClinic(event.target.value as string);
  };

  const filteredRegions = regions.filter(
    (region) => region.BranchID === parseInt(selectedBranch)
  );

  const filteredClinics = clinics.filter(
    (clinic) => clinic.RegionID === parseInt(selectedRegion)
  );

  const handleSubmit = () => {
    if (selectedBranch && selectedRegion && selectedClinic) {
      alert(`Selected: Branch ${selectedBranch}, Region ${selectedRegion}, Clinic ${selectedClinic}`);
      // Here you would typically save this selection to a global state or context
      // and then navigate to the next page (e.g., dispense drugs page)
    } else {
      alert('Please select Branch, Region, and Clinic.');
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>{t('loadingData')}</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>{t('branchSelectionPage')}</Typography>

      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel id="branch-select-label">{t('branch')}</InputLabel>
        <Select
          labelId="branch-select-label"
          id="branch-select"
          value={selectedBranch}
          label={t('branch')}
          onChange={handleBranchChange}
        >
          {branches.map((branch) => (
            <MenuItem key={branch.BranchID} value={branch.BranchID.toString()}>
              {branch.BranchName}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <FormControl fullWidth sx={{ mb: 2 }} disabled={!selectedBranch}>
        <InputLabel id="region-select-label">{t('region')}</InputLabel>
        <Select
          labelId="region-select-label"
          id="region-select"
          value={selectedRegion}
          label={t('region')}
          onChange={handleRegionChange}
        >
          {filteredRegions.map((region) => (
            <MenuItem key={region.RegionID} value={region.RegionID.toString()}>
              {region.RegionName}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <FormControl fullWidth sx={{ mb: 2 }} disabled={!selectedRegion}>
        <InputLabel id="clinic-select-label">{t('clinic')}</InputLabel>
        <Select
          labelId="clinic-select-label"
          id="clinic-select"
          value={selectedClinic}
          label={t('clinic')}
          onChange={handleClinicChange}
        >
          {filteredClinics.map((clinic) => (
            <MenuItem key={clinic.ClinicID} value={clinic.ClinicID.toString()}>
              {clinic.ClinicName}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <Button
        variant="contained"
        onClick={handleSubmit}
        disabled={!selectedBranch || !selectedRegion || !selectedClinic}
      >
        {t('confirmSelection')}
      </Button>
    </Box>
  );
};

export default BranchSelection;