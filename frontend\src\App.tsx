import React, { useState, useMemo } from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import {
  Box,
  Toolbar,
  CssBaseline,
  AppBar,
  Typography,
  Button,
  Menu,
  MenuItem,
  IconButton,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import {
  Medication,
  Assessment,
  Article,
  Gavel,
  Brightness4,
  Brightness7,
  Language,
  Settings,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

// Import individual management pages
import BranchesPage from './pages/BranchesPage';
import RegionsPage from './pages/RegionsPage';
import ClinicsPage from './pages/ClinicsPage';
import DrugCategoriesPage from './pages/DrugCategoriesPage';
import DrugsPage from './pages/DrugsPage';
import DispenseDrugsPage from './pages/DispenseDrugsPage';
import BranchSelection from './pages/BranchSelection';
import InsulinPage from './pages/InsulinPage';
import DrugGroupsPage from './pages/DrugGroupsPage';
import DispensedDrugsHistoryPage from './pages/DispensedDrugsHistoryPage';
import DispensedDrugsReportsPage from './pages/DispensedDrugsReportsPage'; // New import
import ComparisonReportsPage from './pages/ComparisonReportsPage'; // New import for comparison reports
import SpecificDrugComparisonPage from './pages/SpecificDrugComparisonPage'; // New import for specific drug comparison reports

const managementSections = [
  { key: 'branches', path: '/management/branches' },
  { key: 'regions', path: '/management/regions' },
  { key: 'clinics', path: '/management/clinics' },
  { key: 'drugCategories', path: '/management/drug-categories' },
  { key: 'drugs', path: '/management/drugs' },
  { key: 'insulin', path: '/management/insulin' },
  { key: 'drugGroups', path: '/management/drug-groups' },
];

const reportSections = [
  { key: 'dispensedDrugsReports', path: '/dispensed-drugs-reports', icon: <Assessment /> },
  { key: 'comparisonReports', path: '/comparison-reports', icon: <Assessment /> },
  { key: 'specificDrugComparison', path: '/specific-drug-comparison', icon: <Assessment /> },
];

const otherSections = [
  { key: 'dispenseDrugs', path: '/dispense-drugs', icon: <Medication /> },
  { key: 'dispensedDrugsHistory', path: '/dispensed-drugs-history', icon: <Medication /> },
  { key: 'monthlyDispense', path: '/monthly-dispense', icon: <Assessment /> },
  { key: 'medicalTickets', path: '/tickets', icon: <Article /> },
  { key: 'courtRulings', path: '/court-rulings', icon: <Gavel /> },
];

function App() {
  const { t, i18n } = useTranslation();
  const [sectionsAnchorEl, setSectionsAnchorEl] = useState<null | HTMLElement>(null);
  const [managementAnchorEl, setManagementAnchorEl] = useState<null | HTMLElement>(null);
  const [reportsAnchorEl, setReportsAnchorEl] = useState<null | HTMLElement>(null); // New state for reports menu
  const [mode, setMode] = useState<'light' | 'dark'>('light');
  const sectionsOpen = Boolean(sectionsAnchorEl);
  const managementOpen = Boolean(managementAnchorEl);
  const reportsOpen = Boolean(reportsAnchorEl); // New open state for reports menu

  const colorMode = useMemo(
    () => ({
      toggleColorMode: () => {
        setMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
      },
    }),
    [],
  );

  const theme = useMemo(
    () =>
      createTheme({
        palette: {
          mode,
        },
      }),
    [mode],
  );

  const handleSectionsMenu = (event: React.MouseEvent<HTMLElement>) => {
    setSectionsAnchorEl(event.currentTarget);
  };

  const handleManagementMenu = (event: React.MouseEvent<HTMLElement>) => {
    setManagementAnchorEl(event.currentTarget);
  };

  const handleReportsMenu = (event: React.MouseEvent<HTMLElement>) => { // New handler for reports menu
    setReportsAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setSectionsAnchorEl(null);
    setManagementAnchorEl(null);
    setReportsAnchorEl(null); // Close reports menu as well
  };

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <ThemeProvider theme={theme}>
      <Router>
        <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh', bgcolor: 'background.default', color: 'text.primary' }}>
          <CssBaseline />
          <AppBar position="static">
            <Toolbar>
              <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                {t('appTitle')}
              </Typography>
              
              {/* Home Button */}
              <Button color="inherit" component={Link} to="/">
                {t('home')}
              </Button>

              {/* Management Button and Submenu */}
              <Button color="inherit" onClick={handleManagementMenu}>
                <Settings sx={{ mr: 1 }} />{t('management')}
              </Button>
              <Menu
                anchorEl={managementAnchorEl}
                open={managementOpen}
                onClose={handleClose}
              >
                {managementSections.map((section) => (
                  <MenuItem 
                    key={section.key} 
                    onClick={handleClose} 
                    component={Link} 
                    to={section.path}
                  >
                    {t(section.key)}
                  </MenuItem>
                ))}
              </Menu>

              {/* Reports Button and Menu */}
              <Button color="inherit" onClick={handleReportsMenu}>
                {t('reports')}
              </Button>
              <Menu
                anchorEl={reportsAnchorEl}
                open={reportsOpen}
                onClose={handleClose}
              >
                {reportSections.map((section) => (
                  <MenuItem 
                    key={section.key} 
                    onClick={handleClose} 
                    component={Link} 
                    to={section.path}
                  >
                    <ListItemIcon>
                      {section.icon}
                    </ListItemIcon>
                    <ListItemText>{t(section.key)}</ListItemText>
                  </MenuItem>
                ))}
              </Menu>

              {/* Other Sections Button and Menu */}
              <Button color="inherit" onClick={handleSectionsMenu}>
                {t('sections')}
              </Button>
              <Menu
                anchorEl={sectionsAnchorEl}
                open={sectionsOpen}
                onClose={handleClose}
              >
                {otherSections.map((section) => (
                  <MenuItem 
                    key={section.key} 
                    onClick={handleClose} 
                    component={Link} 
                    to={section.path}
                  >
                    <ListItemIcon>
                      {section.icon}
                    </ListItemIcon>
                    <ListItemText>{t(section.key)}</ListItemText>
                  </MenuItem>
                ))}
              </Menu>

              <IconButton sx={{ ml: 1 }} onClick={colorMode.toggleColorMode} color="inherit">
                {theme.palette.mode === 'dark' ? <Brightness7 /> : <Brightness4 />}
              </IconButton>

              <Button color="inherit" onClick={() => changeLanguage(i18n.language === 'ar' ? 'en' : 'ar')}>
                <Language /> {i18n.language === 'ar' ? 'English' : 'العربية'}
              </Button>

            </Toolbar>
          </AppBar>
          <Box component="main" sx={{ flexGrow: 1, p: 3, overflow: 'auto' }}>
            <Routes>
              <Route path="/" element={<Typography>{t('welcomeMessage')}</Typography>} />
              <Route path="/branch-selection" element={<BranchSelection />} />
              <Route path="/management/branches" element={<BranchesPage />} />
              <Route path="/management/regions" element={<RegionsPage />} />
              <Route path="/management/clinics" element={<ClinicsPage />} />
              <Route path="/management/drug-categories" element={<DrugCategoriesPage />} />
              <Route path="/management/drugs" element={<DrugsPage />} />
              <Route path="/dispense-drugs" element={<DispenseDrugsPage />} />
              <Route path="/management/insulin" element={<InsulinPage />} />
              <Route path="/management/drug-groups" element={<DrugGroupsPage />} />
              <Route path="/monthly-dispense" element={<Typography>{t('monthlyDispense')}</Typography>} />
              <Route path="/tickets" element={<Typography>{t('medicalTickets')}</Typography>} />
              <Route path="/court-rulings" element={<Typography>{t('courtRulings')}</Typography>} />
              <Route path="/dispensed-drugs-history" element={<DispensedDrugsHistoryPage />} />
              <Route path="/dispensed-drugs-reports" element={<DispensedDrugsReportsPage />} /> {/* New Route */}
              <Route path="/comparison-reports" element={<ComparisonReportsPage />} />
              <Route path="/specific-drug-comparison" element={<SpecificDrugComparisonPage />} />
            </Routes>
          </Box>
        </Box>
      </Router>
    </ThemeProvider>
  );
}

export default App;