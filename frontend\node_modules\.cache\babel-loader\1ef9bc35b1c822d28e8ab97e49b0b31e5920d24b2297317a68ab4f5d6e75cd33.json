{"ast": null, "code": "var _jsxFileName = \"E:\\\\Python\\\\cmder\\\\drug_dispensing_app\\\\frontend\\\\src\\\\pages\\\\DispensedDrugsReportsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport axios from 'axios';\nimport { Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, FormControl, InputLabel, Select, MenuItem, Snackbar, Alert, Button, TextField, Menu } from '@mui/material';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport dayjs from 'dayjs';\nimport 'dayjs/locale/ar'; // Import Arabic locale\nimport 'dayjs/locale/en'; // Import English locale\n// Import i18n instance\n\nimport * as XLSX from 'xlsx';\n\n// Interfaces for data from backend\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DispensedDrugsReportsPage = () => {\n  _s();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  dayjs.locale(i18n.language); // Set dayjs locale based on i18n language\n\n  useEffect(() => {\n    console.log(\"Current i18n language:\", i18n.language);\n    dayjs.locale(i18n.language);\n  }, [i18n.language]);\n  const [branches, setBranches] = useState([]);\n  const [regions, setRegions] = useState([]);\n  const [clinics, setClinics] = useState([]);\n  const [drugs, setDrugs] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [filterBranchId, setFilterBranchId] = useState('');\n  const [filterRegionId, setFilterRegionId] = useState('');\n  const [filterClinicId, setFilterClinicId] = useState('');\n  const [filterStartDate, setFilterStartDate] = useState(null);\n  const [filterMonth, setFilterMonth] = useState(null);\n  const [filterEndDate, setFilterEndDate] = useState(null);\n  const [filterQuarter, setFilterQuarter] = useState('');\n  const [filterYearForQuarter, setFilterYearForQuarter] = useState(new Date().getFullYear());\n  const [clinicAggregatedData, setClinicAggregatedData] = useState([]);\n  const [regionAggregatedData, setRegionAggregatedData] = useState([]);\n  const [branchAggregatedData, setBranchAggregatedData] = useState([]);\n  const [reportDateRange, setReportDateRange] = useState({\n    key: 'reportForTheSelectedPeriod'\n  });\n  const [snackbar, setSnackbar] = useState(null);\n  useEffect(() => {\n    fetchBranches();\n    fetchRegions();\n    fetchClinics();\n    fetchDrugs();\n    fetchCategories();\n  }, []);\n  useEffect(() => {\n    fetchAggregatedData();\n  }, [filterBranchId, filterRegionId, filterClinicId, filterMonth, filterStartDate, filterEndDate, filterQuarter, filterYearForQuarter, i18n.language]); // Add i18n.language to dependencies\n\n  const fetchBranches = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/branches/');\n      setBranches(response.data);\n    } catch (error) {\n      console.error('Error fetching branches:', error);\n      setSnackbar({\n        open: true,\n        message: t('fetchDataError'),\n        severity: 'error'\n      });\n    }\n  };\n  const fetchRegions = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/regions/');\n      setRegions(response.data);\n    } catch (error) {\n      console.error('Error fetching regions:', error);\n      setSnackbar({\n        open: true,\n        message: t('fetchDataError'),\n        severity: 'error'\n      });\n    }\n  };\n  const fetchClinics = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/clinics/');\n      setClinics(response.data);\n    } catch (error) {\n      console.error('Error fetching clinics:', error);\n      setSnackbar({\n        open: true,\n        message: t('fetchDataError'),\n        severity: 'error'\n      });\n    }\n  };\n  const fetchDrugs = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/drugs/');\n      setDrugs(response.data);\n    } catch (error) {\n      console.error('Error fetching drugs:', error);\n      setSnackbar({\n        open: true,\n        message: t('fetchDataError'),\n        severity: 'error'\n      });\n    }\n  };\n  const fetchCategories = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/drug-categories/');\n      setCategories(response.data);\n    } catch (error) {\n      console.error('Error fetching drug categories:', error);\n      setSnackbar({\n        open: true,\n        message: t('fetchDataError'),\n        severity: 'error'\n      });\n    }\n  };\n  const fetchAggregatedData = async () => {\n    const monthParam = filterMonth ? filterMonth.format('YYYY-MM') : '';\n    const startDateParam = filterStartDate ? filterStartDate.format('YYYY-MM') : '';\n    const endDateParam = filterEndDate ? filterEndDate.format('YYYY-MM') : '';\n    const quarterParam = filterQuarter ? `${filterYearForQuarter}-Q${filterQuarter}` : '';\n    if (monthParam) {\n      setReportDateRange({\n        key: 'forTheMonthOf',\n        params: {\n          monthYear: dayjs(monthParam).format('MMMM YYYY')\n        }\n      });\n    } else if (startDateParam && endDateParam) {\n      setReportDateRange({\n        key: 'forThePeriod',\n        params: {\n          startMonthYear: dayjs(startDateParam).format('MMMM YYYY'),\n          endMonthYear: dayjs(endDateParam).format('MMMM YYYY')\n        }\n      });\n    } else if (quarterParam) {\n      const [year, q] = quarterParam.split('-Q');\n      setReportDateRange({\n        key: 'forQuarter',\n        params: {\n          quarter: q,\n          year: year\n        }\n      });\n    } else {\n      setReportDateRange({\n        key: 'reportForTheSelectedPeriod'\n      });\n    }\n    const params = {\n      ...(filterBranchId && {\n        branch_id: filterBranchId\n      }),\n      ...(filterRegionId && {\n        region_id: filterRegionId\n      }),\n      ...(filterClinicId && {\n        clinic_id: filterClinicId\n      }),\n      ...(monthParam && {\n        month: monthParam\n      }),\n      ...(startDateParam && {\n        start_month: startDateParam\n      }),\n      ...(endDateParam && {\n        end_month: endDateParam\n      }),\n      ...(quarterParam && {\n        quarter: quarterParam\n      })\n    };\n    try {\n      // Fetch by Clinic\n      const clinicResponse = await axios.get('http://localhost:8000/reports/dispensed-drugs/by-clinic', {\n        params\n      });\n      setClinicAggregatedData(clinicResponse.data);\n\n      // Fetch by Region (if not filtering by specific clinic)\n      if (!filterClinicId) {\n        const regionResponse = await axios.get('http://localhost:8000/reports/dispensed-drugs/by-region', {\n          params\n        });\n        setRegionAggregatedData(regionResponse.data);\n      } else {\n        setRegionAggregatedData([]); // Clear if clinic filter is active\n      }\n\n      // Fetch by Branch (if not filtering by specific clinic or region)\n      if (!filterClinicId && !filterRegionId) {\n        const branchResponse = await axios.get('http://localhost:8000/reports/dispensed-drugs/by-branch', {\n          params\n        });\n        setBranchAggregatedData(branchResponse.data);\n      } else {\n        setBranchAggregatedData([]); // Clear if clinic or region filter is active\n      }\n    } catch (error) {\n      console.error('Error fetching aggregated data:', error);\n      setSnackbar({\n        open: true,\n        message: t('fetchDataError'),\n        severity: 'error'\n      });\n    }\n  };\n  const handleResetFilters = () => {\n    setFilterBranchId('');\n    setFilterRegionId('');\n    setFilterClinicId('');\n    setFilterMonth(null);\n    setFilterStartDate(null);\n    setFilterEndDate(null);\n    setFilterQuarter('');\n    setFilterYearForQuarter(new Date().getFullYear());\n  };\n  const getClinicName = clinicId => {\n    const clinic = clinics.find(c => c.id === clinicId);\n    return clinic ? clinic.name : t('unknownClinic');\n  };\n  const getRegionName = regionId => {\n    const region = regions.find(r => r.id === regionId);\n    return region ? region.name : t('unknownRegion');\n  };\n  const getBranchName = branchId => {\n    const branch = branches.find(b => b.id === branchId);\n    return branch ? branch.name : t('unknownBranch');\n  };\n  const getDrugName = (drugId, drugUnit) => {\n    const drug = drugs.find(d => d.DrugID === drugId);\n    return drug ? `${drug.DrugName} (${drugUnit || drug.Unit || 'N/A'})` : t('unknownDrug');\n  };\n  const getCategoryName = categoryId => {\n    const category = categories.find(c => c.CategoryID === categoryId);\n    return category ? category.CategoryName : t('unknownCategory');\n  };\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const open = Boolean(anchorEl);\n  const handleClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  const reportRef = useRef(null);\n  const handlePrint = () => {\n    const printContent = reportRef.current;\n    if (printContent) {\n      const printWindow = window.open('', '', 'height=600,width=800');\n      if (printWindow) {\n        var _document$querySelect;\n        const title = ((_document$querySelect = document.querySelector('h4')) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.innerText) || 'Dispensed Drugs Report';\n        const styles = `\n          <style>\n            body { font-family: 'Amiri', Arial, sans-serif; direction: rtl; }\n            @media print { \n              body { -webkit-print-color-adjust: exact; font-family: Amiri, Arial, sans-serif !important; } \n              .no-print { display: none; } \n            }\n            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }\n            th { background-color: #f2f2f2; }\n            h4, h5, h6 { text-align: center; }\n          </style>\n          <link href=\"https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap\" rel=\"stylesheet\">\n        `;\n        printWindow.document.write(`\n          <html>\n            <head>\n              <title>${title}</title>\n              ${styles}\n            </head>\n            <body dir=\"rtl\" onload=\"window.print(); window.close();\">\n              <h4>${title}</h4>\n              ${printContent.innerHTML}\n            </body>\n          </html>\n        `);\n        printWindow.document.close();\n      }\n    }\n    handleClose();\n  };\n  const handleExportExcel = () => {\n    const report = reportRef.current;\n    if (report) {\n      var _document$querySelect2, _report$querySelector;\n      const mainTitle = ((_document$querySelect2 = document.querySelector('h4')) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.innerText) || 'Dispensed Drugs Report';\n      const dateTitle = ((_report$querySelector = report.querySelector('h6[gutterBottom]')) === null || _report$querySelector === void 0 ? void 0 : _report$querySelector.innerText) || '';\n      const aoa = [];\n      aoa.push([mainTitle]);\n      if (dateTitle) {\n        aoa.push([dateTitle]);\n      }\n      aoa.push([]); // Spacer row\n\n      const elements = report.querySelectorAll('h5, h6, table');\n      let currentAoARow = aoa.length; // Tracks the current row index in the aoa array\n\n      // This array will store the starting row index and number of rows for each table in the AOA\n      const tableInfo = [];\n      const titleInfo = [];\n      elements.forEach(el => {\n        if (el.tagName === 'H5' || el.tagName === 'H6') {\n          if (el.getAttribute('gutterBottom') === '') {\n            return;\n          }\n          const titleText = el.textContent || '';\n          aoa.push([titleText]);\n          titleInfo.push({\n            row: currentAoARow,\n            text: titleText\n          });\n          currentAoARow++;\n        } else if (el.tagName === 'TABLE') {\n          var _tableData$;\n          const table = el;\n          const rows = table.querySelectorAll('tr');\n          const tableData = [];\n          rows.forEach(row => {\n            const rowData = [];\n            const cells = row.querySelectorAll('th, td');\n            cells.forEach(cell => {\n              rowData.push(cell.textContent || '');\n            });\n            tableData.push(rowData);\n          });\n          tableInfo.push({\n            startRow: currentAoARow,\n            numRows: tableData.length,\n            numCols: ((_tableData$ = tableData[0]) === null || _tableData$ === void 0 ? void 0 : _tableData$.length) || 0\n          });\n\n          // Add table data to AOA\n          tableData.forEach(row => {\n            aoa.push(row);\n          });\n          currentAoARow += tableData.length;\n          aoa.push([]); // Spacer after table\n          currentAoARow++;\n        }\n      });\n      const ws = XLSX.utils.aoa_to_sheet(aoa);\n      const wb = XLSX.utils.book_new();\n\n      // Calculate maximum column width for auto-sizing\n      const maxCols = Math.max(...tableInfo.map(info => info.numCols), 1);\n      const colWidths = [];\n      for (let c = 0; c < maxCols; c++) {\n        let maxWidth = 10; // Minimum width\n        for (let r = 0; r < aoa.length; r++) {\n          if (aoa[r] && aoa[r][c]) {\n            const cellValue = String(aoa[r][c]);\n            maxWidth = Math.max(maxWidth, cellValue.length);\n          }\n        }\n        colWidths.push({\n          wch: Math.min(maxWidth + 2, 50)\n        }); // Add padding and cap at 50\n      }\n      ws['!cols'] = colWidths;\n\n      // Style main title (first row)\n      if (aoa.length > 0) {\n        const mainTitleCell = XLSX.utils.encode_cell({\n          r: 0,\n          c: 0\n        });\n        if (!ws[mainTitleCell]) ws[mainTitleCell] = {\n          v: aoa[0][0]\n        };\n        ws[mainTitleCell].s = {\n          font: {\n            bold: true,\n            size: 16,\n            color: {\n              rgb: \"FF000080\"\n            }\n          },\n          alignment: {\n            horizontal: \"center\",\n            vertical: \"center\"\n          },\n          fill: {\n            fgColor: {\n              rgb: \"FFE6F3FF\"\n            }\n          },\n          // Light blue background\n          border: {\n            top: {\n              style: \"medium\",\n              color: {\n                rgb: \"FF000080\"\n              }\n            },\n            bottom: {\n              style: \"medium\",\n              color: {\n                rgb: \"FF000080\"\n              }\n            },\n            left: {\n              style: \"medium\",\n              color: {\n                rgb: \"FF000080\"\n              }\n            },\n            right: {\n              style: \"medium\",\n              color: {\n                rgb: \"FF000080\"\n              }\n            }\n          }\n        };\n      }\n\n      // Style date title (second row if exists)\n      if (aoa.length > 1 && dateTitle) {\n        const dateTitleCell = XLSX.utils.encode_cell({\n          r: 1,\n          c: 0\n        });\n        if (!ws[dateTitleCell]) ws[dateTitleCell] = {\n          v: aoa[1][0]\n        };\n        ws[dateTitleCell].s = {\n          font: {\n            bold: true,\n            size: 12,\n            color: {\n              rgb: \"FF000060\"\n            }\n          },\n          alignment: {\n            horizontal: \"center\",\n            vertical: \"center\"\n          },\n          fill: {\n            fgColor: {\n              rgb: \"FFF0F8FF\"\n            }\n          },\n          // Very light blue background\n          border: {\n            top: {\n              style: \"thin\",\n              color: {\n                rgb: \"FF000060\"\n              }\n            },\n            bottom: {\n              style: \"thin\",\n              color: {\n                rgb: \"FF000060\"\n              }\n            },\n            left: {\n              style: \"thin\",\n              color: {\n                rgb: \"FF000060\"\n              }\n            },\n            right: {\n              style: \"thin\",\n              color: {\n                rgb: \"FF000060\"\n              }\n            }\n          }\n        };\n      }\n\n      // Style section titles (H5, H6)\n      titleInfo.forEach(title => {\n        const titleCell = XLSX.utils.encode_cell({\n          r: title.row,\n          c: 0\n        });\n        if (!ws[titleCell]) ws[titleCell] = {\n          v: title.text\n        };\n        ws[titleCell].s = {\n          font: {\n            bold: true,\n            size: 14,\n            color: {\n              rgb: \"FF2E4057\"\n            }\n          },\n          alignment: {\n            horizontal: \"center\",\n            vertical: \"center\"\n          },\n          fill: {\n            fgColor: {\n              rgb: \"FFDDEEFF\"\n            }\n          },\n          // Light purple background\n          border: {\n            top: {\n              style: \"medium\",\n              color: {\n                rgb: \"FF2E4057\"\n              }\n            },\n            bottom: {\n              style: \"medium\",\n              color: {\n                rgb: \"FF2E4057\"\n              }\n            },\n            left: {\n              style: \"medium\",\n              color: {\n                rgb: \"FF2E4057\"\n              }\n            },\n            right: {\n              style: \"medium\",\n              color: {\n                rgb: \"FF2E4057\"\n              }\n            }\n          }\n        };\n      });\n\n      // Apply styles to tables\n      tableInfo.forEach(info => {\n        const headerRowIndex = info.startRow;\n        const headerCellsCount = info.numCols;\n\n        // Apply styles to the table headers (first row of each table)\n        for (let i = 0; i < headerCellsCount; i++) {\n          const cellRef = XLSX.utils.encode_cell({\n            r: headerRowIndex,\n            c: i\n          });\n          if (!ws[cellRef]) {\n            ws[cellRef] = {\n              v: aoa[headerRowIndex][i]\n            };\n          }\n          ws[cellRef].s = {\n            font: {\n              bold: true,\n              size: 11,\n              color: {\n                rgb: \"FFFFFFFF\"\n              }\n            },\n            // White text\n            alignment: {\n              horizontal: \"center\",\n              vertical: \"center\"\n            },\n            fill: {\n              fgColor: {\n                rgb: \"FF4472C4\"\n              }\n            },\n            // Blue background\n            border: {\n              top: {\n                style: \"medium\",\n                color: {\n                  rgb: \"FF2E4057\"\n                }\n              },\n              bottom: {\n                style: \"medium\",\n                color: {\n                  rgb: \"FF2E4057\"\n                }\n              },\n              left: {\n                style: \"medium\",\n                color: {\n                  rgb: \"FF2E4057\"\n                }\n              },\n              right: {\n                style: \"medium\",\n                color: {\n                  rgb: \"FF2E4057\"\n                }\n              }\n            }\n          };\n        }\n\n        // Apply styles to all data cells in the table\n        for (let r = 1; r < info.numRows; r++) {\n          // Start from 1 to skip header\n          for (let c = 0; c < info.numCols; c++) {\n            const cellRef = XLSX.utils.encode_cell({\n              r: info.startRow + r,\n              c: c\n            });\n            if (!ws[cellRef]) {\n              ws[cellRef] = {\n                v: aoa[info.startRow + r][c]\n              };\n            }\n\n            // Alternate row colors for better readability\n            const isEvenRow = r % 2 === 0;\n            ws[cellRef].s = {\n              font: {\n                size: 10\n              },\n              alignment: {\n                horizontal: \"center\",\n                vertical: \"center\"\n              },\n              fill: {\n                fgColor: {\n                  rgb: isEvenRow ? \"FFF8F9FA\" : \"FFFFFFFF\"\n                }\n              },\n              // Alternating light gray and white\n              border: {\n                top: {\n                  style: \"thin\",\n                  color: {\n                    rgb: \"FFD0D0D0\"\n                  }\n                },\n                bottom: {\n                  style: \"thin\",\n                  color: {\n                    rgb: \"FFD0D0D0\"\n                  }\n                },\n                left: {\n                  style: \"thin\",\n                  color: {\n                    rgb: \"FFD0D0D0\"\n                  }\n                },\n                right: {\n                  style: \"thin\",\n                  color: {\n                    rgb: \"FFD0D0D0\"\n                  }\n                }\n              }\n            };\n          }\n        }\n      });\n      XLSX.utils.book_append_sheet(wb, ws, 'تقرير الأدوية المنصرفة');\n      XLSX.writeFile(wb, 'تقرير_الأدوية_المنصرفة.xlsx', {\n        cellStyles: true\n      });\n    }\n    handleClose();\n  };\n  const handleExportPdf = async () => {\n    var _document$querySelect3;\n    const reportContentEl = reportRef.current;\n    if (!reportContentEl) return;\n    const title = ((_document$querySelect3 = document.querySelector('h4')) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.innerText) || 'Dispensed Drugs Report';\n    const styles = `\n      <style>\n        body { font-family: 'Amiri', sans-serif; direction: rtl; font-size: 14px; }\n        table { width: 100%; border-collapse: collapse; margin-top: 10px; margin-bottom: 20px; font-size: 14px; }\n        th, td { border: 1px solid #333; padding: 8px; text-align: right; }\n        th { background-color: #f2f2f2; font-weight: bold; }\n        h4, h5, h6 { text-align: center; margin: 15px 0; }\n        h4 { font-size: 24px; }\n        h5 { font-size: 20px; }\n        h6 { font-size: 18px; }\n      </style>\n      <link href=\"https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap\" rel=\"stylesheet\">\n    `;\n    const htmlString = `\n      <!DOCTYPE html>\n      <html lang=\"ar\">\n      <head>\n        <meta charset=\"UTF-8\">\n        <title>${title}</title>\n        ${styles}\n      </head>\n      <body>\n        <h4>${title}</h4>\n        ${reportContentEl.innerHTML}\n      </body>\n      </html>\n    `;\n    try {\n      var _link$parentNode;\n      const response = await axios.post('http://localhost:8000/generate-pdf-report', {\n        html_content: htmlString,\n        title: title\n      }, {\n        responseType: 'blob'\n      });\n      const url = window.URL.createObjectURL(new Blob([response.data], {\n        type: 'application/pdf'\n      }));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', `${title}.pdf`);\n      document.body.appendChild(link);\n      link.click();\n      (_link$parentNode = link.parentNode) === null || _link$parentNode === void 0 ? void 0 : _link$parentNode.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      setSnackbar({\n        open: true,\n        message: t('pdfExportError'),\n        severity: 'error'\n      });\n    }\n    handleClose();\n  };\n  return /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n    dateAdapter: AdapterDayjs,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: t('dispensedDrugsReports')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2,\n          mb: 3,\n          flexWrap: 'wrap'\n        },\n        className: \"no-print\",\n        children: [/*#__PURE__*/_jsxDEV(FormControl, {\n          sx: {\n            minWidth: 180\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: t('filterByBranch')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: filterBranchId,\n            label: t('filterByBranch'),\n            onChange: e => {\n              setFilterBranchId(e.target.value);\n              setFilterRegionId(''); // Reset region when branch changes\n              setFilterClinicId(''); // Reset clinic when branch changes\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: t('allBranches')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 15\n            }, this), branches.map(branch => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: branch.id,\n              children: branch.name\n            }, branch.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          sx: {\n            minWidth: 180\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: t('filterByRegion')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: filterRegionId,\n            label: t('filterByRegion'),\n            onChange: e => {\n              setFilterRegionId(e.target.value);\n              setFilterClinicId(''); // Reset clinic when region changes\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: t('allRegions')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 15\n            }, this), regions.filter(region => filterBranchId === '' || region.branch_id === filterBranchId).map(region => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: region.id,\n              children: region.name\n            }, region.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          sx: {\n            minWidth: 180\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: t('filterByClinic')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: filterClinicId,\n            label: t('filterByClinic'),\n            onChange: e => setFilterClinicId(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: t('allClinics')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 15\n            }, this), clinics.filter(clinic => filterRegionId === '' || clinic.region_id === filterRegionId).map(clinic => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: clinic.id,\n              children: clinic.name\n            }, clinic.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n          label: t('filterByMonth'),\n          views: ['month', 'year'],\n          value: filterMonth,\n          onChange: value => setFilterMonth(value),\n          slots: {\n            textField: TextField\n          },\n          slotProps: {\n            textField: {\n              fullWidth: true,\n              sx: {\n                minWidth: 180\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n          label: t('filterByStartMonth'),\n          views: ['month', 'year'],\n          value: filterStartDate,\n          onChange: value => setFilterStartDate(value),\n          slots: {\n            textField: TextField\n          },\n          slotProps: {\n            textField: {\n              fullWidth: true,\n              sx: {\n                minWidth: 180\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n          label: t('filterByEndMonth'),\n          views: ['month', 'year'],\n          value: filterEndDate,\n          onChange: value => setFilterEndDate(value),\n          slots: {\n            textField: TextField\n          },\n          slotProps: {\n            textField: {\n              fullWidth: true,\n              sx: {\n                minWidth: 180\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          sx: {\n            minWidth: 180\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: t('filterByQuarter')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: filterQuarter,\n            label: t('filterByQuarter'),\n            onChange: e => setFilterQuarter(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: t('allQuarters')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: 1,\n              children: t('quarter1')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: 2,\n              children: t('quarter2')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: 3,\n              children: t('quarter3')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: 4,\n              children: t('quarter4')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          sx: {\n            minWidth: 120\n          },\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            label: t('yearForQuarter'),\n            type: \"number\",\n            value: filterYearForQuarter,\n            onChange: e => setFilterYearForQuarter(parseInt(e.target.value, 10))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: handleResetFilters,\n          children: t('resetFilters')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          id: \"export-button\",\n          \"aria-controls\": open ? 'export-menu' : undefined,\n          \"aria-haspopup\": \"true\",\n          \"aria-expanded\": open ? 'true' : undefined,\n          onClick: handleClick,\n          variant: \"contained\",\n          children: t('export')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 730,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          id: \"export-menu\",\n          anchorEl: anchorEl,\n          open: open,\n          onClose: handleClose,\n          MenuListProps: {\n            'aria-labelledby': 'export-button'\n          },\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: handlePrint,\n            children: t('print')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: handleExportExcel,\n            children: t('exportToExcel')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: handleExportPdf,\n            children: t('exportToPdf')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: reportRef,\n        style: {\n          fontFamily: 'Amiri, Arial, sans-serif'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            mt: 2,\n            textAlign: 'center'\n          },\n          children: String(t(reportDateRange.key, reportDateRange.params))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 11\n        }, this), filterClinicId ?\n        /*#__PURE__*/\n        // Display Clinic-level aggregation if a specific clinic is selected\n        _jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            sx: {\n              mt: 4\n            },\n            children: [t('clinicReportFor'), \" \", getClinicName(filterClinicId)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: t('drug')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: t('category')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 771,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: t('unitPrice')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: t('totalQuantity')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 773,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: t('numberOfCases')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 774,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: t('totalCost')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 775,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: clinicAggregatedData.map((data, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: getDrugName(data.DrugID, data.DrugUnit)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: getCategoryName(data.CategoryID)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: data.UnitPrice\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: data.TotalQuantity\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: data.NumberOfCases\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 785,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: data.TotalCost\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 13\n        }, this) : filterRegionId ?\n        /*#__PURE__*/\n        // Display Region-level aggregation if a specific region is selected (and no clinic)\n        _jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            sx: {\n              mt: 4\n            },\n            children: [t('regionReportFor'), \" \", getRegionName(filterRegionId)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 15\n          }, this), regions.filter(r => r.id === filterRegionId).map(selectedRegion => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 4\n            },\n            children: [clinics.filter(clinic => clinic.region_id === selectedRegion.id).map(clinic => {\n              const clinicData = clinicAggregatedData.filter(d => d.ClinicID === clinic.id);\n              if (clinicData.length === 0) return null;\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    mt: 2\n                  },\n                  children: [t('clinic'), \": \", clinic.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 29\n                }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n                  component: Paper,\n                  children: /*#__PURE__*/_jsxDEV(Table, {\n                    size: \"small\",\n                    children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                      children: /*#__PURE__*/_jsxDEV(TableRow, {\n                        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('drug')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 818,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('category')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 819,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('unitPrice')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 820,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('totalQuantity')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 821,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('numberOfCases')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 822,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('totalCost')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 823,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 817,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 816,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                      children: clinicData.map((data, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                          children: getDrugName(data.DrugID, data.DrugUnit)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 829,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: getCategoryName(data.CategoryID)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 830,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: data.UnitPrice\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 831,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: data.TotalQuantity\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 832,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: data.NumberOfCases\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 833,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: data.TotalCost\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 834,\n                          columnNumber: 39\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 828,\n                        columnNumber: 37\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 826,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 815,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 29\n                }, this)]\n              }, clinic.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 27\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mt: 4\n              },\n              children: [t('totalForRegion'), \": \", selectedRegion.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('drug')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 850,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('category')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 851,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('unitPrice')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 852,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('totalQuantity')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 853,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('numberOfCases')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 854,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('totalCost')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 855,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 849,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: regionAggregatedData.map((data, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: getDrugName(data.DrugID, data.DrugUnit)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 861,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: getCategoryName(data.CategoryID)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 862,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: data.UnitPrice\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 863,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: data.TotalQuantity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 864,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: data.NumberOfCases\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: data.TotalCost\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 866,\n                      columnNumber: 31\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 21\n            }, this)]\n          }, selectedRegion.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 19\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 13\n        }, this) : filterBranchId ?\n        /*#__PURE__*/\n        // Display Branch-level aggregation if a specific branch is selected (and no clinic/region)\n        _jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            sx: {\n              mt: 4\n            },\n            children: [t('branchReportFor'), \" \", getBranchName(filterBranchId)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 15\n          }, this), branches.filter(b => b.id === filterBranchId).map(selectedBranch => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 4\n            },\n            children: [regions.filter(region => region.branch_id === selectedBranch.id).map(region => {\n              const regionData = regionAggregatedData.filter(d => d.RegionID === region.id);\n              if (regionData.length === 0) return null;\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    mt: 2\n                  },\n                  children: [t('region'), \": \", region.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 29\n                }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n                  component: Paper,\n                  children: /*#__PURE__*/_jsxDEV(Table, {\n                    size: \"small\",\n                    children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                      children: /*#__PURE__*/_jsxDEV(TableRow, {\n                        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('drug')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 900,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('category')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 901,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('unitPrice')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 902,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('totalQuantity')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 903,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('numberOfCases')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 904,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: t('totalCost')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 905,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 899,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                      children: regionData.map((data, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                          children: getDrugName(data.DrugID, data.DrugUnit)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 911,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: getCategoryName(data.CategoryID)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 912,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: data.UnitPrice\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 913,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: data.TotalQuantity\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 914,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: data.NumberOfCases\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 915,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                          children: data.TotalCost\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 916,\n                          columnNumber: 39\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 910,\n                        columnNumber: 37\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 908,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 897,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 896,\n                  columnNumber: 29\n                }, this)]\n              }, region.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 27\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mt: 4\n              },\n              children: [t('totalForBranch'), \": \", selectedBranch.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 925,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('drug')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 932,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('category')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 933,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('unitPrice')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('totalQuantity')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 935,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('numberOfCases')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 936,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: t('totalCost')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 937,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: branchAggregatedData.map((data, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: getDrugName(data.DrugID, data.DrugUnit)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 943,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: getCategoryName(data.CategoryID)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 944,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: data.UnitPrice\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 945,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: data.TotalQuantity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 946,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: data.NumberOfCases\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 947,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: data.TotalCost\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 948,\n                      columnNumber: 31\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 929,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 21\n            }, this)]\n          }, selectedBranch.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 885,\n            columnNumber: 19\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 13\n        }, this) :\n        /*#__PURE__*/\n        // Default view or message if no filters are applied\n        _jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 4\n          },\n          children: t('selectFiltersForReport')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 959,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 755,\n        columnNumber: 9\n      }, this), snackbar && /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: snackbar.open,\n        autoHideDuration: 6000,\n        onClose: () => setSnackbar(null),\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: () => setSnackbar(null),\n          severity: snackbar.severity,\n          sx: {\n            width: '100%'\n          },\n          children: snackbar.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 972,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 966,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 590,\n    columnNumber: 5\n  }, this);\n};\n_s(DispensedDrugsReportsPage, \"HeZFRMEk2qmYVpbGBpjMb/fJUkc=\", false, function () {\n  return [useTranslation];\n});\n_c = DispensedDrugsReportsPage;\nexport default DispensedDrugsReportsPage;\nvar _c;\n$RefreshReg$(_c, \"DispensedDrugsReportsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useTranslation", "axios", "Box", "Typography", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "FormControl", "InputLabel", "Select", "MenuItem", "Snackbar", "<PERSON><PERSON>", "<PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "DatePicker", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LocalizationProvider", "dayjs", "XLSX", "jsxDEV", "_jsxDEV", "DispensedDrugsReportsPage", "_s", "t", "i18n", "locale", "language", "console", "log", "branches", "setBranches", "regions", "setRegions", "clinics", "setClinics", "drugs", "setDrugs", "categories", "setCategories", "filterBranchId", "setFilterBranchId", "filterRegionId", "setFilterRegionId", "filterClinicId", "setFilterClinicId", "filterStartDate", "setFilterStartDate", "filterMonth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filterEndDate", "setFilterEndDate", "filterQuarter", "setFilterQuarter", "filterYearForQuarter", "setFilterYearForQuarter", "Date", "getFullYear", "clinicAggregatedData", "setClinicAggregatedData", "regionAggregatedData", "setRegionAggregatedData", "branchAggregatedData", "setBranchAggregatedData", "reportDateRange", "setReportDateRange", "key", "snackbar", "setSnackbar", "fetchBranches", "fetchRegions", "fetchClinics", "fetchDrugs", "fetchCategories", "fetchAggregatedData", "response", "get", "data", "error", "open", "message", "severity", "month<PERSON>ara<PERSON>", "format", "startDateParam", "endDateParam", "quarterParam", "params", "monthYear", "startMonthYear", "endMonthYear", "year", "q", "split", "quarter", "branch_id", "region_id", "clinic_id", "month", "start_month", "end_month", "clinicResponse", "regionResponse", "branchResponse", "handleResetFilters", "getClinicName", "clinicId", "clinic", "find", "c", "id", "name", "getRegionName", "regionId", "region", "r", "getBranchName", "branchId", "branch", "b", "getDrugName", "drugId", "drugUnit", "drug", "d", "DrugID", "DrugName", "Unit", "getCategoryName", "categoryId", "category", "CategoryID", "CategoryName", "anchorEl", "setAnchorEl", "Boolean", "handleClick", "event", "currentTarget", "handleClose", "reportRef", "handlePrint", "printContent", "current", "printWindow", "window", "_document$querySelect", "title", "document", "querySelector", "innerText", "styles", "write", "innerHTML", "close", "handleExportExcel", "report", "_document$querySelect2", "_report$querySelector", "mainTitle", "dateTitle", "aoa", "push", "elements", "querySelectorAll", "currentAoARow", "length", "tableInfo", "titleInfo", "for<PERSON>ach", "el", "tagName", "getAttribute", "titleText", "textContent", "row", "text", "_tableData$", "table", "rows", "tableData", "rowData", "cells", "cell", "startRow", "numRows", "numCols", "ws", "utils", "aoa_to_sheet", "wb", "book_new", "maxCols", "Math", "max", "map", "info", "col<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "cellValue", "String", "wch", "min", "mainTitleCell", "encode_cell", "v", "s", "font", "bold", "size", "color", "rgb", "alignment", "horizontal", "vertical", "fill", "fgColor", "border", "top", "style", "bottom", "left", "right", "dateTitleCell", "title<PERSON>ell", "headerRowIndex", "headerCellsCount", "i", "cellRef", "isEvenRow", "book_append_sheet", "writeFile", "cellStyles", "handleExportPdf", "_document$querySelect3", "reportContentEl", "htmlString", "_link$parentNode", "post", "html_content", "responseType", "url", "URL", "createObjectURL", "Blob", "type", "link", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "dateAdapter", "children", "sx", "p", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gap", "mb", "flexWrap", "className", "min<PERSON><PERSON><PERSON>", "value", "label", "onChange", "e", "target", "filter", "views", "slots", "textField", "slotProps", "fullWidth", "parseInt", "onClick", "undefined", "onClose", "MenuListProps", "ref", "fontFamily", "mt", "textAlign", "component", "index", "DrugUnit", "UnitPrice", "TotalQuantity", "NumberOfCases", "TotalCost", "selectedRegion", "clinicData", "ClinicID", "<PERSON><PERSON><PERSON><PERSON>", "regionData", "RegionID", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "width", "_c", "$RefreshReg$"], "sources": ["E:/Python/cmder/drug_dispensing_app/frontend/src/pages/DispensedDrugsReportsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport axios from 'axios';\nimport {\n  Box,\n  Typography,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Snackbar,\n  Alert,\n  Button,\n  TextField,\n  Menu,\n} from '@mui/material';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport dayjs, { Dayjs } from 'dayjs';\nimport 'dayjs/locale/ar'; // Import Arabic locale\nimport 'dayjs/locale/en'; // Import English locale\nimport i18n from 'i18next'; // Import i18n instance\nimport jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\nimport * as XLSX from 'xlsx';\n\n// Interfaces for data from backend\ninterface Branch {\n  id: number;\n  name: string;\n}\n\ninterface Region {\n  id: number;\n  name: string;\n  branch_id: number;\n}\n\ninterface Clinic {\n  id: number;\n  name: string;\n  region_id: number;\n}\n\ninterface Drug {\n  DrugID: number;\n  DrugName: string;\n  Unit: string | null;\n  CategoryID: number;\n}\n\ninterface DrugCategory {\n  CategoryID: number;\n  CategoryName: string;\n}\n\ninterface AggregatedDispensedDrugClinic {\n  ClinicID: number;\n  ClinicName: string;\n  DrugID: number;\n  DrugName: string;\n  DrugUnit: string | null;\n  CategoryID: number;\n  CategoryName: string;\n  UnitPrice: number;\n  TotalQuantity: number;\n  TotalCost: number;\n  NumberOfCases: number;\n}\n\ninterface AggregatedDispensedDrugRegion {\n  RegionID: number;\n  RegionName: string;\n  DrugID: number;\n  DrugName: string;\n  DrugUnit: string | null;\n  CategoryID: number;\n  CategoryName: string;\n  UnitPrice: number;\n  TotalQuantity: number;\n  TotalCost: number;\n  NumberOfCases: number;\n}\n\ninterface AggregatedDispensedDrugBranch {\n  BranchID: number;\n  BranchName: string;\n  DrugID: number;\n  DrugName: string;\n  DrugUnit: string | null;\n  CategoryID: number;\n  CategoryName: string;\n  UnitPrice: number;\n  TotalQuantity: number;\n  TotalCost: number;\n  NumberOfCases: number;\n}\n\nconst DispensedDrugsReportsPage = () => {\n  const { t, i18n } = useTranslation();\n  dayjs.locale(i18n.language); // Set dayjs locale based on i18n language\n\n  useEffect(() => {\n    console.log(\"Current i18n language:\", i18n.language);\n    dayjs.locale(i18n.language);\n  }, [i18n.language]);\n  const [branches, setBranches] = useState<Branch[]>([]);\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [clinics, setClinics] = useState<Clinic[]>([]);\n  const [drugs, setDrugs] = useState<Drug[]>([]);\n  const [categories, setCategories] = useState<DrugCategory[]>([]);\n\n  const [filterBranchId, setFilterBranchId] = useState<number | ''>( '');\n  const [filterRegionId, setFilterRegionId] = useState<number | ''>( '');\n  const [filterClinicId, setFilterClinicId] = useState<number | ''>( '');\n  const [filterStartDate, setFilterStartDate] = useState<Dayjs | null>(null);\n  const [filterMonth, setFilterMonth] = useState<Dayjs | null>(null);\n  const [filterEndDate, setFilterEndDate] = useState<Dayjs | null>(null);\n  const [filterQuarter, setFilterQuarter] = useState<number | ''>( '');\n  const [filterYearForQuarter, setFilterYearForQuarter] = useState<number | ''>(new Date().getFullYear());\n\n  const [clinicAggregatedData, setClinicAggregatedData] = useState<AggregatedDispensedDrugClinic[]>([]);\n  const [regionAggregatedData, setRegionAggregatedData] = useState<AggregatedDispensedDrugRegion[]>([]);\n  const [branchAggregatedData, setBranchAggregatedData] = useState<AggregatedDispensedDrugBranch[]>([]);\n  const [reportDateRange, setReportDateRange] = useState<{ key: string; params?: any }>({ key: 'reportForTheSelectedPeriod' });\n\n  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);\n\n  useEffect(() => {\n    fetchBranches();\n    fetchRegions();\n    fetchClinics();\n    fetchDrugs();\n    fetchCategories();\n  }, []);\n\n  useEffect(() => {\n    fetchAggregatedData();\n  }, [filterBranchId, filterRegionId, filterClinicId, filterMonth, filterStartDate, filterEndDate, filterQuarter, filterYearForQuarter, i18n.language]); // Add i18n.language to dependencies\n\n  const fetchBranches = async () => {\n    try {\n      const response = await axios.get<Branch[]>('http://localhost:8000/branches/');\n      setBranches(response.data);\n    } catch (error) {\n      console.error('Error fetching branches:', error);\n      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });\n    }\n  };\n\n  const fetchRegions = async () => {\n    try {\n      const response = await axios.get<Region[]>('http://localhost:8000/regions/');\n      setRegions(response.data);\n    } catch (error) {\n      console.error('Error fetching regions:', error);\n      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });\n    }\n  };\n\n  const fetchClinics = async () => {\n    try {\n      const response = await axios.get<Clinic[]>('http://localhost:8000/clinics/');\n      setClinics(response.data);\n    } catch (error) {\n      console.error('Error fetching clinics:', error);\n      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });\n    }\n  };\n\n  const fetchDrugs = async () => {\n    try {\n      const response = await axios.get<Drug[]>('http://localhost:8000/drugs/');\n      setDrugs(response.data);\n    } catch (error) {\n      console.error('Error fetching drugs:', error);\n      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });\n    }\n  };\n\n  const fetchCategories = async () => {\n    try {\n      const response = await axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/');\n      setCategories(response.data);\n    } catch (error) {\n      console.error('Error fetching drug categories:', error);\n      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });\n    }\n  };\n\n  const fetchAggregatedData = async () => {\n    const monthParam = filterMonth ? filterMonth.format('YYYY-MM') : '';\n    const startDateParam = filterStartDate ? filterStartDate.format('YYYY-MM') : '';\n    const endDateParam = filterEndDate ? filterEndDate.format('YYYY-MM') : '';\n    const quarterParam = filterQuarter ? `${filterYearForQuarter}-Q${filterQuarter}` : '';\n\n    if (monthParam) {\n      setReportDateRange({ key: 'forTheMonthOf', params: { monthYear: dayjs(monthParam).format('MMMM YYYY') } });\n    } else if (startDateParam && endDateParam) {\n      setReportDateRange({ key: 'forThePeriod', params: { startMonthYear: dayjs(startDateParam).format('MMMM YYYY'), endMonthYear: dayjs(endDateParam).format('MMMM YYYY') } });\n    } else if (quarterParam) {\n      const [year, q] = quarterParam.split('-Q');\n      setReportDateRange({ key: 'forQuarter', params: { quarter: q, year: year } });\n    } else {\n      setReportDateRange({ key: 'reportForTheSelectedPeriod' });\n    }\n\n    const params = {\n      ...(filterBranchId && { branch_id: filterBranchId }),\n      ...(filterRegionId && { region_id: filterRegionId }),\n      ...(filterClinicId && { clinic_id: filterClinicId }),\n      ...(monthParam && { month: monthParam }),\n      ...(startDateParam && { start_month: startDateParam }),\n      ...(endDateParam && { end_month: endDateParam }),\n      ...(quarterParam && { quarter: quarterParam }),\n    };\n\n    try {\n      // Fetch by Clinic\n      const clinicResponse = await axios.get<AggregatedDispensedDrugClinic[]>(\n        'http://localhost:8000/reports/dispensed-drugs/by-clinic',\n        { params }\n      );\n      setClinicAggregatedData(clinicResponse.data);\n\n      // Fetch by Region (if not filtering by specific clinic)\n      if (!filterClinicId) {\n        const regionResponse = await axios.get<AggregatedDispensedDrugRegion[]>(\n          'http://localhost:8000/reports/dispensed-drugs/by-region',\n          { params }\n        );\n        setRegionAggregatedData(regionResponse.data);\n      } else {\n        setRegionAggregatedData([]); // Clear if clinic filter is active\n      }\n\n      // Fetch by Branch (if not filtering by specific clinic or region)\n      if (!filterClinicId && !filterRegionId) {\n        const branchResponse = await axios.get<AggregatedDispensedDrugBranch[]>(\n          'http://localhost:8000/reports/dispensed-drugs/by-branch',\n          { params }\n        );\n        setBranchAggregatedData(branchResponse.data);\n      } else {\n        setBranchAggregatedData([]); // Clear if clinic or region filter is active\n      }\n\n    } catch (error) {\n      console.error('Error fetching aggregated data:', error);\n      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });\n    }\n  };\n\n  const handleResetFilters = () => {\n    setFilterBranchId('');\n    setFilterRegionId('');\n    setFilterClinicId('');\n    setFilterMonth(null);\n    setFilterStartDate(null);\n    setFilterEndDate(null);\n    setFilterQuarter('');\n    setFilterYearForQuarter(new Date().getFullYear());\n  };\n\n  const getClinicName = (clinicId: number) => {\n    const clinic = clinics.find(c => c.id === clinicId);\n    return clinic ? clinic.name : t('unknownClinic');\n  };\n\n  const getRegionName = (regionId: number) => {\n    const region = regions.find(r => r.id === regionId);\n    return region ? region.name : t('unknownRegion');\n  };\n\n  const getBranchName = (branchId: number) => {\n    const branch = branches.find(b => b.id === branchId);\n    return branch ? branch.name : t('unknownBranch');\n  };\n\n  const getDrugName = (drugId: number, drugUnit: string | null) => {\n    const drug = drugs.find(d => d.DrugID === drugId);\n    return drug ? `${drug.DrugName} (${drugUnit || drug.Unit || 'N/A'})` : t('unknownDrug');\n  };\n\n  const getCategoryName = (categoryId: number) => {\n    const category = categories.find(c => c.CategoryID === categoryId);\n    return category ? category.CategoryName : t('unknownCategory');\n  };\n\n  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);\n  const open = Boolean(anchorEl);\n  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n\n  const reportRef = useRef<HTMLDivElement>(null);\n\n  const handlePrint = () => {\n    const printContent = reportRef.current;\n    if (printContent) {\n      const printWindow = window.open('', '', 'height=600,width=800');\n      if (printWindow) {\n        const title = document.querySelector('h4')?.innerText || 'Dispensed Drugs Report';\n        const styles = `\n          <style>\n            body { font-family: 'Amiri', Arial, sans-serif; direction: rtl; }\n            @media print { \n              body { -webkit-print-color-adjust: exact; font-family: Amiri, Arial, sans-serif !important; } \n              .no-print { display: none; } \n            }\n            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }\n            th { background-color: #f2f2f2; }\n            h4, h5, h6 { text-align: center; }\n          </style>\n          <link href=\"https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap\" rel=\"stylesheet\">\n        `;\n        printWindow.document.write(`\n          <html>\n            <head>\n              <title>${title}</title>\n              ${styles}\n            </head>\n            <body dir=\"rtl\" onload=\"window.print(); window.close();\">\n              <h4>${title}</h4>\n              ${printContent.innerHTML}\n            </body>\n          </html>\n        `);\n        printWindow.document.close();\n      }\n    }\n    handleClose();\n  };\n\n  const handleExportExcel = () => {\n    const report = reportRef.current;\n    if (report) {\n        const mainTitle = document.querySelector('h4')?.innerText || 'Dispensed Drugs Report';\n        const dateTitle = (report.querySelector('h6[gutterBottom]') as HTMLElement)?.innerText || '';\n\n        const aoa: (string | number)[][] = [];\n        aoa.push([mainTitle]);\n        if (dateTitle) {\n            aoa.push([dateTitle]);\n        }\n        aoa.push([]); // Spacer row\n\n        const elements = report.querySelectorAll('h5, h6, table');\n        let currentAoARow = aoa.length; // Tracks the current row index in the aoa array\n\n        // This array will store the starting row index and number of rows for each table in the AOA\n        const tableInfo: { startRow: number; numRows: number; numCols: number }[] = [];\n        const titleInfo: { row: number; text: string }[] = [];\n\n        elements.forEach(el => {\n            if ((el.tagName === 'H5' || el.tagName === 'H6')) {\n                if (el.getAttribute('gutterBottom') === '') {\n                    return;\n                }\n                const titleText = el.textContent || '';\n                aoa.push([titleText]);\n                titleInfo.push({ row: currentAoARow, text: titleText });\n                currentAoARow++;\n            } else if (el.tagName === 'TABLE') {\n                const table = el as HTMLTableElement;\n                const rows = table.querySelectorAll('tr');\n                const tableData: string[][] = [];\n                rows.forEach(row => {\n                    const rowData: string[] = [];\n                    const cells = row.querySelectorAll('th, td');\n                    cells.forEach(cell => {\n                        rowData.push(cell.textContent || '');\n                    });\n                    tableData.push(rowData);\n                });\n\n                tableInfo.push({\n                    startRow: currentAoARow,\n                    numRows: tableData.length,\n                    numCols: tableData[0]?.length || 0\n                });\n\n                // Add table data to AOA\n                tableData.forEach(row => {\n                    aoa.push(row);\n                });\n\n                currentAoARow += tableData.length;\n                aoa.push([]); // Spacer after table\n                currentAoARow++;\n            }\n        });\n\n        const ws = XLSX.utils.aoa_to_sheet(aoa);\n        const wb = XLSX.utils.book_new();\n\n        // Calculate maximum column width for auto-sizing\n        const maxCols = Math.max(...tableInfo.map(info => info.numCols), 1);\n        const colWidths: { wch: number }[] = [];\n\n        for (let c = 0; c < maxCols; c++) {\n            let maxWidth = 10; // Minimum width\n            for (let r = 0; r < aoa.length; r++) {\n                if (aoa[r] && aoa[r][c]) {\n                    const cellValue = String(aoa[r][c]);\n                    maxWidth = Math.max(maxWidth, cellValue.length);\n                }\n            }\n            colWidths.push({ wch: Math.min(maxWidth + 2, 50) }); // Add padding and cap at 50\n        }\n        ws['!cols'] = colWidths;\n\n        // Style main title (first row)\n        if (aoa.length > 0) {\n            const mainTitleCell = XLSX.utils.encode_cell({ r: 0, c: 0 });\n            if (!ws[mainTitleCell]) ws[mainTitleCell] = { v: aoa[0][0] };\n            ws[mainTitleCell].s = {\n                font: { bold: true, size: 16, color: { rgb: \"FF000080\" } },\n                alignment: { horizontal: \"center\", vertical: \"center\" },\n                fill: { fgColor: { rgb: \"FFE6F3FF\" } }, // Light blue background\n                border: {\n                    top: { style: \"medium\", color: { rgb: \"FF000080\" } },\n                    bottom: { style: \"medium\", color: { rgb: \"FF000080\" } },\n                    left: { style: \"medium\", color: { rgb: \"FF000080\" } },\n                    right: { style: \"medium\", color: { rgb: \"FF000080\" } }\n                }\n            };\n        }\n\n        // Style date title (second row if exists)\n        if (aoa.length > 1 && dateTitle) {\n            const dateTitleCell = XLSX.utils.encode_cell({ r: 1, c: 0 });\n            if (!ws[dateTitleCell]) ws[dateTitleCell] = { v: aoa[1][0] };\n            ws[dateTitleCell].s = {\n                font: { bold: true, size: 12, color: { rgb: \"FF000060\" } },\n                alignment: { horizontal: \"center\", vertical: \"center\" },\n                fill: { fgColor: { rgb: \"FFF0F8FF\" } }, // Very light blue background\n                border: {\n                    top: { style: \"thin\", color: { rgb: \"FF000060\" } },\n                    bottom: { style: \"thin\", color: { rgb: \"FF000060\" } },\n                    left: { style: \"thin\", color: { rgb: \"FF000060\" } },\n                    right: { style: \"thin\", color: { rgb: \"FF000060\" } }\n                }\n            };\n        }\n\n        // Style section titles (H5, H6)\n        titleInfo.forEach(title => {\n            const titleCell = XLSX.utils.encode_cell({ r: title.row, c: 0 });\n            if (!ws[titleCell]) ws[titleCell] = { v: title.text };\n            ws[titleCell].s = {\n                font: { bold: true, size: 14, color: { rgb: \"FF2E4057\" } },\n                alignment: { horizontal: \"center\", vertical: \"center\" },\n                fill: { fgColor: { rgb: \"FFDDEEFF\" } }, // Light purple background\n                border: {\n                    top: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n                    bottom: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n                    left: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n                    right: { style: \"medium\", color: { rgb: \"FF2E4057\" } }\n                }\n            };\n        });\n\n        // Apply styles to tables\n        tableInfo.forEach(info => {\n            const headerRowIndex = info.startRow;\n            const headerCellsCount = info.numCols;\n\n            // Apply styles to the table headers (first row of each table)\n            for (let i = 0; i < headerCellsCount; i++) {\n                const cellRef = XLSX.utils.encode_cell({ r: headerRowIndex, c: i });\n                if (!ws[cellRef]) {\n                    ws[cellRef] = { v: aoa[headerRowIndex][i] };\n                }\n                ws[cellRef].s = {\n                    font: { bold: true, size: 11, color: { rgb: \"FFFFFFFF\" } }, // White text\n                    alignment: { horizontal: \"center\", vertical: \"center\" },\n                    fill: { fgColor: { rgb: \"FF4472C4\" } }, // Blue background\n                    border: {\n                        top: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n                        bottom: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n                        left: { style: \"medium\", color: { rgb: \"FF2E4057\" } },\n                        right: { style: \"medium\", color: { rgb: \"FF2E4057\" } }\n                    }\n                };\n            }\n\n            // Apply styles to all data cells in the table\n            for (let r = 1; r < info.numRows; r++) { // Start from 1 to skip header\n                for (let c = 0; c < info.numCols; c++) {\n                    const cellRef = XLSX.utils.encode_cell({ r: info.startRow + r, c: c });\n                    if (!ws[cellRef]) {\n                        ws[cellRef] = { v: aoa[info.startRow + r][c] };\n                    }\n\n                    // Alternate row colors for better readability\n                    const isEvenRow = r % 2 === 0;\n                    ws[cellRef].s = {\n                        font: { size: 10 },\n                        alignment: { horizontal: \"center\", vertical: \"center\" },\n                        fill: { fgColor: { rgb: isEvenRow ? \"FFF8F9FA\" : \"FFFFFFFF\" } }, // Alternating light gray and white\n                        border: {\n                            top: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } },\n                            bottom: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } },\n                            left: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } },\n                            right: { style: \"thin\", color: { rgb: \"FFD0D0D0\" } }\n                        }\n                    };\n                }\n            }\n        });\n\n        XLSX.utils.book_append_sheet(wb, ws, 'تقرير الأدوية المنصرفة');\n        XLSX.writeFile(wb, 'تقرير_الأدوية_المنصرفة.xlsx', { cellStyles: true });\n    }\n    handleClose();\n  };\n\n  const handleExportPdf = async () => {\n    const reportContentEl = reportRef.current;\n    if (!reportContentEl) return;\n\n    const title = document.querySelector('h4')?.innerText || 'Dispensed Drugs Report';\n    \n    const styles = `\n      <style>\n        body { font-family: 'Amiri', sans-serif; direction: rtl; font-size: 14px; }\n        table { width: 100%; border-collapse: collapse; margin-top: 10px; margin-bottom: 20px; font-size: 14px; }\n        th, td { border: 1px solid #333; padding: 8px; text-align: right; }\n        th { background-color: #f2f2f2; font-weight: bold; }\n        h4, h5, h6 { text-align: center; margin: 15px 0; }\n        h4 { font-size: 24px; }\n        h5 { font-size: 20px; }\n        h6 { font-size: 18px; }\n      </style>\n      <link href=\"https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap\" rel=\"stylesheet\">\n    `;\n\n    const htmlString = `\n      <!DOCTYPE html>\n      <html lang=\"ar\">\n      <head>\n        <meta charset=\"UTF-8\">\n        <title>${title}</title>\n        ${styles}\n      </head>\n      <body>\n        <h4>${title}</h4>\n        ${reportContentEl.innerHTML}\n      </body>\n      </html>\n    `;\n\n    try {\n      const response = await axios.post('http://localhost:8000/generate-pdf-report', \n        { html_content: htmlString, title: title },\n        { responseType: 'blob' }\n      );\n\n      const url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/pdf' }));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', `${title}.pdf`);\n      document.body.appendChild(link);\n      link.click();\n      link.parentNode?.removeChild(link);\n      window.URL.revokeObjectURL(url);\n\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      setSnackbar({ open: true, message: t('pdfExportError'), severity: 'error' });\n    }\n    \n    handleClose();\n  };\n\n  return (\n    <LocalizationProvider dateAdapter={AdapterDayjs}>\n      <Box sx={{ p: 3 }}>\n        <Typography variant=\"h4\" gutterBottom>\n          {t('dispensedDrugsReports')}\n        </Typography>\n\n        <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }} className=\"no-print\">\n          <FormControl sx={{ minWidth: 180 }}>\n            <InputLabel>{t('filterByBranch')}</InputLabel>\n            <Select\n              value={filterBranchId}\n              label={t('filterByBranch')}\n              onChange={(e) => {\n                setFilterBranchId(e.target.value as number);\n                setFilterRegionId(''); // Reset region when branch changes\n                setFilterClinicId(''); // Reset clinic when branch changes\n              }}\n            >\n              <MenuItem value=\"\">{t('allBranches')}</MenuItem>\n              {branches.map((branch) => (\n                <MenuItem key={branch.id} value={branch.id}>\n                  {branch.name}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n\n          <FormControl sx={{ minWidth: 180 }}>\n            <InputLabel>{t('filterByRegion')}</InputLabel>\n            <Select\n              value={filterRegionId}\n              label={t('filterByRegion')}\n              onChange={(e) => {\n                setFilterRegionId(e.target.value as number);\n                setFilterClinicId(''); // Reset clinic when region changes\n              }}\n            >\n              <MenuItem value=\"\">{t('allRegions')}</MenuItem>\n              {regions\n                .filter(region => filterBranchId === '' || region.branch_id === filterBranchId)\n                .map((region) => (\n                  <MenuItem key={region.id} value={region.id}>\n                    {region.name}\n                  </MenuItem>\n                ))}\n            </Select>\n          </FormControl>\n\n          <FormControl sx={{ minWidth: 180 }}>\n            <InputLabel>{t('filterByClinic')}</InputLabel>\n            <Select\n              value={filterClinicId}\n              label={t('filterByClinic')}\n              onChange={(e) => setFilterClinicId(e.target.value as number)}\n            >\n              <MenuItem value=\"\">{t('allClinics')}</MenuItem>\n              {clinics\n                .filter(clinic => filterRegionId === '' || clinic.region_id === filterRegionId)\n                .map((clinic) => (\n                  <MenuItem key={clinic.id} value={clinic.id}>\n                    {clinic.name}\n                  </MenuItem>\n                ))}\n            </Select>\n          </FormControl>\n\n          <DatePicker\n            label={t('filterByMonth')}\n            views={['month', 'year']}\n            value={filterMonth}\n            onChange={(value: Dayjs | null) => setFilterMonth(value)}\n            slots={{ textField: TextField }}\n            slotProps={{\n              textField: {\n                fullWidth: true,\n                sx: { minWidth: 180 }\n              }\n            }}\n          />\n\n          <DatePicker\n            label={t('filterByStartMonth')}\n            views={['month', 'year']}\n            value={filterStartDate}\n            onChange={(value: Dayjs | null) => setFilterStartDate(value)}\n            slots={{ textField: TextField }}\n            slotProps={{\n              textField: {\n                fullWidth: true,\n                sx: { minWidth: 180 }\n              }\n            }}\n          />\n\n          <DatePicker\n            label={t('filterByEndMonth')}\n            views={['month', 'year']}\n            value={filterEndDate}\n            onChange={(value: Dayjs | null) => setFilterEndDate(value)}\n            slots={{ textField: TextField }}\n            slotProps={{\n              textField: {\n                fullWidth: true,\n                sx: { minWidth: 180 }\n              }\n            }}\n          />\n\n          <FormControl sx={{ minWidth: 180 }}>\n            <InputLabel>{t('filterByQuarter')}</InputLabel>\n            <Select\n              value={filterQuarter}\n              label={t('filterByQuarter')}\n              onChange={(e) => setFilterQuarter(e.target.value as number)}\n            >\n              <MenuItem value=\"\">{t('allQuarters')}</MenuItem>\n              <MenuItem value={1}>{t('quarter1')}</MenuItem>\n              <MenuItem value={2}>{t('quarter2')}</MenuItem>\n              <MenuItem value={3}>{t('quarter3')}</MenuItem>\n              <MenuItem value={4}>{t('quarter4')}</MenuItem>\n            </Select>\n          </FormControl>\n\n          <FormControl sx={{ minWidth: 120 }}>\n            <TextField\n              label={t('yearForQuarter')}\n              type=\"number\"\n              value={filterYearForQuarter}\n              onChange={(e) => setFilterYearForQuarter(parseInt(e.target.value, 10))}\n            />\n          </FormControl>\n\n          <Button\n            variant=\"outlined\"\n            color=\"secondary\"\n            onClick={handleResetFilters}\n          >\n            {t('resetFilters')}\n          </Button>\n\n          <Button\n            id=\"export-button\"\n            aria-controls={open ? 'export-menu' : undefined}\n            aria-haspopup=\"true\"\n            aria-expanded={open ? 'true' : undefined}\n            onClick={handleClick}\n            variant=\"contained\"\n          >\n            {t('export')}\n          </Button>\n          <Menu\n            id=\"export-menu\"\n            anchorEl={anchorEl}\n            open={open}\n            onClose={handleClose}\n            MenuListProps={{\n              'aria-labelledby': 'export-button',\n            }}\n          >\n            <MenuItem onClick={handlePrint}>{t('print')}</MenuItem>\n            <MenuItem onClick={handleExportExcel}>{t('exportToExcel')}</MenuItem>\n            <MenuItem onClick={handleExportPdf}>{t('exportToPdf')}</MenuItem>\n          </Menu>\n        </Box>\n\n        <div ref={reportRef} style={{ fontFamily: 'Amiri, Arial, sans-serif' }}>\n          <Typography variant=\"h6\" gutterBottom sx={{ mt: 2, textAlign: 'center' }}>\n            {String(t(reportDateRange.key, reportDateRange.params))}\n          </Typography>\n          {/* Display Aggregated Data */}\n          {filterClinicId ? (\n            // Display Clinic-level aggregation if a specific clinic is selected\n            <Box>\n              <Typography variant=\"h5\" gutterBottom sx={{ mt: 4 }}>\n                {t('clinicReportFor')} {getClinicName(filterClinicId as number)}\n              </Typography>\n              <TableContainer component={Paper}>\n                <Table>\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>{t('drug')}</TableCell>\n                      <TableCell>{t('category')}</TableCell>\n                      <TableCell>{t('unitPrice')}</TableCell>\n                      <TableCell>{t('totalQuantity')}</TableCell>\n                      <TableCell>{t('numberOfCases')}</TableCell>\n                      <TableCell>{t('totalCost')}</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {clinicAggregatedData.map((data, index) => (\n                      <TableRow key={index}>\n                        <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>\n                        <TableCell>{getCategoryName(data.CategoryID)}</TableCell>\n                        <TableCell>{data.UnitPrice}</TableCell>\n                        <TableCell>{data.TotalQuantity}</TableCell>\n                        <TableCell>{data.NumberOfCases}</TableCell>\n                        <TableCell>{data.TotalCost}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            </Box>\n          ) : filterRegionId ? (\n            // Display Region-level aggregation if a specific region is selected (and no clinic)\n            <Box>\n              <Typography variant=\"h5\" gutterBottom sx={{ mt: 4 }}>\n                {t('regionReportFor')} {getRegionName(filterRegionId as number)}\n              </Typography>\n              {/* Aggregate by Clinic within the selected Region */}\n              {regions\n                .filter(r => r.id === filterRegionId)\n                .map(selectedRegion => (\n                  <Box key={selectedRegion.id} sx={{ mb: 4 }}>\n                    {clinics\n                      .filter(clinic => clinic.region_id === selectedRegion.id)\n                      .map(clinic => {\n                        const clinicData = clinicAggregatedData.filter(d => d.ClinicID === clinic.id);\n                        if (clinicData.length === 0) return null;\n                        return (\n                          <Box key={clinic.id} sx={{ mb: 2 }}>\n                            <Typography variant=\"h6\" sx={{ mt: 2 }}>\n                              {t('clinic')}: {clinic.name}\n                            </Typography>\n                            <TableContainer component={Paper}>\n                              <Table size=\"small\">\n                                <TableHead>\n                                  <TableRow>\n                                    <TableCell>{t('drug')}</TableCell>\n                                    <TableCell>{t('category')}</TableCell>\n                                    <TableCell>{t('unitPrice')}</TableCell>\n                                    <TableCell>{t('totalQuantity')}</TableCell>\n                                    <TableCell>{t('numberOfCases')}</TableCell>\n                                    <TableCell>{t('totalCost')}</TableCell>\n                                  </TableRow>\n                                </TableHead>\n                                <TableBody>\n                                  {clinicData.map((data, index) => (\n                                    <TableRow key={index}>\n                                      <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>\n                                      <TableCell>{getCategoryName(data.CategoryID)}</TableCell>\n                                      <TableCell>{data.UnitPrice}</TableCell>\n                                      <TableCell>{data.TotalQuantity}</TableCell>\n                                      <TableCell>{data.NumberOfCases}</TableCell>\n                                      <TableCell>{data.TotalCost}</TableCell>\n                                    </TableRow>\n                                  ))}\n                                </TableBody>\n                              </Table>\n                            </TableContainer>\n                          </Box>\n                        );\n                      })}\n                    <Typography variant=\"h6\" sx={{ mt: 4 }}>\n                      {t('totalForRegion')}: {selectedRegion.name}\n                    </Typography>\n                    <TableContainer component={Paper}>\n                      <Table>\n                        <TableHead>\n                          <TableRow>\n                            <TableCell>{t('drug')}</TableCell>\n                            <TableCell>{t('category')}</TableCell>\n                            <TableCell>{t('unitPrice')}</TableCell>\n                            <TableCell>{t('totalQuantity')}</TableCell>\n                            <TableCell>{t('numberOfCases')}</TableCell>\n                            <TableCell>{t('totalCost')}</TableCell>\n                          </TableRow>\n                        </TableHead>\n                        <TableBody>\n                          {regionAggregatedData.map((data, index) => (\n                            <TableRow key={index}>\n                              <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>\n                              <TableCell>{getCategoryName(data.CategoryID)}</TableCell>\n                              <TableCell>{data.UnitPrice}</TableCell>\n                              <TableCell>{data.TotalQuantity}</TableCell>\n                              <TableCell>{data.NumberOfCases}</TableCell>\n                              <TableCell>{data.TotalCost}</TableCell>\n                            </TableRow>\n                          ))}\n                        </TableBody>\n                      </Table>\n                    </TableContainer>\n                  </Box>\n                ))}\n            </Box>\n          ) : filterBranchId ? (\n            // Display Branch-level aggregation if a specific branch is selected (and no clinic/region)\n            <Box>\n              <Typography variant=\"h5\" gutterBottom sx={{ mt: 4 }}>\n                {t('branchReportFor')} {getBranchName(filterBranchId as number)}\n              </Typography>\n              {/* Aggregate by Region within the selected Branch */}\n              {branches\n                .filter(b => b.id === filterBranchId)\n                .map(selectedBranch => (\n                  <Box key={selectedBranch.id} sx={{ mb: 4 }}>\n                    {regions\n                      .filter(region => region.branch_id === selectedBranch.id)\n                      .map(region => {\n                        const regionData = regionAggregatedData.filter(d => d.RegionID === region.id);\n                        if (regionData.length === 0) return null;\n                        return (\n                          <Box key={region.id} sx={{ mb: 2 }}>\n                            <Typography variant=\"h6\" sx={{ mt: 2 }}>\n                              {t('region')}: {region.name}\n                            </Typography>\n                            <TableContainer component={Paper}>\n                              <Table size=\"small\">\n                                <TableHead>\n                                  <TableRow>\n                                    <TableCell>{t('drug')}</TableCell>\n                                    <TableCell>{t('category')}</TableCell>\n                                    <TableCell>{t('unitPrice')}</TableCell>\n                                    <TableCell>{t('totalQuantity')}</TableCell>\n                                    <TableCell>{t('numberOfCases')}</TableCell>\n                                    <TableCell>{t('totalCost')}</TableCell>\n                                  </TableRow>\n                                </TableHead>\n                                <TableBody>\n                                  {regionData.map((data, index) => (\n                                    <TableRow key={index}>\n                                      <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>\n                                      <TableCell>{getCategoryName(data.CategoryID)}</TableCell>\n                                      <TableCell>{data.UnitPrice}</TableCell>\n                                      <TableCell>{data.TotalQuantity}</TableCell>\n                                      <TableCell>{data.NumberOfCases}</TableCell>\n                                      <TableCell>{data.TotalCost}</TableCell>\n                                    </TableRow>\n                                  ))}\n                                </TableBody>\n                              </Table>\n                            </TableContainer>\n                          </Box>\n                        );\n                      })}\n                    <Typography variant=\"h6\" sx={{ mt: 4 }}>\n                      {t('totalForBranch')}: {selectedBranch.name}\n                    </Typography>\n                    <TableContainer component={Paper}>\n                      <Table>\n                        <TableHead>\n                          <TableRow>\n                            <TableCell>{t('drug')}</TableCell>\n                            <TableCell>{t('category')}</TableCell>\n                            <TableCell>{t('unitPrice')}</TableCell>\n                            <TableCell>{t('totalQuantity')}</TableCell>\n                            <TableCell>{t('numberOfCases')}</TableCell>\n                            <TableCell>{t('totalCost')}</TableCell>\n                          </TableRow>\n                        </TableHead>\n                        <TableBody>\n                          {branchAggregatedData.map((data, index) => (\n                            <TableRow key={index}>\n                              <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>\n                              <TableCell>{getCategoryName(data.CategoryID)}</TableCell>\n                              <TableCell>{data.UnitPrice}</TableCell>\n                              <TableCell>{data.TotalQuantity}</TableCell>\n                              <TableCell>{data.NumberOfCases}</TableCell>\n                              <TableCell>{data.TotalCost}</TableCell>\n                            </TableRow>\n                          ))}\n                        </TableBody>\n                      </Table>\n                    </TableContainer>\n                  </Box>\n                ))}\n            </Box>\n          ) : (\n            // Default view or message if no filters are applied\n            <Typography variant=\"body1\" sx={{ mt: 4 }}>\n              {t('selectFiltersForReport')}\n            </Typography>\n          )}\n        </div>\n\n        {snackbar && (\n          <Snackbar\n            open={snackbar.open}\n            autoHideDuration={6000}\n            onClose={() => setSnackbar(null)}\n            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n          >\n            <Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>\n              {snackbar.message}\n            </Alert>\n          </Snackbar>\n        )}\n      </Box>\n    </LocalizationProvider>\n  );\n};\n\n\nexport default DispensedDrugsReportsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,IAAI,QACC,eAAe;AACtB,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,OAAOC,KAAK,MAAiB,OAAO;AACpC,OAAO,iBAAiB,CAAC,CAAC;AAC1B,OAAO,iBAAiB,CAAC,CAAC;AACE;;AAG5B,OAAO,KAAKC,IAAI,MAAM,MAAM;;AAE5B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAwEA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAG9B,cAAc,CAAC,CAAC;EACpCuB,KAAK,CAACQ,MAAM,CAACD,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC;;EAE7BlC,SAAS,CAAC,MAAM;IACdmC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEJ,IAAI,CAACE,QAAQ,CAAC;IACpDT,KAAK,CAACQ,MAAM,CAACD,IAAI,CAACE,QAAQ,CAAC;EAC7B,CAAC,EAAE,CAACF,IAAI,CAACE,QAAQ,CAAC,CAAC;EACnB,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAW,EAAE,CAAC;EACtD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC8C,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAiB,EAAE,CAAC;EAEhE,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAe,EAAE,CAAC;EACtE,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAe,EAAE,CAAC;EACtE,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAe,EAAE,CAAC;EACtE,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAe,IAAI,CAAC;EAC1E,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAe,IAAI,CAAC;EAClE,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAe,IAAI,CAAC;EACtE,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAe,EAAE,CAAC;EACpE,MAAM,CAAC8D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/D,QAAQ,CAAc,IAAIgE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAEvG,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnE,QAAQ,CAAkC,EAAE,CAAC;EACrG,MAAM,CAACoE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrE,QAAQ,CAAkC,EAAE,CAAC;EACrG,MAAM,CAACsE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvE,QAAQ,CAAkC,EAAE,CAAC;EACrG,MAAM,CAACwE,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAgC;IAAE0E,GAAG,EAAE;EAA6B,CAAC,CAAC;EAE5H,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAA2E,IAAI,CAAC;EAExHC,SAAS,CAAC,MAAM;IACd4E,aAAa,CAAC,CAAC;IACfC,YAAY,CAAC,CAAC;IACdC,YAAY,CAAC,CAAC;IACdC,UAAU,CAAC,CAAC;IACZC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAENhF,SAAS,CAAC,MAAM;IACdiF,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAClC,cAAc,EAAEE,cAAc,EAAEE,cAAc,EAAEI,WAAW,EAAEF,eAAe,EAAEI,aAAa,EAAEE,aAAa,EAAEE,oBAAoB,EAAE7B,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEvJ,MAAM0C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,GAAG,CAAW,iCAAiC,CAAC;MAC7E7C,WAAW,CAAC4C,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlD,OAAO,CAACkD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDV,WAAW,CAAC;QAAEW,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExD,CAAC,CAAC,gBAAgB,CAAC;QAAEyD,QAAQ,EAAE;MAAQ,CAAC,CAAC;IAC9E;EACF,CAAC;EAED,MAAMX,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,GAAG,CAAW,gCAAgC,CAAC;MAC5E3C,UAAU,CAAC0C,QAAQ,CAACE,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlD,OAAO,CAACkD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CV,WAAW,CAAC;QAAEW,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExD,CAAC,CAAC,gBAAgB,CAAC;QAAEyD,QAAQ,EAAE;MAAQ,CAAC,CAAC;IAC9E;EACF,CAAC;EAED,MAAMV,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,GAAG,CAAW,gCAAgC,CAAC;MAC5EzC,UAAU,CAACwC,QAAQ,CAACE,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlD,OAAO,CAACkD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CV,WAAW,CAAC;QAAEW,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExD,CAAC,CAAC,gBAAgB,CAAC;QAAEyD,QAAQ,EAAE;MAAQ,CAAC,CAAC;IAC9E;EACF,CAAC;EAED,MAAMT,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,GAAG,CAAS,8BAA8B,CAAC;MACxEvC,QAAQ,CAACsC,QAAQ,CAACE,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlD,OAAO,CAACkD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CV,WAAW,CAAC;QAAEW,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExD,CAAC,CAAC,gBAAgB,CAAC;QAAEyD,QAAQ,EAAE;MAAQ,CAAC,CAAC;IAC9E;EACF,CAAC;EAED,MAAMR,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,GAAG,CAAiB,wCAAwC,CAAC;MAC1FrC,aAAa,CAACoC,QAAQ,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlD,OAAO,CAACkD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDV,WAAW,CAAC;QAAEW,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExD,CAAC,CAAC,gBAAgB,CAAC;QAAEyD,QAAQ,EAAE;MAAQ,CAAC,CAAC;IAC9E;EACF,CAAC;EAED,MAAMP,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,MAAMQ,UAAU,GAAGlC,WAAW,GAAGA,WAAW,CAACmC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE;IACnE,MAAMC,cAAc,GAAGtC,eAAe,GAAGA,eAAe,CAACqC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE;IAC/E,MAAME,YAAY,GAAGnC,aAAa,GAAGA,aAAa,CAACiC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE;IACzE,MAAMG,YAAY,GAAGlC,aAAa,GAAG,GAAGE,oBAAoB,KAAKF,aAAa,EAAE,GAAG,EAAE;IAErF,IAAI8B,UAAU,EAAE;MACdjB,kBAAkB,CAAC;QAAEC,GAAG,EAAE,eAAe;QAAEqB,MAAM,EAAE;UAAEC,SAAS,EAAEtE,KAAK,CAACgE,UAAU,CAAC,CAACC,MAAM,CAAC,WAAW;QAAE;MAAE,CAAC,CAAC;IAC5G,CAAC,MAAM,IAAIC,cAAc,IAAIC,YAAY,EAAE;MACzCpB,kBAAkB,CAAC;QAAEC,GAAG,EAAE,cAAc;QAAEqB,MAAM,EAAE;UAAEE,cAAc,EAAEvE,KAAK,CAACkE,cAAc,CAAC,CAACD,MAAM,CAAC,WAAW,CAAC;UAAEO,YAAY,EAAExE,KAAK,CAACmE,YAAY,CAAC,CAACF,MAAM,CAAC,WAAW;QAAE;MAAE,CAAC,CAAC;IAC3K,CAAC,MAAM,IAAIG,YAAY,EAAE;MACvB,MAAM,CAACK,IAAI,EAAEC,CAAC,CAAC,GAAGN,YAAY,CAACO,KAAK,CAAC,IAAI,CAAC;MAC1C5B,kBAAkB,CAAC;QAAEC,GAAG,EAAE,YAAY;QAAEqB,MAAM,EAAE;UAAEO,OAAO,EAAEF,CAAC;UAAED,IAAI,EAAEA;QAAK;MAAE,CAAC,CAAC;IAC/E,CAAC,MAAM;MACL1B,kBAAkB,CAAC;QAAEC,GAAG,EAAE;MAA6B,CAAC,CAAC;IAC3D;IAEA,MAAMqB,MAAM,GAAG;MACb,IAAI/C,cAAc,IAAI;QAAEuD,SAAS,EAAEvD;MAAe,CAAC,CAAC;MACpD,IAAIE,cAAc,IAAI;QAAEsD,SAAS,EAAEtD;MAAe,CAAC,CAAC;MACpD,IAAIE,cAAc,IAAI;QAAEqD,SAAS,EAAErD;MAAe,CAAC,CAAC;MACpD,IAAIsC,UAAU,IAAI;QAAEgB,KAAK,EAAEhB;MAAW,CAAC,CAAC;MACxC,IAAIE,cAAc,IAAI;QAAEe,WAAW,EAAEf;MAAe,CAAC,CAAC;MACtD,IAAIC,YAAY,IAAI;QAAEe,SAAS,EAAEf;MAAa,CAAC,CAAC;MAChD,IAAIC,YAAY,IAAI;QAAEQ,OAAO,EAAER;MAAa,CAAC;IAC/C,CAAC;IAED,IAAI;MACF;MACA,MAAMe,cAAc,GAAG,MAAMzG,KAAK,CAACgF,GAAG,CACpC,yDAAyD,EACzD;QAAEW;MAAO,CACX,CAAC;MACD5B,uBAAuB,CAAC0C,cAAc,CAACxB,IAAI,CAAC;;MAE5C;MACA,IAAI,CAACjC,cAAc,EAAE;QACnB,MAAM0D,cAAc,GAAG,MAAM1G,KAAK,CAACgF,GAAG,CACpC,yDAAyD,EACzD;UAAEW;QAAO,CACX,CAAC;QACD1B,uBAAuB,CAACyC,cAAc,CAACzB,IAAI,CAAC;MAC9C,CAAC,MAAM;QACLhB,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC;MAC/B;;MAEA;MACA,IAAI,CAACjB,cAAc,IAAI,CAACF,cAAc,EAAE;QACtC,MAAM6D,cAAc,GAAG,MAAM3G,KAAK,CAACgF,GAAG,CACpC,yDAAyD,EACzD;UAAEW;QAAO,CACX,CAAC;QACDxB,uBAAuB,CAACwC,cAAc,CAAC1B,IAAI,CAAC;MAC9C,CAAC,MAAM;QACLd,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC;MAC/B;IAEF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdlD,OAAO,CAACkD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDV,WAAW,CAAC;QAAEW,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExD,CAAC,CAAC,gBAAgB,CAAC;QAAEyD,QAAQ,EAAE;MAAQ,CAAC,CAAC;IAC9E;EACF,CAAC;EAED,MAAMuB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B/D,iBAAiB,CAAC,EAAE,CAAC;IACrBE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,iBAAiB,CAAC,EAAE,CAAC;IACrBI,cAAc,CAAC,IAAI,CAAC;IACpBF,kBAAkB,CAAC,IAAI,CAAC;IACxBI,gBAAgB,CAAC,IAAI,CAAC;IACtBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,uBAAuB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMgD,aAAa,GAAIC,QAAgB,IAAK;IAC1C,MAAMC,MAAM,GAAGzE,OAAO,CAAC0E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,QAAQ,CAAC;IACnD,OAAOC,MAAM,GAAGA,MAAM,CAACI,IAAI,GAAGvF,CAAC,CAAC,eAAe,CAAC;EAClD,CAAC;EAED,MAAMwF,aAAa,GAAIC,QAAgB,IAAK;IAC1C,MAAMC,MAAM,GAAGlF,OAAO,CAAC4E,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKG,QAAQ,CAAC;IACnD,OAAOC,MAAM,GAAGA,MAAM,CAACH,IAAI,GAAGvF,CAAC,CAAC,eAAe,CAAC;EAClD,CAAC;EAED,MAAM4F,aAAa,GAAIC,QAAgB,IAAK;IAC1C,MAAMC,MAAM,GAAGxF,QAAQ,CAAC8E,IAAI,CAACW,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAKO,QAAQ,CAAC;IACpD,OAAOC,MAAM,GAAGA,MAAM,CAACP,IAAI,GAAGvF,CAAC,CAAC,eAAe,CAAC;EAClD,CAAC;EAED,MAAMgG,WAAW,GAAGA,CAACC,MAAc,EAAEC,QAAuB,KAAK;IAC/D,MAAMC,IAAI,GAAGvF,KAAK,CAACwE,IAAI,CAACgB,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAKJ,MAAM,CAAC;IACjD,OAAOE,IAAI,GAAG,GAAGA,IAAI,CAACG,QAAQ,KAAKJ,QAAQ,IAAIC,IAAI,CAACI,IAAI,IAAI,KAAK,GAAG,GAAGvG,CAAC,CAAC,aAAa,CAAC;EACzF,CAAC;EAED,MAAMwG,eAAe,GAAIC,UAAkB,IAAK;IAC9C,MAAMC,QAAQ,GAAG5F,UAAU,CAACsE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACsB,UAAU,KAAKF,UAAU,CAAC;IAClE,OAAOC,QAAQ,GAAGA,QAAQ,CAACE,YAAY,GAAG5G,CAAC,CAAC,iBAAiB,CAAC;EAChE,CAAC;EAED,MAAM,CAAC6G,QAAQ,EAAEC,WAAW,CAAC,GAAG/I,KAAK,CAACC,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAMuF,IAAI,GAAGwD,OAAO,CAACF,QAAQ,CAAC;EAC9B,MAAMG,WAAW,GAAIC,KAA0C,IAAK;IAClEH,WAAW,CAACG,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EACD,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBL,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMM,SAAS,GAAGlJ,MAAM,CAAiB,IAAI,CAAC;EAE9C,MAAMmJ,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,YAAY,GAAGF,SAAS,CAACG,OAAO;IACtC,IAAID,YAAY,EAAE;MAChB,MAAME,WAAW,GAAGC,MAAM,CAAClE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,sBAAsB,CAAC;MAC/D,IAAIiE,WAAW,EAAE;QAAA,IAAAE,qBAAA;QACf,MAAMC,KAAK,GAAG,EAAAD,qBAAA,GAAAE,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,cAAAH,qBAAA,uBAA5BA,qBAAA,CAA8BI,SAAS,KAAI,wBAAwB;QACjF,MAAMC,MAAM,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;QACDP,WAAW,CAACI,QAAQ,CAACI,KAAK,CAAC;AACnC;AACA;AACA,uBAAuBL,KAAK;AAC5B,gBAAgBI,MAAM;AACtB;AACA;AACA,oBAAoBJ,KAAK;AACzB,gBAAgBL,YAAY,CAACW,SAAS;AACtC;AACA;AACA,SAAS,CAAC;QACFT,WAAW,CAACI,QAAQ,CAACM,KAAK,CAAC,CAAC;MAC9B;IACF;IACAf,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMgB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,MAAM,GAAGhB,SAAS,CAACG,OAAO;IAChC,IAAIa,MAAM,EAAE;MAAA,IAAAC,sBAAA,EAAAC,qBAAA;MACR,MAAMC,SAAS,GAAG,EAAAF,sBAAA,GAAAT,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,cAAAQ,sBAAA,uBAA5BA,sBAAA,CAA8BP,SAAS,KAAI,wBAAwB;MACrF,MAAMU,SAAS,GAAG,EAAAF,qBAAA,GAACF,MAAM,CAACP,aAAa,CAAC,kBAAkB,CAAC,cAAAS,qBAAA,uBAAzCA,qBAAA,CAA2DR,SAAS,KAAI,EAAE;MAE5F,MAAMW,GAA0B,GAAG,EAAE;MACrCA,GAAG,CAACC,IAAI,CAAC,CAACH,SAAS,CAAC,CAAC;MACrB,IAAIC,SAAS,EAAE;QACXC,GAAG,CAACC,IAAI,CAAC,CAACF,SAAS,CAAC,CAAC;MACzB;MACAC,GAAG,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEd,MAAMC,QAAQ,GAAGP,MAAM,CAACQ,gBAAgB,CAAC,eAAe,CAAC;MACzD,IAAIC,aAAa,GAAGJ,GAAG,CAACK,MAAM,CAAC,CAAC;;MAEhC;MACA,MAAMC,SAAmE,GAAG,EAAE;MAC9E,MAAMC,SAA0C,GAAG,EAAE;MAErDL,QAAQ,CAACM,OAAO,CAACC,EAAE,IAAI;QACnB,IAAKA,EAAE,CAACC,OAAO,KAAK,IAAI,IAAID,EAAE,CAACC,OAAO,KAAK,IAAI,EAAG;UAC9C,IAAID,EAAE,CAACE,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,EAAE;YACxC;UACJ;UACA,MAAMC,SAAS,GAAGH,EAAE,CAACI,WAAW,IAAI,EAAE;UACtCb,GAAG,CAACC,IAAI,CAAC,CAACW,SAAS,CAAC,CAAC;UACrBL,SAAS,CAACN,IAAI,CAAC;YAAEa,GAAG,EAAEV,aAAa;YAAEW,IAAI,EAAEH;UAAU,CAAC,CAAC;UACvDR,aAAa,EAAE;QACnB,CAAC,MAAM,IAAIK,EAAE,CAACC,OAAO,KAAK,OAAO,EAAE;UAAA,IAAAM,WAAA;UAC/B,MAAMC,KAAK,GAAGR,EAAsB;UACpC,MAAMS,IAAI,GAAGD,KAAK,CAACd,gBAAgB,CAAC,IAAI,CAAC;UACzC,MAAMgB,SAAqB,GAAG,EAAE;UAChCD,IAAI,CAACV,OAAO,CAACM,GAAG,IAAI;YAChB,MAAMM,OAAiB,GAAG,EAAE;YAC5B,MAAMC,KAAK,GAAGP,GAAG,CAACX,gBAAgB,CAAC,QAAQ,CAAC;YAC5CkB,KAAK,CAACb,OAAO,CAACc,IAAI,IAAI;cAClBF,OAAO,CAACnB,IAAI,CAACqB,IAAI,CAACT,WAAW,IAAI,EAAE,CAAC;YACxC,CAAC,CAAC;YACFM,SAAS,CAAClB,IAAI,CAACmB,OAAO,CAAC;UAC3B,CAAC,CAAC;UAEFd,SAAS,CAACL,IAAI,CAAC;YACXsB,QAAQ,EAAEnB,aAAa;YACvBoB,OAAO,EAAEL,SAAS,CAACd,MAAM;YACzBoB,OAAO,EAAE,EAAAT,WAAA,GAAAG,SAAS,CAAC,CAAC,CAAC,cAAAH,WAAA,uBAAZA,WAAA,CAAcX,MAAM,KAAI;UACrC,CAAC,CAAC;;UAEF;UACAc,SAAS,CAACX,OAAO,CAACM,GAAG,IAAI;YACrBd,GAAG,CAACC,IAAI,CAACa,GAAG,CAAC;UACjB,CAAC,CAAC;UAEFV,aAAa,IAAIe,SAAS,CAACd,MAAM;UACjCL,GAAG,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;UACdG,aAAa,EAAE;QACnB;MACJ,CAAC,CAAC;MAEF,MAAMsB,EAAE,GAAGxK,IAAI,CAACyK,KAAK,CAACC,YAAY,CAAC5B,GAAG,CAAC;MACvC,MAAM6B,EAAE,GAAG3K,IAAI,CAACyK,KAAK,CAACG,QAAQ,CAAC,CAAC;;MAEhC;MACA,MAAMC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG3B,SAAS,CAAC4B,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACV,OAAO,CAAC,EAAE,CAAC,CAAC;MACnE,MAAMW,SAA4B,GAAG,EAAE;MAEvC,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,OAAO,EAAEnF,CAAC,EAAE,EAAE;QAC9B,IAAIyF,QAAQ,GAAG,EAAE,CAAC,CAAC;QACnB,KAAK,IAAInF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,GAAG,CAACK,MAAM,EAAEnD,CAAC,EAAE,EAAE;UACjC,IAAI8C,GAAG,CAAC9C,CAAC,CAAC,IAAI8C,GAAG,CAAC9C,CAAC,CAAC,CAACN,CAAC,CAAC,EAAE;YACrB,MAAM0F,SAAS,GAAGC,MAAM,CAACvC,GAAG,CAAC9C,CAAC,CAAC,CAACN,CAAC,CAAC,CAAC;YACnCyF,QAAQ,GAAGL,IAAI,CAACC,GAAG,CAACI,QAAQ,EAAEC,SAAS,CAACjC,MAAM,CAAC;UACnD;QACJ;QACA+B,SAAS,CAACnC,IAAI,CAAC;UAAEuC,GAAG,EAAER,IAAI,CAACS,GAAG,CAACJ,QAAQ,GAAG,CAAC,EAAE,EAAE;QAAE,CAAC,CAAC,CAAC,CAAC;MACzD;MACAX,EAAE,CAAC,OAAO,CAAC,GAAGU,SAAS;;MAEvB;MACA,IAAIpC,GAAG,CAACK,MAAM,GAAG,CAAC,EAAE;QAChB,MAAMqC,aAAa,GAAGxL,IAAI,CAACyK,KAAK,CAACgB,WAAW,CAAC;UAAEzF,CAAC,EAAE,CAAC;UAAEN,CAAC,EAAE;QAAE,CAAC,CAAC;QAC5D,IAAI,CAAC8E,EAAE,CAACgB,aAAa,CAAC,EAAEhB,EAAE,CAACgB,aAAa,CAAC,GAAG;UAAEE,CAAC,EAAE5C,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAAE,CAAC;QAC5D0B,EAAE,CAACgB,aAAa,CAAC,CAACG,CAAC,GAAG;UAClBC,IAAI,EAAE;YAAEC,IAAI,EAAE,IAAI;YAAEC,IAAI,EAAE,EAAE;YAAEC,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE,CAAC;UAC1DC,SAAS,EAAE;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAC;UACvDC,IAAI,EAAE;YAAEC,OAAO,EAAE;cAAEL,GAAG,EAAE;YAAW;UAAE,CAAC;UAAE;UACxCM,MAAM,EAAE;YACJC,GAAG,EAAE;cAAEC,KAAK,EAAE,QAAQ;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACpDS,MAAM,EAAE;cAAED,KAAK,EAAE,QAAQ;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACvDU,IAAI,EAAE;cAAEF,KAAK,EAAE,QAAQ;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACrDW,KAAK,EAAE;cAAEH,KAAK,EAAE,QAAQ;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE;UACzD;QACJ,CAAC;MACL;;MAEA;MACA,IAAIlD,GAAG,CAACK,MAAM,GAAG,CAAC,IAAIN,SAAS,EAAE;QAC7B,MAAM+D,aAAa,GAAG5M,IAAI,CAACyK,KAAK,CAACgB,WAAW,CAAC;UAAEzF,CAAC,EAAE,CAAC;UAAEN,CAAC,EAAE;QAAE,CAAC,CAAC;QAC5D,IAAI,CAAC8E,EAAE,CAACoC,aAAa,CAAC,EAAEpC,EAAE,CAACoC,aAAa,CAAC,GAAG;UAAElB,CAAC,EAAE5C,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAAE,CAAC;QAC5D0B,EAAE,CAACoC,aAAa,CAAC,CAACjB,CAAC,GAAG;UAClBC,IAAI,EAAE;YAAEC,IAAI,EAAE,IAAI;YAAEC,IAAI,EAAE,EAAE;YAAEC,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE,CAAC;UAC1DC,SAAS,EAAE;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAC;UACvDC,IAAI,EAAE;YAAEC,OAAO,EAAE;cAAEL,GAAG,EAAE;YAAW;UAAE,CAAC;UAAE;UACxCM,MAAM,EAAE;YACJC,GAAG,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YAClDS,MAAM,EAAE;cAAED,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACrDU,IAAI,EAAE;cAAEF,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACnDW,KAAK,EAAE;cAAEH,KAAK,EAAE,MAAM;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE;UACvD;QACJ,CAAC;MACL;;MAEA;MACA3C,SAAS,CAACC,OAAO,CAACtB,KAAK,IAAI;QACvB,MAAM6E,SAAS,GAAG7M,IAAI,CAACyK,KAAK,CAACgB,WAAW,CAAC;UAAEzF,CAAC,EAAEgC,KAAK,CAAC4B,GAAG;UAAElE,CAAC,EAAE;QAAE,CAAC,CAAC;QAChE,IAAI,CAAC8E,EAAE,CAACqC,SAAS,CAAC,EAAErC,EAAE,CAACqC,SAAS,CAAC,GAAG;UAAEnB,CAAC,EAAE1D,KAAK,CAAC6B;QAAK,CAAC;QACrDW,EAAE,CAACqC,SAAS,CAAC,CAAClB,CAAC,GAAG;UACdC,IAAI,EAAE;YAAEC,IAAI,EAAE,IAAI;YAAEC,IAAI,EAAE,EAAE;YAAEC,KAAK,EAAE;cAAEC,GAAG,EAAE;YAAW;UAAE,CAAC;UAC1DC,SAAS,EAAE;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAC;UACvDC,IAAI,EAAE;YAAEC,OAAO,EAAE;cAAEL,GAAG,EAAE;YAAW;UAAE,CAAC;UAAE;UACxCM,MAAM,EAAE;YACJC,GAAG,EAAE;cAAEC,KAAK,EAAE,QAAQ;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACpDS,MAAM,EAAE;cAAED,KAAK,EAAE,QAAQ;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACvDU,IAAI,EAAE;cAAEF,KAAK,EAAE,QAAQ;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YACrDW,KAAK,EAAE;cAAEH,KAAK,EAAE,QAAQ;cAAET,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE;UACzD;QACJ,CAAC;MACL,CAAC,CAAC;;MAEF;MACA5C,SAAS,CAACE,OAAO,CAAC2B,IAAI,IAAI;QACtB,MAAM6B,cAAc,GAAG7B,IAAI,CAACZ,QAAQ;QACpC,MAAM0C,gBAAgB,GAAG9B,IAAI,CAACV,OAAO;;QAErC;QACA,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,gBAAgB,EAAEC,CAAC,EAAE,EAAE;UACvC,MAAMC,OAAO,GAAGjN,IAAI,CAACyK,KAAK,CAACgB,WAAW,CAAC;YAAEzF,CAAC,EAAE8G,cAAc;YAAEpH,CAAC,EAAEsH;UAAE,CAAC,CAAC;UACnE,IAAI,CAACxC,EAAE,CAACyC,OAAO,CAAC,EAAE;YACdzC,EAAE,CAACyC,OAAO,CAAC,GAAG;cAAEvB,CAAC,EAAE5C,GAAG,CAACgE,cAAc,CAAC,CAACE,CAAC;YAAE,CAAC;UAC/C;UACAxC,EAAE,CAACyC,OAAO,CAAC,CAACtB,CAAC,GAAG;YACZC,IAAI,EAAE;cAAEC,IAAI,EAAE,IAAI;cAAEC,IAAI,EAAE,EAAE;cAAEC,KAAK,EAAE;gBAAEC,GAAG,EAAE;cAAW;YAAE,CAAC;YAAE;YAC5DC,SAAS,EAAE;cAAEC,UAAU,EAAE,QAAQ;cAAEC,QAAQ,EAAE;YAAS,CAAC;YACvDC,IAAI,EAAE;cAAEC,OAAO,EAAE;gBAAEL,GAAG,EAAE;cAAW;YAAE,CAAC;YAAE;YACxCM,MAAM,EAAE;cACJC,GAAG,EAAE;gBAAEC,KAAK,EAAE,QAAQ;gBAAET,KAAK,EAAE;kBAAEC,GAAG,EAAE;gBAAW;cAAE,CAAC;cACpDS,MAAM,EAAE;gBAAED,KAAK,EAAE,QAAQ;gBAAET,KAAK,EAAE;kBAAEC,GAAG,EAAE;gBAAW;cAAE,CAAC;cACvDU,IAAI,EAAE;gBAAEF,KAAK,EAAE,QAAQ;gBAAET,KAAK,EAAE;kBAAEC,GAAG,EAAE;gBAAW;cAAE,CAAC;cACrDW,KAAK,EAAE;gBAAEH,KAAK,EAAE,QAAQ;gBAAET,KAAK,EAAE;kBAAEC,GAAG,EAAE;gBAAW;cAAE;YACzD;UACJ,CAAC;QACL;;QAEA;QACA,KAAK,IAAIhG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,IAAI,CAACX,OAAO,EAAEtE,CAAC,EAAE,EAAE;UAAE;UACrC,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuF,IAAI,CAACV,OAAO,EAAE7E,CAAC,EAAE,EAAE;YACnC,MAAMuH,OAAO,GAAGjN,IAAI,CAACyK,KAAK,CAACgB,WAAW,CAAC;cAAEzF,CAAC,EAAEiF,IAAI,CAACZ,QAAQ,GAAGrE,CAAC;cAAEN,CAAC,EAAEA;YAAE,CAAC,CAAC;YACtE,IAAI,CAAC8E,EAAE,CAACyC,OAAO,CAAC,EAAE;cACdzC,EAAE,CAACyC,OAAO,CAAC,GAAG;gBAAEvB,CAAC,EAAE5C,GAAG,CAACmC,IAAI,CAACZ,QAAQ,GAAGrE,CAAC,CAAC,CAACN,CAAC;cAAE,CAAC;YAClD;;YAEA;YACA,MAAMwH,SAAS,GAAGlH,CAAC,GAAG,CAAC,KAAK,CAAC;YAC7BwE,EAAE,CAACyC,OAAO,CAAC,CAACtB,CAAC,GAAG;cACZC,IAAI,EAAE;gBAAEE,IAAI,EAAE;cAAG,CAAC;cAClBG,SAAS,EAAE;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,QAAQ,EAAE;cAAS,CAAC;cACvDC,IAAI,EAAE;gBAAEC,OAAO,EAAE;kBAAEL,GAAG,EAAEkB,SAAS,GAAG,UAAU,GAAG;gBAAW;cAAE,CAAC;cAAE;cACjEZ,MAAM,EAAE;gBACJC,GAAG,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAET,KAAK,EAAE;oBAAEC,GAAG,EAAE;kBAAW;gBAAE,CAAC;gBAClDS,MAAM,EAAE;kBAAED,KAAK,EAAE,MAAM;kBAAET,KAAK,EAAE;oBAAEC,GAAG,EAAE;kBAAW;gBAAE,CAAC;gBACrDU,IAAI,EAAE;kBAAEF,KAAK,EAAE,MAAM;kBAAET,KAAK,EAAE;oBAAEC,GAAG,EAAE;kBAAW;gBAAE,CAAC;gBACnDW,KAAK,EAAE;kBAAEH,KAAK,EAAE,MAAM;kBAAET,KAAK,EAAE;oBAAEC,GAAG,EAAE;kBAAW;gBAAE;cACvD;YACJ,CAAC;UACL;QACJ;MACJ,CAAC,CAAC;MAEFhM,IAAI,CAACyK,KAAK,CAAC0C,iBAAiB,CAACxC,EAAE,EAAEH,EAAE,EAAE,wBAAwB,CAAC;MAC9DxK,IAAI,CAACoN,SAAS,CAACzC,EAAE,EAAE,6BAA6B,EAAE;QAAE0C,UAAU,EAAE;MAAK,CAAC,CAAC;IAC3E;IACA7F,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAM8F,eAAe,GAAG,MAAAA,CAAA,KAAY;IAAA,IAAAC,sBAAA;IAClC,MAAMC,eAAe,GAAG/F,SAAS,CAACG,OAAO;IACzC,IAAI,CAAC4F,eAAe,EAAE;IAEtB,MAAMxF,KAAK,GAAG,EAAAuF,sBAAA,GAAAtF,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,cAAAqF,sBAAA,uBAA5BA,sBAAA,CAA8BpF,SAAS,KAAI,wBAAwB;IAEjF,MAAMC,MAAM,GAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IAED,MAAMqF,UAAU,GAAG;AACvB;AACA;AACA;AACA;AACA,iBAAiBzF,KAAK;AACtB,UAAUI,MAAM;AAChB;AACA;AACA,cAAcJ,KAAK;AACnB,UAAUwF,eAAe,CAAClF,SAAS;AACnC;AACA;AACA,KAAK;IAED,IAAI;MAAA,IAAAoF,gBAAA;MACF,MAAMlK,QAAQ,GAAG,MAAM/E,KAAK,CAACkP,IAAI,CAAC,2CAA2C,EAC3E;QAAEC,YAAY,EAAEH,UAAU;QAAEzF,KAAK,EAAEA;MAAM,CAAC,EAC1C;QAAE6F,YAAY,EAAE;MAAO,CACzB,CAAC;MAED,MAAMC,GAAG,GAAGhG,MAAM,CAACiG,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACzK,QAAQ,CAACE,IAAI,CAAC,EAAE;QAAEwK,IAAI,EAAE;MAAkB,CAAC,CAAC,CAAC;MAC9F,MAAMC,IAAI,GAAGlG,QAAQ,CAACmG,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGP,GAAG;MACfK,IAAI,CAACG,YAAY,CAAC,UAAU,EAAE,GAAGtG,KAAK,MAAM,CAAC;MAC7CC,QAAQ,CAACsG,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC;MAC/BA,IAAI,CAACM,KAAK,CAAC,CAAC;MACZ,CAAAf,gBAAA,GAAAS,IAAI,CAACO,UAAU,cAAAhB,gBAAA,uBAAfA,gBAAA,CAAiBiB,WAAW,CAACR,IAAI,CAAC;MAClCrG,MAAM,CAACiG,GAAG,CAACa,eAAe,CAACd,GAAG,CAAC;IAEjC,CAAC,CAAC,OAAOnK,KAAK,EAAE;MACdlD,OAAO,CAACkD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CV,WAAW,CAAC;QAAEW,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAExD,CAAC,CAAC,gBAAgB,CAAC;QAAEyD,QAAQ,EAAE;MAAQ,CAAC,CAAC;IAC9E;IAEA0D,WAAW,CAAC,CAAC;EACf,CAAC;EAED,oBACEtH,OAAA,CAACJ,oBAAoB;IAAC+O,WAAW,EAAEhP,YAAa;IAAAiP,QAAA,eAC9C5O,OAAA,CAACxB,GAAG;MAACqQ,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAF,QAAA,gBAChB5O,OAAA,CAACvB,UAAU;QAACsQ,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAJ,QAAA,EAClCzO,CAAC,CAAC,uBAAuB;MAAC;QAAA8O,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAEbpP,OAAA,CAACxB,GAAG;QAACqQ,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAACC,SAAS,EAAC,UAAU;QAAAb,QAAA,gBACjF5O,OAAA,CAACf,WAAW;UAAC4P,EAAE,EAAE;YAAEa,QAAQ,EAAE;UAAI,CAAE;UAAAd,QAAA,gBACjC5O,OAAA,CAACd,UAAU;YAAA0P,QAAA,EAAEzO,CAAC,CAAC,gBAAgB;UAAC;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9CpP,OAAA,CAACb,MAAM;YACLwQ,KAAK,EAAExO,cAAe;YACtByO,KAAK,EAAEzP,CAAC,CAAC,gBAAgB,CAAE;YAC3B0P,QAAQ,EAAGC,CAAC,IAAK;cACf1O,iBAAiB,CAAC0O,CAAC,CAACC,MAAM,CAACJ,KAAe,CAAC;cAC3CrO,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;cACvBE,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;YACzB,CAAE;YAAAoN,QAAA,gBAEF5O,OAAA,CAACZ,QAAQ;cAACuQ,KAAK,EAAC,EAAE;cAAAf,QAAA,EAAEzO,CAAC,CAAC,aAAa;YAAC;cAAA8O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC/C3O,QAAQ,CAACqK,GAAG,CAAE7E,MAAM,iBACnBjG,OAAA,CAACZ,QAAQ;cAAiBuQ,KAAK,EAAE1J,MAAM,CAACR,EAAG;cAAAmJ,QAAA,EACxC3I,MAAM,CAACP;YAAI,GADCO,MAAM,CAACR,EAAE;cAAAwJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEdpP,OAAA,CAACf,WAAW;UAAC4P,EAAE,EAAE;YAAEa,QAAQ,EAAE;UAAI,CAAE;UAAAd,QAAA,gBACjC5O,OAAA,CAACd,UAAU;YAAA0P,QAAA,EAAEzO,CAAC,CAAC,gBAAgB;UAAC;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9CpP,OAAA,CAACb,MAAM;YACLwQ,KAAK,EAAEtO,cAAe;YACtBuO,KAAK,EAAEzP,CAAC,CAAC,gBAAgB,CAAE;YAC3B0P,QAAQ,EAAGC,CAAC,IAAK;cACfxO,iBAAiB,CAACwO,CAAC,CAACC,MAAM,CAACJ,KAAe,CAAC;cAC3CnO,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;YACzB,CAAE;YAAAoN,QAAA,gBAEF5O,OAAA,CAACZ,QAAQ;cAACuQ,KAAK,EAAC,EAAE;cAAAf,QAAA,EAAEzO,CAAC,CAAC,YAAY;YAAC;cAAA8O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC9CzO,OAAO,CACLqP,MAAM,CAACnK,MAAM,IAAI1E,cAAc,KAAK,EAAE,IAAI0E,MAAM,CAACnB,SAAS,KAAKvD,cAAc,CAAC,CAC9E2J,GAAG,CAAEjF,MAAM,iBACV7F,OAAA,CAACZ,QAAQ;cAAiBuQ,KAAK,EAAE9J,MAAM,CAACJ,EAAG;cAAAmJ,QAAA,EACxC/I,MAAM,CAACH;YAAI,GADCG,MAAM,CAACJ,EAAE;cAAAwJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEdpP,OAAA,CAACf,WAAW;UAAC4P,EAAE,EAAE;YAAEa,QAAQ,EAAE;UAAI,CAAE;UAAAd,QAAA,gBACjC5O,OAAA,CAACd,UAAU;YAAA0P,QAAA,EAAEzO,CAAC,CAAC,gBAAgB;UAAC;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9CpP,OAAA,CAACb,MAAM;YACLwQ,KAAK,EAAEpO,cAAe;YACtBqO,KAAK,EAAEzP,CAAC,CAAC,gBAAgB,CAAE;YAC3B0P,QAAQ,EAAGC,CAAC,IAAKtO,iBAAiB,CAACsO,CAAC,CAACC,MAAM,CAACJ,KAAe,CAAE;YAAAf,QAAA,gBAE7D5O,OAAA,CAACZ,QAAQ;cAACuQ,KAAK,EAAC,EAAE;cAAAf,QAAA,EAAEzO,CAAC,CAAC,YAAY;YAAC;cAAA8O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC9CvO,OAAO,CACLmP,MAAM,CAAC1K,MAAM,IAAIjE,cAAc,KAAK,EAAE,IAAIiE,MAAM,CAACX,SAAS,KAAKtD,cAAc,CAAC,CAC9EyJ,GAAG,CAAExF,MAAM,iBACVtF,OAAA,CAACZ,QAAQ;cAAiBuQ,KAAK,EAAErK,MAAM,CAACG,EAAG;cAAAmJ,QAAA,EACxCtJ,MAAM,CAACI;YAAI,GADCJ,MAAM,CAACG,EAAE;cAAAwJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEdpP,OAAA,CAACN,UAAU;UACTkQ,KAAK,EAAEzP,CAAC,CAAC,eAAe,CAAE;UAC1B8P,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,CAAE;UACzBN,KAAK,EAAEhO,WAAY;UACnBkO,QAAQ,EAAGF,KAAmB,IAAK/N,cAAc,CAAC+N,KAAK,CAAE;UACzDO,KAAK,EAAE;YAAEC,SAAS,EAAE3Q;UAAU,CAAE;UAChC4Q,SAAS,EAAE;YACTD,SAAS,EAAE;cACTE,SAAS,EAAE,IAAI;cACfxB,EAAE,EAAE;gBAAEa,QAAQ,EAAE;cAAI;YACtB;UACF;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFpP,OAAA,CAACN,UAAU;UACTkQ,KAAK,EAAEzP,CAAC,CAAC,oBAAoB,CAAE;UAC/B8P,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,CAAE;UACzBN,KAAK,EAAElO,eAAgB;UACvBoO,QAAQ,EAAGF,KAAmB,IAAKjO,kBAAkB,CAACiO,KAAK,CAAE;UAC7DO,KAAK,EAAE;YAAEC,SAAS,EAAE3Q;UAAU,CAAE;UAChC4Q,SAAS,EAAE;YACTD,SAAS,EAAE;cACTE,SAAS,EAAE,IAAI;cACfxB,EAAE,EAAE;gBAAEa,QAAQ,EAAE;cAAI;YACtB;UACF;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFpP,OAAA,CAACN,UAAU;UACTkQ,KAAK,EAAEzP,CAAC,CAAC,kBAAkB,CAAE;UAC7B8P,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,CAAE;UACzBN,KAAK,EAAE9N,aAAc;UACrBgO,QAAQ,EAAGF,KAAmB,IAAK7N,gBAAgB,CAAC6N,KAAK,CAAE;UAC3DO,KAAK,EAAE;YAAEC,SAAS,EAAE3Q;UAAU,CAAE;UAChC4Q,SAAS,EAAE;YACTD,SAAS,EAAE;cACTE,SAAS,EAAE,IAAI;cACfxB,EAAE,EAAE;gBAAEa,QAAQ,EAAE;cAAI;YACtB;UACF;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFpP,OAAA,CAACf,WAAW;UAAC4P,EAAE,EAAE;YAAEa,QAAQ,EAAE;UAAI,CAAE;UAAAd,QAAA,gBACjC5O,OAAA,CAACd,UAAU;YAAA0P,QAAA,EAAEzO,CAAC,CAAC,iBAAiB;UAAC;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/CpP,OAAA,CAACb,MAAM;YACLwQ,KAAK,EAAE5N,aAAc;YACrB6N,KAAK,EAAEzP,CAAC,CAAC,iBAAiB,CAAE;YAC5B0P,QAAQ,EAAGC,CAAC,IAAK9N,gBAAgB,CAAC8N,CAAC,CAACC,MAAM,CAACJ,KAAe,CAAE;YAAAf,QAAA,gBAE5D5O,OAAA,CAACZ,QAAQ;cAACuQ,KAAK,EAAC,EAAE;cAAAf,QAAA,EAAEzO,CAAC,CAAC,aAAa;YAAC;cAAA8O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChDpP,OAAA,CAACZ,QAAQ;cAACuQ,KAAK,EAAE,CAAE;cAAAf,QAAA,EAAEzO,CAAC,CAAC,UAAU;YAAC;cAAA8O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9CpP,OAAA,CAACZ,QAAQ;cAACuQ,KAAK,EAAE,CAAE;cAAAf,QAAA,EAAEzO,CAAC,CAAC,UAAU;YAAC;cAAA8O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9CpP,OAAA,CAACZ,QAAQ;cAACuQ,KAAK,EAAE,CAAE;cAAAf,QAAA,EAAEzO,CAAC,CAAC,UAAU;YAAC;cAAA8O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9CpP,OAAA,CAACZ,QAAQ;cAACuQ,KAAK,EAAE,CAAE;cAAAf,QAAA,EAAEzO,CAAC,CAAC,UAAU;YAAC;cAAA8O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEdpP,OAAA,CAACf,WAAW;UAAC4P,EAAE,EAAE;YAAEa,QAAQ,EAAE;UAAI,CAAE;UAAAd,QAAA,eACjC5O,OAAA,CAACR,SAAS;YACRoQ,KAAK,EAAEzP,CAAC,CAAC,gBAAgB,CAAE;YAC3B6N,IAAI,EAAC,QAAQ;YACb2B,KAAK,EAAE1N,oBAAqB;YAC5B4N,QAAQ,EAAGC,CAAC,IAAK5N,uBAAuB,CAACoO,QAAQ,CAACR,CAAC,CAACC,MAAM,CAACJ,KAAK,EAAE,EAAE,CAAC;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAEdpP,OAAA,CAACT,MAAM;UACLwP,OAAO,EAAC,UAAU;UAClBlD,KAAK,EAAC,WAAW;UACjB0E,OAAO,EAAEpL,kBAAmB;UAAAyJ,QAAA,EAE3BzO,CAAC,CAAC,cAAc;QAAC;UAAA8O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAETpP,OAAA,CAACT,MAAM;UACLkG,EAAE,EAAC,eAAe;UAClB,iBAAe/B,IAAI,GAAG,aAAa,GAAG8M,SAAU;UAChD,iBAAc,MAAM;UACpB,iBAAe9M,IAAI,GAAG,MAAM,GAAG8M,SAAU;UACzCD,OAAO,EAAEpJ,WAAY;UACrB4H,OAAO,EAAC,WAAW;UAAAH,QAAA,EAElBzO,CAAC,CAAC,QAAQ;QAAC;UAAA8O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACTpP,OAAA,CAACP,IAAI;UACHgG,EAAE,EAAC,aAAa;UAChBuB,QAAQ,EAAEA,QAAS;UACnBtD,IAAI,EAAEA,IAAK;UACX+M,OAAO,EAAEnJ,WAAY;UACrBoJ,aAAa,EAAE;YACb,iBAAiB,EAAE;UACrB,CAAE;UAAA9B,QAAA,gBAEF5O,OAAA,CAACZ,QAAQ;YAACmR,OAAO,EAAE/I,WAAY;YAAAoH,QAAA,EAAEzO,CAAC,CAAC,OAAO;UAAC;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACvDpP,OAAA,CAACZ,QAAQ;YAACmR,OAAO,EAAEjI,iBAAkB;YAAAsG,QAAA,EAAEzO,CAAC,CAAC,eAAe;UAAC;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACrEpP,OAAA,CAACZ,QAAQ;YAACmR,OAAO,EAAEnD,eAAgB;YAAAwB,QAAA,EAAEzO,CAAC,CAAC,aAAa;UAAC;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENpP,OAAA;QAAK2Q,GAAG,EAAEpJ,SAAU;QAAC+E,KAAK,EAAE;UAAEsE,UAAU,EAAE;QAA2B,CAAE;QAAAhC,QAAA,gBACrE5O,OAAA,CAACvB,UAAU;UAACsQ,OAAO,EAAC,IAAI;UAACC,YAAY;UAACH,EAAE,EAAE;YAAEgC,EAAE,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAlC,QAAA,EACtEzD,MAAM,CAAChL,CAAC,CAACwC,eAAe,CAACE,GAAG,EAAEF,eAAe,CAACuB,MAAM,CAAC;QAAC;UAAA+K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,EAEZ7N,cAAc;QAAA;QACb;QACAvB,OAAA,CAACxB,GAAG;UAAAoQ,QAAA,gBACF5O,OAAA,CAACvB,UAAU;YAACsQ,OAAO,EAAC,IAAI;YAACC,YAAY;YAACH,EAAE,EAAE;cAAEgC,EAAE,EAAE;YAAE,CAAE;YAAAjC,QAAA,GACjDzO,CAAC,CAAC,iBAAiB,CAAC,EAAC,GAAC,EAACiF,aAAa,CAAC7D,cAAwB,CAAC;UAAA;YAAA0N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACbpP,OAAA,CAACnB,cAAc;YAACkS,SAAS,EAAE/R,KAAM;YAAA4P,QAAA,eAC/B5O,OAAA,CAACtB,KAAK;cAAAkQ,QAAA,gBACJ5O,OAAA,CAAClB,SAAS;gBAAA8P,QAAA,eACR5O,OAAA,CAACjB,QAAQ;kBAAA6P,QAAA,gBACP5O,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,MAAM;kBAAC;oBAAA8O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClCpP,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,UAAU;kBAAC;oBAAA8O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtCpP,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,WAAW;kBAAC;oBAAA8O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvCpP,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,eAAe;kBAAC;oBAAA8O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,eAAe;kBAAC;oBAAA8O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,WAAW;kBAAC;oBAAA8O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZpP,OAAA,CAACrB,SAAS;gBAAAiQ,QAAA,EACPvM,oBAAoB,CAACyI,GAAG,CAAC,CAACtH,IAAI,EAAEwN,KAAK,kBACpChR,OAAA,CAACjB,QAAQ;kBAAA6P,QAAA,gBACP5O,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEzI,WAAW,CAAC3C,IAAI,CAACgD,MAAM,EAAEhD,IAAI,CAACyN,QAAQ;kBAAC;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChEpP,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEjI,eAAe,CAACnD,IAAI,CAACsD,UAAU;kBAAC;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzDpP,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEpL,IAAI,CAAC0N;kBAAS;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvCpP,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEpL,IAAI,CAAC2N;kBAAa;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEpL,IAAI,CAAC4N;kBAAa;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;oBAAAgQ,QAAA,EAAEpL,IAAI,CAAC6N;kBAAS;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAN1B4B,KAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,GACJ/N,cAAc;QAAA;QAChB;QACArB,OAAA,CAACxB,GAAG;UAAAoQ,QAAA,gBACF5O,OAAA,CAACvB,UAAU;YAACsQ,OAAO,EAAC,IAAI;YAACC,YAAY;YAACH,EAAE,EAAE;cAAEgC,EAAE,EAAE;YAAE,CAAE;YAAAjC,QAAA,GACjDzO,CAAC,CAAC,iBAAiB,CAAC,EAAC,GAAC,EAACwF,aAAa,CAACtE,cAAwB,CAAC;UAAA;YAAA4N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,EAEZzO,OAAO,CACLqP,MAAM,CAAClK,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKpE,cAAc,CAAC,CACpCyJ,GAAG,CAACwG,cAAc,iBACjBtR,OAAA,CAACxB,GAAG;YAAyBqQ,EAAE,EAAE;cAAEU,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,GACxC/N,OAAO,CACLmP,MAAM,CAAC1K,MAAM,IAAIA,MAAM,CAACX,SAAS,KAAK2M,cAAc,CAAC7L,EAAE,CAAC,CACxDqF,GAAG,CAACxF,MAAM,IAAI;cACb,MAAMiM,UAAU,GAAGlP,oBAAoB,CAAC2N,MAAM,CAACzJ,CAAC,IAAIA,CAAC,CAACiL,QAAQ,KAAKlM,MAAM,CAACG,EAAE,CAAC;cAC7E,IAAI8L,UAAU,CAACtI,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;cACxC,oBACEjJ,OAAA,CAACxB,GAAG;gBAAiBqQ,EAAE,EAAE;kBAAEU,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,gBACjC5O,OAAA,CAACvB,UAAU;kBAACsQ,OAAO,EAAC,IAAI;kBAACF,EAAE,EAAE;oBAAEgC,EAAE,EAAE;kBAAE,CAAE;kBAAAjC,QAAA,GACpCzO,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAACmF,MAAM,CAACI,IAAI;gBAAA;kBAAAuJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACbpP,OAAA,CAACnB,cAAc;kBAACkS,SAAS,EAAE/R,KAAM;kBAAA4P,QAAA,eAC/B5O,OAAA,CAACtB,KAAK;oBAACkN,IAAI,EAAC,OAAO;oBAAAgD,QAAA,gBACjB5O,OAAA,CAAClB,SAAS;sBAAA8P,QAAA,eACR5O,OAAA,CAACjB,QAAQ;wBAAA6P,QAAA,gBACP5O,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,MAAM;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAClCpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,UAAU;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACtCpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,WAAW;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACvCpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,eAAe;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,eAAe;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,WAAW;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACZpP,OAAA,CAACrB,SAAS;sBAAAiQ,QAAA,EACP2C,UAAU,CAACzG,GAAG,CAAC,CAACtH,IAAI,EAAEwN,KAAK,kBAC1BhR,OAAA,CAACjB,QAAQ;wBAAA6P,QAAA,gBACP5O,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzI,WAAW,CAAC3C,IAAI,CAACgD,MAAM,EAAEhD,IAAI,CAACyN,QAAQ;wBAAC;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAChEpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEjI,eAAe,CAACnD,IAAI,CAACsD,UAAU;wBAAC;0BAAAmI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACzDpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEpL,IAAI,CAAC0N;wBAAS;0BAAAjC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACvCpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEpL,IAAI,CAAC2N;wBAAa;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEpL,IAAI,CAAC4N;wBAAa;0BAAAnC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEpL,IAAI,CAAC6N;wBAAS;0BAAApC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA,GAN1B4B,KAAK;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOV,CACX;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,GA7BT9J,MAAM,CAACG,EAAE;gBAAAwJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8Bd,CAAC;YAEV,CAAC,CAAC,eACJpP,OAAA,CAACvB,UAAU;cAACsQ,OAAO,EAAC,IAAI;cAACF,EAAE,EAAE;gBAAEgC,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,GACpCzO,CAAC,CAAC,gBAAgB,CAAC,EAAC,IAAE,EAACmR,cAAc,CAAC5L,IAAI;YAAA;cAAAuJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACbpP,OAAA,CAACnB,cAAc;cAACkS,SAAS,EAAE/R,KAAM;cAAA4P,QAAA,eAC/B5O,OAAA,CAACtB,KAAK;gBAAAkQ,QAAA,gBACJ5O,OAAA,CAAClB,SAAS;kBAAA8P,QAAA,eACR5O,OAAA,CAACjB,QAAQ;oBAAA6P,QAAA,gBACP5O,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,MAAM;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAClCpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,UAAU;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACtCpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,WAAW;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvCpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,eAAe;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,eAAe;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,WAAW;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZpP,OAAA,CAACrB,SAAS;kBAAAiQ,QAAA,EACPrM,oBAAoB,CAACuI,GAAG,CAAC,CAACtH,IAAI,EAAEwN,KAAK,kBACpChR,OAAA,CAACjB,QAAQ;oBAAA6P,QAAA,gBACP5O,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzI,WAAW,CAAC3C,IAAI,CAACgD,MAAM,EAAEhD,IAAI,CAACyN,QAAQ;oBAAC;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAChEpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEjI,eAAe,CAACnD,IAAI,CAACsD,UAAU;oBAAC;sBAAAmI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzDpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEpL,IAAI,CAAC0N;oBAAS;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvCpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEpL,IAAI,CAAC2N;oBAAa;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEpL,IAAI,CAAC4N;oBAAa;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEpL,IAAI,CAAC6N;oBAAS;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA,GAN1B4B,KAAK;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOV,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA,GApETkC,cAAc,CAAC7L,EAAE;YAAAwJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqEtB,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,GACJjO,cAAc;QAAA;QAChB;QACAnB,OAAA,CAACxB,GAAG;UAAAoQ,QAAA,gBACF5O,OAAA,CAACvB,UAAU;YAACsQ,OAAO,EAAC,IAAI;YAACC,YAAY;YAACH,EAAE,EAAE;cAAEgC,EAAE,EAAE;YAAE,CAAE;YAAAjC,QAAA,GACjDzO,CAAC,CAAC,iBAAiB,CAAC,EAAC,GAAC,EAAC4F,aAAa,CAAC5E,cAAwB,CAAC;UAAA;YAAA8N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,EAEZ3O,QAAQ,CACNuP,MAAM,CAAC9J,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAKtE,cAAc,CAAC,CACpC2J,GAAG,CAAC2G,cAAc,iBACjBzR,OAAA,CAACxB,GAAG;YAAyBqQ,EAAE,EAAE;cAAEU,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,GACxCjO,OAAO,CACLqP,MAAM,CAACnK,MAAM,IAAIA,MAAM,CAACnB,SAAS,KAAK+M,cAAc,CAAChM,EAAE,CAAC,CACxDqF,GAAG,CAACjF,MAAM,IAAI;cACb,MAAM6L,UAAU,GAAGnP,oBAAoB,CAACyN,MAAM,CAACzJ,CAAC,IAAIA,CAAC,CAACoL,QAAQ,KAAK9L,MAAM,CAACJ,EAAE,CAAC;cAC7E,IAAIiM,UAAU,CAACzI,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;cACxC,oBACEjJ,OAAA,CAACxB,GAAG;gBAAiBqQ,EAAE,EAAE;kBAAEU,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,gBACjC5O,OAAA,CAACvB,UAAU;kBAACsQ,OAAO,EAAC,IAAI;kBAACF,EAAE,EAAE;oBAAEgC,EAAE,EAAE;kBAAE,CAAE;kBAAAjC,QAAA,GACpCzO,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAAC0F,MAAM,CAACH,IAAI;gBAAA;kBAAAuJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACbpP,OAAA,CAACnB,cAAc;kBAACkS,SAAS,EAAE/R,KAAM;kBAAA4P,QAAA,eAC/B5O,OAAA,CAACtB,KAAK;oBAACkN,IAAI,EAAC,OAAO;oBAAAgD,QAAA,gBACjB5O,OAAA,CAAClB,SAAS;sBAAA8P,QAAA,eACR5O,OAAA,CAACjB,QAAQ;wBAAA6P,QAAA,gBACP5O,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,MAAM;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAClCpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,UAAU;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACtCpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,WAAW;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACvCpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,eAAe;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,eAAe;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzO,CAAC,CAAC,WAAW;wBAAC;0BAAA8O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACZpP,OAAA,CAACrB,SAAS;sBAAAiQ,QAAA,EACP8C,UAAU,CAAC5G,GAAG,CAAC,CAACtH,IAAI,EAAEwN,KAAK,kBAC1BhR,OAAA,CAACjB,QAAQ;wBAAA6P,QAAA,gBACP5O,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEzI,WAAW,CAAC3C,IAAI,CAACgD,MAAM,EAAEhD,IAAI,CAACyN,QAAQ;wBAAC;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAChEpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEjI,eAAe,CAACnD,IAAI,CAACsD,UAAU;wBAAC;0BAAAmI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACzDpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEpL,IAAI,CAAC0N;wBAAS;0BAAAjC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACvCpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEpL,IAAI,CAAC2N;wBAAa;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEpL,IAAI,CAAC4N;wBAAa;0BAAAnC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;0BAAAgQ,QAAA,EAAEpL,IAAI,CAAC6N;wBAAS;0BAAApC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA,GAN1B4B,KAAK;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOV,CACX;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,GA7BTvJ,MAAM,CAACJ,EAAE;gBAAAwJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8Bd,CAAC;YAEV,CAAC,CAAC,eACJpP,OAAA,CAACvB,UAAU;cAACsQ,OAAO,EAAC,IAAI;cAACF,EAAE,EAAE;gBAAEgC,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,GACpCzO,CAAC,CAAC,gBAAgB,CAAC,EAAC,IAAE,EAACsR,cAAc,CAAC/L,IAAI;YAAA;cAAAuJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACbpP,OAAA,CAACnB,cAAc;cAACkS,SAAS,EAAE/R,KAAM;cAAA4P,QAAA,eAC/B5O,OAAA,CAACtB,KAAK;gBAAAkQ,QAAA,gBACJ5O,OAAA,CAAClB,SAAS;kBAAA8P,QAAA,eACR5O,OAAA,CAACjB,QAAQ;oBAAA6P,QAAA,gBACP5O,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,MAAM;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAClCpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,UAAU;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACtCpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,WAAW;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvCpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,eAAe;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,eAAe;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzO,CAAC,CAAC,WAAW;oBAAC;sBAAA8O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZpP,OAAA,CAACrB,SAAS;kBAAAiQ,QAAA,EACPnM,oBAAoB,CAACqI,GAAG,CAAC,CAACtH,IAAI,EAAEwN,KAAK,kBACpChR,OAAA,CAACjB,QAAQ;oBAAA6P,QAAA,gBACP5O,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEzI,WAAW,CAAC3C,IAAI,CAACgD,MAAM,EAAEhD,IAAI,CAACyN,QAAQ;oBAAC;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAChEpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEjI,eAAe,CAACnD,IAAI,CAACsD,UAAU;oBAAC;sBAAAmI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzDpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEpL,IAAI,CAAC0N;oBAAS;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvCpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEpL,IAAI,CAAC2N;oBAAa;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEpL,IAAI,CAAC4N;oBAAa;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3CpP,OAAA,CAACpB,SAAS;sBAAAgQ,QAAA,EAAEpL,IAAI,CAAC6N;oBAAS;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA,GAN1B4B,KAAK;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOV,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA,GApETqC,cAAc,CAAChM,EAAE;YAAAwJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqEtB,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;QAAA;QAEN;QACApP,OAAA,CAACvB,UAAU;UAACsQ,OAAO,EAAC,OAAO;UAACF,EAAE,EAAE;YAAEgC,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,EACvCzO,CAAC,CAAC,wBAAwB;QAAC;UAAA8O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELtM,QAAQ,iBACP9C,OAAA,CAACX,QAAQ;QACPqE,IAAI,EAAEZ,QAAQ,CAACY,IAAK;QACpBkO,gBAAgB,EAAE,IAAK;QACvBnB,OAAO,EAAEA,CAAA,KAAM1N,WAAW,CAAC,IAAI,CAAE;QACjC8O,YAAY,EAAE;UAAE5F,QAAQ,EAAE,QAAQ;UAAED,UAAU,EAAE;QAAS,CAAE;QAAA4C,QAAA,eAE3D5O,OAAA,CAACV,KAAK;UAACmR,OAAO,EAAEA,CAAA,KAAM1N,WAAW,CAAC,IAAI,CAAE;UAACa,QAAQ,EAAEd,QAAQ,CAACc,QAAS;UAACiL,EAAE,EAAE;YAAEiD,KAAK,EAAE;UAAO,CAAE;UAAAlD,QAAA,EACzF9L,QAAQ,CAACa;QAAO;UAAAsL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACX;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAE3B,CAAC;AAAClP,EAAA,CAz2BID,yBAAyB;EAAA,QACT3B,cAAc;AAAA;AAAAyT,EAAA,GAD9B9R,yBAAyB;AA42B/B,eAAeA,yBAAyB;AAAC,IAAA8R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}