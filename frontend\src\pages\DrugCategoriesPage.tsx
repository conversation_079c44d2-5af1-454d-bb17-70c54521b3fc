
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Snackbar,
  Alert,
} from '@mui/material';
import { Edit, Delete } from '@mui/icons-material';

interface DrugCategory {
  CategoryID: number;
  CategoryName: string;
}

const DrugCategoriesPage = () => {
  const { t } = useTranslation();
  const [categories, setCategories] = useState<DrugCategory[]>([]);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<DrugCategory | null>(null);
  const [categoryName, setCategoryName] = useState(''); // For edit dialog
  const [newCategoryName, setNewCategoryName] = useState(''); // For add input
  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);
  const [newCategoryNameError, setNewCategoryNameError] = useState(false);
  const [categoryNameError, setCategoryNameError] = useState(false);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/');
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching drug categories:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const handleAdd = async () => {
    if (!newCategoryName.trim()) {
      setNewCategoryNameError(true);
      setSnackbar({ open: true, message: t('categoryNameRequired'), severity: 'error' });
      return;
    }
    setNewCategoryNameError(false);
    try {
      await axios.post('http://localhost:8000/drug-categories/', { CategoryName: newCategoryName });
      fetchCategories();
      setNewCategoryName('');
      setSnackbar({ open: true, message: t('categoryAddedSuccess'), severity: 'success' });
    } catch (error) {
      console.error('Error adding category:', error);
      setSnackbar({ open: true, message: t('categoryOperationError'), severity: 'error' });
    }
  };

  const handleUpdate = async () => {
    if (!categoryName.trim()) {
      setCategoryNameError(true);
      setSnackbar({ open: true, message: t('categoryNameRequired'), severity: 'error' });
      return;
    }
    setCategoryNameError(false);
    if (selectedCategory) {
      try {
        await axios.put(`http://localhost:8000/drug-categories/${selectedCategory.CategoryID}`, { CategoryName: categoryName });
        fetchCategories();
        setOpenEditDialog(false);
        setCategoryName('');
        setSelectedCategory(null);
        setSnackbar({ open: true, message: t('categoryUpdatedSuccess'), severity: 'success' });
      } catch (error) {
        console.error('Error updating category:', error);
        setSnackbar({ open: true, message: t('categoryOperationError'), severity: 'error' });
      }
    }
  };

  const handleDelete = async () => {
    if (selectedCategory) {
      try {
        await axios.delete(`http://localhost:8000/drug-categories/${selectedCategory.CategoryID}`);
        fetchCategories();
        setOpenDeleteDialog(false);
        setSelectedCategory(null);
        setSnackbar({ open: true, message: t('categoryDeletedSuccess'), severity: 'success' });
      } catch (error) {
        console.error('Error deleting category:', error);
        setSnackbar({ open: true, message: t('categoryOperationError'), severity: 'error' });
      }
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {t('drugCategories')}
      </Typography>
      <Box sx={{ display: 'flex', mb: 2 }}>
        <TextField
          label={t('categoryName')}
          variant="outlined"
          value={newCategoryName}
          onChange={(e) => { setNewCategoryName(e.target.value); setNewCategoryNameError(false); }}
          sx={{ mr: 1 }}
          error={newCategoryNameError}
          helperText={newCategoryNameError ? t('categoryNameRequired') : ''}
        />
        <Button variant="contained" color="primary" onClick={handleAdd}>
          {t('addCategory')}
        </Button>
      </Box>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('categoryName')}</TableCell>
              <TableCell>{t('actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {categories.map((category) => (
              <TableRow key={category.CategoryID}>
                <TableCell>{category.CategoryName}</TableCell>
                <TableCell>
                  <IconButton color="primary" onClick={() => { setSelectedCategory(category); setCategoryName(category.CategoryName); setCategoryNameError(false); setOpenEditDialog(true); }}>
                    <Edit />
                  </IconButton>
                  <IconButton color="secondary" onClick={() => { setSelectedCategory(category); setOpenDeleteDialog(true); }}>
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Edit Dialog */}
      <Dialog open={openEditDialog} onClose={() => setOpenEditDialog(false)}>
        <DialogTitle>{t('editCategory')}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label={t('categoryName')}
            type="text"
            fullWidth
            variant="standard"
            value={categoryName}
            onChange={(e) => { setCategoryName(e.target.value); setCategoryNameError(false); }}
            error={categoryNameError}
            helperText={categoryNameError ? t('categoryNameRequired') : ''}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenEditDialog(false)}>{t('cancel')}</Button>
          <Button onClick={handleUpdate}>{t('save')}</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>{t('deleteCategory')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('deleteCategoryConfirmation')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>{t('cancel')}</Button>
          <Button onClick={handleDelete} color="secondary">{t('delete')}</Button>
        </DialogActions>
      </Dialog>

      {snackbar && (
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      )}
    </Box>
  );
};

export default DrugCategoriesPage;
