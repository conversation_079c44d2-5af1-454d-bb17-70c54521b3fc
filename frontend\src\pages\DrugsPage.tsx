
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Snackbar,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import { Edit, Delete } from '@mui/icons-material';

interface Drug {
  DrugID: number;
  CategoryID: number;
  DrugName: string;
  Unit: string | null;
}

interface DrugCategory {
  CategoryID: number;
  CategoryName: string;
}

const DrugsPage = () => {
  const { t } = useTranslation();
  const [drugs, setDrugs] = useState<Drug[]>([]);
  const [categories, setCategories] = useState<DrugCategory[]>([]);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedDrug, setSelectedDrug] = useState<Drug | null>(null);
  const [drugName, setDrugName] = useState('');
  const [unit, setUnit] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | ''>('');
  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);
  const [drugNameError, setDrugNameError] = useState(false);
  const [categoryError, setCategoryError] = useState(false);
  const [unitError, setUnitError] = useState(false);

  // Bulk Add States
  const [bulkDrugNames, setBulkDrugNames] = useState('');
  const [bulkUnits, setBulkUnits] = useState('');
  const [bulkCategoryId, setBulkCategoryId] = useState<number | ''>('');
  const [bulkDrugNamesError, setBulkDrugNamesError] = useState(false);
  const [bulkUnitsError, setBulkUnitsError] = useState(false);
  const [bulkCategoryError, setBulkCategoryError] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [filterCategoryId, setFilterCategoryId] = useState<number | ''>('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchDrugs();
    fetchCategories();
  }, [filterCategoryId, searchTerm]);

  const fetchDrugs = async () => {
    try {
      const params: { category_id?: number; search?: string } = {};
      if (filterCategoryId) {
        params.category_id = filterCategoryId as number;
      }
      if (searchTerm) {
        params.search = searchTerm;
      }
      const response = await axios.get<Drug[]>('http://localhost:8000/drugs/', { params });
      setDrugs(response.data);
    } catch (error) {
      console.error('Error fetching drugs:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/');
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching drug categories:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const handleAdd = async () => {
    let hasError = false;
    if (!drugName.trim()) {
      setDrugNameError(true);
      hasError = true;
    }
    if (!selectedCategoryId) {
      setCategoryError(true);
      hasError = true;
    }
    if (!unit.trim()) {
      setUnitError(true);
      hasError = true;
    }

    if (hasError) {
      setSnackbar({ open: true, message: t('fillAllRequiredFields'), severity: 'error' });
      return;
    }

    try {
      await axios.post('http://localhost:8000/drugs/', { DrugName: drugName, CategoryID: selectedCategoryId, Unit: unit });
      fetchDrugs();
      setDrugName('');
      setUnit('');
      setSelectedCategoryId('');
      setDrugNameError(false);
      setCategoryError(false);
      setUnitError(false);
      setSnackbar({ open: true, message: t('drugAddedSuccess'), severity: 'success' });
    } catch (error) {
      console.error('Error adding drug:', error);
      setSnackbar({ open: true, message: t('drugOperationError'), severity: 'error' });
    }
  };

  const handleBulkAdd = async () => {
    let hasError = false;
    const names = bulkDrugNames.split('\n').map(s => s.trim()).filter(s => s);
    const units = bulkUnits.split('\n').map(s => s.trim()).filter(s => s);

    if (names.length === 0) {
      setBulkDrugNamesError(true);
      hasError = true;
    }
    if (units.length === 0) {
      setBulkUnitsError(true);
      hasError = true;
    }
    if (names.length !== units.length) {
      setSnackbar({ open: true, message: t('bulkMismatchError'), severity: 'error' });
      hasError = true;
    }
    if (!bulkCategoryId) {
      setBulkCategoryError(true);
      hasError = true;
    }

    if (hasError) {
      setSnackbar({ open: true, message: t('fillAllRequiredFields'), severity: 'error' });
      return;
    }

    const drugsToCreate = names.map((name, index) => ({
      DrugName: name,
      CategoryID: bulkCategoryId as number,
      Unit: units[index],
    }));

    try {
      await axios.post('http://localhost:8000/drugs/bulk/', { drugs: drugsToCreate });
      fetchDrugs();
      setBulkDrugNames('');
      setBulkUnits('');
      setBulkCategoryId('');
      setBulkDrugNamesError(false);
      setBulkUnitsError(false);
      setBulkCategoryError(false);
      setSnackbar({ open: true, message: t('bulkDrugsAddedSuccess'), severity: 'success' });
    } catch (error) {
      console.error('Error adding bulk drugs:', error);
      setSnackbar({ open: true, message: t('drugOperationError'), severity: 'error' });
    }
  };

  const handleUpdate = async () => {
    let hasError = false;
    if (!drugName.trim()) {
      setDrugNameError(true);
      hasError = true;
    }
    if (!selectedCategoryId) {
      setCategoryError(true);
      hasError = true;
    }
    if (!unit.trim()) {
      setUnitError(true);
      hasError = true;
    }

    if (hasError) {
      setSnackbar({ open: true, message: t('fillAllRequiredFields'), severity: 'error' });
      return;
    }

    if (selectedDrug) {
      try {
        await axios.put(`http://localhost:8000/drugs/${selectedDrug.DrugID}`, { DrugName: drugName, CategoryID: selectedCategoryId, Unit: unit });
        fetchDrugs();
        setOpenEditDialog(false);
        setDrugName('');
        setUnit('');
        setSelectedCategoryId('');
        setSelectedDrug(null);
        setDrugNameError(false);
        setCategoryError(false);
        setUnitError(false);
        setSnackbar({ open: true, message: t('drugUpdatedSuccess'), severity: 'success' });
      } catch (error) {
        console.error('Error updating drug:', error);
        setSnackbar({ open: true, message: t('drugOperationError'), severity: 'error' });
      }
    }
  };

  const handleDelete = async () => {
    if (selectedDrug) {
      try {
        await axios.delete(`http://localhost:8000/drugs/${selectedDrug.DrugID}`);
        fetchDrugs();
        setOpenDeleteDialog(false);
        setSelectedDrug(null);
        setSnackbar({ open: true, message: t('drugDeletedSuccess'), severity: 'success' });
      } catch (error) {
        console.error('Error deleting drug:', error);
        setSnackbar({ open: true, message: t('drugOperationError'), severity: 'error' });
      }
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {t('drugs')}
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, mb: 2, alignItems: 'center' }}>
        <FormControl sx={{ minWidth: 180 }}>
          <InputLabel>{t('filterByCategory')}</InputLabel>
          <Select
            value={filterCategoryId}
            onChange={(e) => setFilterCategoryId(e.target.value as number)}
            label={t('filterByCategory')}
          >
            <MenuItem value="">{t('allCategories')}</MenuItem>
            {categories.map((category) => (
              <MenuItem key={category.CategoryID} value={category.CategoryID}>
                {category.CategoryName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <TextField
          label={t('searchByDrugName')}
          variant="outlined"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ flexGrow: 1 }}
        />
      </Box>

      <Tabs value={tabValue} onChange={handleTabChange} aria-label="drug add tabs">
        <Tab label={t('singleAdd')} />
        <Tab label={t('bulkAdd')} />
      </Tabs>

      {tabValue === 0 && (
        <Box sx={{ display: 'flex', mb: 2, mt: 2 }}>
          <TextField
            label={t('drugName')}
            variant="outlined"
            value={drugName}
            onChange={(e) => { setDrugName(e.target.value); setDrugNameError(false); }}
            sx={{ mr: 1 }}
            error={drugNameError}
            helperText={drugNameError ? t('drugNameRequired') : ''}
          />
          <FormControl sx={{ mr: 1, minWidth: 120 }} error={categoryError}>
            <InputLabel>{t('category')}</InputLabel>
            <Select
              value={selectedCategoryId}
              onChange={(e) => { setSelectedCategoryId(e.target.value as number); setCategoryError(false); }}
              label={t('category')}
            >
              {categories.map((category) => (
                <MenuItem key={category.CategoryID} value={category.CategoryID}>
                  {category.CategoryName}
                </MenuItem>
              ))}
            </Select>
            {categoryError && <Typography color="error" variant="caption">{t('categoryRequired')}</Typography>}
          </FormControl>
          <TextField
            label={t('unit')}
            variant="outlined"
            value={unit}
            onChange={(e) => { setUnit(e.target.value); setUnitError(false); }}
            sx={{ mr: 1 }}
            error={unitError}
            helperText={unitError ? t('unitRequired') : ''}
          />
          <Button variant="contained" color="primary" onClick={handleAdd}>
            {t('addDrug')}
          </Button>
        </Box>
      )}

      {tabValue === 1 && (
        <Box sx={{ mt: 2 }}>
          <FormControl fullWidth sx={{ mb: 2 }} error={bulkCategoryError}>
            <InputLabel>{t('category')}</InputLabel>
            <Select
              value={bulkCategoryId}
              onChange={(e) => { setBulkCategoryId(e.target.value as number); setBulkCategoryError(false); }}
              label={t('category')}
            >
              {categories.map((category) => (
                <MenuItem key={category.CategoryID} value={category.CategoryID}>
                  {category.CategoryName}
                </MenuItem>
              ))}
            </Select>
            {bulkCategoryError && <Typography color="error" variant="caption">{t('categoryRequired')}</Typography>}
          </FormControl>
          <TextField
            label={t('drugNamesLabel')}
            multiline
            rows={6}
            fullWidth
            variant="outlined"
            value={bulkDrugNames}
            onChange={(e) => { setBulkDrugNames(e.target.value); setBulkDrugNamesError(false); }}
            sx={{ mb: 2 }}
            error={bulkDrugNamesError}
            helperText={bulkDrugNamesError ? t('drugNamesRequired') : t('enterOneDrugNamePerLine')}
          />
          <TextField
            label={t('unitsLabel')}
            multiline
            rows={6}
            fullWidth
            variant="outlined"
            value={bulkUnits}
            onChange={(e) => { setBulkUnits(e.target.value); setBulkUnitsError(false); }}
            sx={{ mb: 2 }}
            error={bulkUnitsError}
            helperText={bulkUnitsError ? t('unitsRequired') : t('enterOneUnitPerLine')}
          />
          <Button variant="contained" color="primary" onClick={handleBulkAdd}>
            {t('bulkAddDrugs')}
          </Button>
        </Box>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('drugName')}</TableCell>
              <TableCell>{t('category')}</TableCell>
              <TableCell>{t('unit')}</TableCell>
              <TableCell>{t('actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {drugs.map((drug) => (
              <TableRow key={drug.DrugID}>
                <TableCell>{drug.DrugName}</TableCell>
                <TableCell>{categories.find(c => c.CategoryID === drug.CategoryID)?.CategoryName || ''}</TableCell>
                <TableCell>{drug.Unit}</TableCell>
                <TableCell>
                  <IconButton color="primary" onClick={() => { setSelectedDrug(drug); setDrugName(drug.DrugName); setUnit(drug.Unit || ''); setSelectedCategoryId(drug.CategoryID); setDrugNameError(false); setCategoryError(false); setUnitError(false); setOpenEditDialog(true); }}>
                    <Edit />
                  </IconButton>
                  <IconButton color="secondary" onClick={() => { setSelectedDrug(drug); setOpenDeleteDialog(true); }}>
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Edit Dialog */}
      <Dialog open={openEditDialog} onClose={() => setOpenEditDialog(false)}>
        <DialogTitle>{t('editDrug')}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label={t('drugName')}
            type="text"
            fullWidth
            variant="standard"
            value={drugName}
            onChange={(e) => { setDrugName(e.target.value); setDrugNameError(false); }}
            error={drugNameError}
            helperText={drugNameError ? t('drugNameRequired') : ''}
          />
          <FormControl fullWidth sx={{ mt: 2 }} error={categoryError}>
            <InputLabel>{t('category')}</InputLabel>
            <Select
              value={selectedCategoryId}
              onChange={(e) => { setSelectedCategoryId(e.target.value as number); setCategoryError(false); }}
              label={t('category')}
            >
              {categories.map((category) => (
                <MenuItem key={category.CategoryID} value={category.CategoryID}>
                  {category.CategoryName}
                </MenuItem>
              ))}
            </Select>
            {categoryError && <Typography color="error" variant="caption">{t('categoryRequired')}</Typography>}
          </FormControl>
          <TextField
            margin="dense"
            label={t('unit')}
            type="text"
            fullWidth
            variant="standard"
            value={unit}
            onChange={(e) => { setUnit(e.target.value); setUnitError(false); }}
            error={unitError}
            helperText={unitError ? t('unitRequired') : ''}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenEditDialog(false)}>{t('cancel')}</Button>
          <Button onClick={handleUpdate}>{t('save')}</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>{t('deleteDrug')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('deleteDrugConfirmation')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>{t('cancel')}</Button>
          <Button onClick={handleDelete} color="secondary">{t('delete')}</Button>
        </DialogActions>
      </Dialog>

      {snackbar && (
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      )}
    </Box>
  );
};

export default DrugsPage;
