import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Alert,
  Button,
  TextField,
  Menu,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/ar'; // Import Arabic locale
import 'dayjs/locale/en'; // Import English locale
import i18n from 'i18next'; // Import i18n instance
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';

// Interfaces for data from backend
interface Branch {
  id: number;
  name: string;
}

interface Region {
  id: number;
  name: string;
  branch_id: number;
}

interface Clinic {
  id: number;
  name: string;
  region_id: number;
}

interface Drug {
  DrugID: number;
  DrugName: string;
  Unit: string | null;
  CategoryID: number;
}

interface DrugCategory {
  CategoryID: number;
  CategoryName: string;
}

interface AggregatedDispensedDrugClinic {
  ClinicID: number;
  ClinicName: string;
  DrugID: number;
  DrugName: string;
  DrugUnit: string | null;
  CategoryID: number;
  CategoryName: string;
  UnitPrice: number;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
}

interface AggregatedDispensedDrugRegion {
  RegionID: number;
  RegionName: string;
  DrugID: number;
  DrugName: string;
  DrugUnit: string | null;
  CategoryID: number;
  CategoryName: string;
  UnitPrice: number;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
}

interface AggregatedDispensedDrugBranch {
  BranchID: number;
  BranchName: string;
  DrugID: number;
  DrugName: string;
  DrugUnit: string | null;
  CategoryID: number;
  CategoryName: string;
  UnitPrice: number;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
}

const DispensedDrugsReportsPage = () => {
  const { t, i18n } = useTranslation();
  dayjs.locale(i18n.language); // Set dayjs locale based on i18n language

  useEffect(() => {
    console.log("Current i18n language:", i18n.language);
    dayjs.locale(i18n.language);
  }, [i18n.language]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [drugs, setDrugs] = useState<Drug[]>([]);
  const [categories, setCategories] = useState<DrugCategory[]>([]);

  const [filterBranchId, setFilterBranchId] = useState<number | ''>( '');
  const [filterRegionId, setFilterRegionId] = useState<number | ''>( '');
  const [filterClinicId, setFilterClinicId] = useState<number | ''>( '');
  const [filterStartDate, setFilterStartDate] = useState<Dayjs | null>(null);
  const [filterMonth, setFilterMonth] = useState<Dayjs | null>(null);
  const [filterEndDate, setFilterEndDate] = useState<Dayjs | null>(null);
  const [filterQuarter, setFilterQuarter] = useState<number | ''>( '');
  const [filterYearForQuarter, setFilterYearForQuarter] = useState<number | ''>(new Date().getFullYear());

  const [clinicAggregatedData, setClinicAggregatedData] = useState<AggregatedDispensedDrugClinic[]>([]);
  const [regionAggregatedData, setRegionAggregatedData] = useState<AggregatedDispensedDrugRegion[]>([]);
  const [branchAggregatedData, setBranchAggregatedData] = useState<AggregatedDispensedDrugBranch[]>([]);
  const [reportDateRange, setReportDateRange] = useState<{ key: string; params?: any }>({ key: 'reportForTheSelectedPeriod' });

  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);

  useEffect(() => {
    fetchBranches();
    fetchRegions();
    fetchClinics();
    fetchDrugs();
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchAggregatedData();
  }, [filterBranchId, filterRegionId, filterClinicId, filterMonth, filterStartDate, filterEndDate, filterQuarter, filterYearForQuarter, i18n.language]); // Add i18n.language to dependencies

  const fetchBranches = async () => {
    try {
      const response = await axios.get<Branch[]>('http://localhost:8000/branches/');
      setBranches(response.data);
    } catch (error) {
      console.error('Error fetching branches:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchRegions = async () => {
    try {
      const response = await axios.get<Region[]>('http://localhost:8000/regions/');
      setRegions(response.data);
    } catch (error) {
      console.error('Error fetching regions:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchClinics = async () => {
    try {
      const response = await axios.get<Clinic[]>('http://localhost:8000/clinics/');
      setClinics(response.data);
    } catch (error) {
      console.error('Error fetching clinics:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchDrugs = async () => {
    try {
      const response = await axios.get<Drug[]>('http://localhost:8000/drugs/');
      setDrugs(response.data);
    } catch (error) {
      console.error('Error fetching drugs:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/');
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching drug categories:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchAggregatedData = async () => {
    const monthParam = filterMonth ? filterMonth.format('YYYY-MM') : '';
    const startDateParam = filterStartDate ? filterStartDate.format('YYYY-MM') : '';
    const endDateParam = filterEndDate ? filterEndDate.format('YYYY-MM') : '';
    const quarterParam = filterQuarter ? `${filterYearForQuarter}-Q${filterQuarter}` : '';

    if (monthParam) {
      setReportDateRange({ key: 'forTheMonthOf', params: { monthYear: dayjs(monthParam).format('MMMM YYYY') } });
    } else if (startDateParam && endDateParam) {
      setReportDateRange({ key: 'forThePeriod', params: { startMonthYear: dayjs(startDateParam).format('MMMM YYYY'), endMonthYear: dayjs(endDateParam).format('MMMM YYYY') } });
    } else if (quarterParam) {
      const [year, q] = quarterParam.split('-Q');
      setReportDateRange({ key: 'forQuarter', params: { quarter: q, year: year } });
    } else {
      setReportDateRange({ key: 'reportForTheSelectedPeriod' });
    }

    const params = {
      ...(filterBranchId && { branch_id: filterBranchId }),
      ...(filterRegionId && { region_id: filterRegionId }),
      ...(filterClinicId && { clinic_id: filterClinicId }),
      ...(monthParam && { month: monthParam }),
      ...(startDateParam && { start_month: startDateParam }),
      ...(endDateParam && { end_month: endDateParam }),
      ...(quarterParam && { quarter: quarterParam }),
    };

    try {
      // Fetch by Clinic
      const clinicResponse = await axios.get<AggregatedDispensedDrugClinic[]>(
        'http://localhost:8000/reports/dispensed-drugs/by-clinic',
        { params }
      );
      setClinicAggregatedData(clinicResponse.data);

      // Fetch by Region (if not filtering by specific clinic)
      if (!filterClinicId) {
        const regionResponse = await axios.get<AggregatedDispensedDrugRegion[]>(
          'http://localhost:8000/reports/dispensed-drugs/by-region',
          { params }
        );
        setRegionAggregatedData(regionResponse.data);
      } else {
        setRegionAggregatedData([]); // Clear if clinic filter is active
      }

      // Fetch by Branch (if not filtering by specific clinic or region)
      if (!filterClinicId && !filterRegionId) {
        const branchResponse = await axios.get<AggregatedDispensedDrugBranch[]>(
          'http://localhost:8000/reports/dispensed-drugs/by-branch',
          { params }
        );
        setBranchAggregatedData(branchResponse.data);
      } else {
        setBranchAggregatedData([]); // Clear if clinic or region filter is active
      }

    } catch (error) {
      console.error('Error fetching aggregated data:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const handleResetFilters = () => {
    setFilterBranchId('');
    setFilterRegionId('');
    setFilterClinicId('');
    setFilterMonth(null);
    setFilterStartDate(null);
    setFilterEndDate(null);
    setFilterQuarter('');
    setFilterYearForQuarter(new Date().getFullYear());
  };

  const getClinicName = (clinicId: number) => {
    const clinic = clinics.find(c => c.id === clinicId);
    return clinic ? clinic.name : t('unknownClinic');
  };

  const getRegionName = (regionId: number) => {
    const region = regions.find(r => r.id === regionId);
    return region ? region.name : t('unknownRegion');
  };

  const getBranchName = (branchId: number) => {
    const branch = branches.find(b => b.id === branchId);
    return branch ? branch.name : t('unknownBranch');
  };

  const getDrugName = (drugId: number, drugUnit: string | null) => {
    const drug = drugs.find(d => d.DrugID === drugId);
    return drug ? `${drug.DrugName} (${drugUnit || drug.Unit || 'N/A'})` : t('unknownDrug');
  };

  const getCategoryName = (categoryId: number) => {
    const category = categories.find(c => c.CategoryID === categoryId);
    return category ? category.CategoryName : t('unknownCategory');
  };

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const reportRef = useRef<HTMLDivElement>(null);

  const handlePrint = () => {
    const printContent = reportRef.current;
    if (printContent) {
      const printWindow = window.open('', '', 'height=600,width=800');
      if (printWindow) {
        const title = document.querySelector('h4')?.innerText || 'Dispensed Drugs Report';
        const styles = `
          <style>
            body { font-family: 'Amiri', Arial, sans-serif; direction: rtl; }
            @media print { 
              body { -webkit-print-color-adjust: exact; font-family: Amiri, Arial, sans-serif !important; } 
              .no-print { display: none; } 
            }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f2f2f2; }
            h4, h5, h6 { text-align: center; }
          </style>
          <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
        `;
        printWindow.document.write(`
          <html>
            <head>
              <title>${title}</title>
              ${styles}
            </head>
            <body dir="rtl" onload="window.print(); window.close();">
              <h4>${title}</h4>
              ${printContent.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
      }
    }
    handleClose();
  };

  const handleExportExcel = () => {
    const report = reportRef.current;
    if (report) {
        const mainTitle = document.querySelector('h4')?.innerText || 'Dispensed Drugs Report';
        const dateTitle = (report.querySelector('h6[gutterBottom]') as HTMLElement)?.innerText || '';

        const aoa: (string | number)[][] = [];
        aoa.push([mainTitle]);
        if (dateTitle) {
            aoa.push([dateTitle]);
        }
        aoa.push([]); // Spacer row

        const elements = report.querySelectorAll('h5, h6, table');
        let currentAoARow = aoa.length; // Tracks the current row index in the aoa array

        // This array will store the starting row index and number of rows for each table in the AOA
        const tableInfo: { startRow: number; numRows: number; numCols: number }[] = [];

        elements.forEach(el => {
            if ((el.tagName === 'H5' || el.tagName === 'H6')) {
                if (el.getAttribute('gutterBottom') === '') {
                    return;
                }
                aoa.push([el.textContent || '']);
                currentAoARow++;
            } else if (el.tagName === 'TABLE') {
                const table = el as HTMLTableElement;
                const rows = table.querySelectorAll('tr');
                const tableData: string[][] = [];
                rows.forEach(row => {
                    const rowData: string[] = [];
                    const cells = row.querySelectorAll('th, td');
                    cells.forEach(cell => {
                        rowData.push(cell.textContent || '');
                    });
                    tableData.push(rowData);
                });

                tableInfo.push({
                    startRow: currentAoARow,
                    numRows: tableData.length,
                    numCols: tableData[0]?.length || 0
                });

                // Add table data to AOA
                tableData.forEach(row => {
                    aoa.push(row);
                });

                currentAoARow += tableData.length;
                aoa.push([]); // Spacer after table
                currentAoARow++;
            }
        });

        const ws = XLSX.utils.aoa_to_sheet(aoa);
        const wb = XLSX.utils.book_new();

        // Apply styles based on tableInfo
        tableInfo.forEach(info => {
            const headerRowIndex = info.startRow;
            const headerCellsCount = info.numCols;

            // Apply styles to the table headers
            for (let i = 0; i < headerCellsCount; i++) {
                const cellRef = XLSX.utils.encode_cell({ r: headerRowIndex, c: i });
                if (!ws[cellRef]) { // Ensure the cell object exists
                    ws[cellRef] = { v: aoa[headerRowIndex][i] };
                }
                ws[cellRef].s = {
                    fill: { fgColor: { rgb: "FFD9D9D9" } }, // Light gray background
                    border: {
                        top: { style: "thin", color: { auto: 1 } },
                        bottom: { style: "thin", color: { auto: 1 } },
                        left: { style: "thin", color: { auto: 1 } },
                        right: { style: "thin", color: { auto: 1 } }
                    }
                };
            }

            // Apply borders to all cells in the table (including headers)
            for (let r = 0; r < info.numRows; r++) {
                for (let c = 0; c < info.numCols; c++) {
                    const cellRef = XLSX.utils.encode_cell({ r: info.startRow + r, c: c });
                    if (!ws[cellRef]) { // Ensure the cell object exists
                        ws[cellRef] = { v: aoa[info.startRow + r][c] };
                    }
                    ws[cellRef].s = {
                        border: {
                            top: { style: "thin", color: { auto: 1 } },
                            bottom: { style: "thin", color: { auto: 1 } },
                            left: { style: "thin", color: { auto: 1 } },
                            right: { style: "thin", color: { auto: 1 } }
                        }
                    };
                }
            }
        });

        XLSX.utils.book_append_sheet(wb, ws, 'Report');
        XLSX.writeFile(wb, 'DispensedDrugsReport.xlsx');
    }
    handleClose();
  };

  const handleExportPdf = async () => {
    const reportContentEl = reportRef.current;
    if (!reportContentEl) return;

    const title = document.querySelector('h4')?.innerText || 'Dispensed Drugs Report';
    
    const styles = `
      <style>
        body { font-family: 'Amiri', sans-serif; direction: rtl; font-size: 14px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; margin-bottom: 20px; font-size: 14px; }
        th, td { border: 1px solid #333; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; font-weight: bold; }
        h4, h5, h6 { text-align: center; margin: 15px 0; }
        h4 { font-size: 24px; }
        h5 { font-size: 20px; }
        h6 { font-size: 18px; }
      </style>
      <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    `;

    const htmlString = `
      <!DOCTYPE html>
      <html lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>${title}</title>
        ${styles}
      </head>
      <body>
        <h4>${title}</h4>
        ${reportContentEl.innerHTML}
      </body>
      </html>
    `;

    try {
      const response = await axios.post('http://localhost:8000/generate-pdf-report', 
        { html_content: htmlString, title: title },
        { responseType: 'blob' }
      );

      const url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/pdf' }));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${title}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Error generating PDF:', error);
      setSnackbar({ open: true, message: t('pdfExportError'), severity: 'error' });
    }
    
    handleClose();
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          {t('dispensedDrugsReports')}
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }} className="no-print">
          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByBranch')}</InputLabel>
            <Select
              value={filterBranchId}
              label={t('filterByBranch')}
              onChange={(e) => {
                setFilterBranchId(e.target.value as number);
                setFilterRegionId(''); // Reset region when branch changes
                setFilterClinicId(''); // Reset clinic when branch changes
              }}
            >
              <MenuItem value="">{t('allBranches')}</MenuItem>
              {branches.map((branch) => (
                <MenuItem key={branch.id} value={branch.id}>
                  {branch.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByRegion')}</InputLabel>
            <Select
              value={filterRegionId}
              label={t('filterByRegion')}
              onChange={(e) => {
                setFilterRegionId(e.target.value as number);
                setFilterClinicId(''); // Reset clinic when region changes
              }}
            >
              <MenuItem value="">{t('allRegions')}</MenuItem>
              {regions
                .filter(region => filterBranchId === '' || region.branch_id === filterBranchId)
                .map((region) => (
                  <MenuItem key={region.id} value={region.id}>
                    {region.name}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByClinic')}</InputLabel>
            <Select
              value={filterClinicId}
              label={t('filterByClinic')}
              onChange={(e) => setFilterClinicId(e.target.value as number)}
            >
              <MenuItem value="">{t('allClinics')}</MenuItem>
              {clinics
                .filter(clinic => filterRegionId === '' || clinic.region_id === filterRegionId)
                .map((clinic) => (
                  <MenuItem key={clinic.id} value={clinic.id}>
                    {clinic.name}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <DatePicker
            label={t('filterByMonth')}
            views={['month', 'year']}
            value={filterMonth}
            onChange={(value: Dayjs | null) => setFilterMonth(value)}
            slots={{ textField: TextField }}
            slotProps={{
              textField: {
                fullWidth: true,
                sx: { minWidth: 180 }
              }
            }}
          />

          <DatePicker
            label={t('filterByStartMonth')}
            views={['month', 'year']}
            value={filterStartDate}
            onChange={(value: Dayjs | null) => setFilterStartDate(value)}
            slots={{ textField: TextField }}
            slotProps={{
              textField: {
                fullWidth: true,
                sx: { minWidth: 180 }
              }
            }}
          />

          <DatePicker
            label={t('filterByEndMonth')}
            views={['month', 'year']}
            value={filterEndDate}
            onChange={(value: Dayjs | null) => setFilterEndDate(value)}
            slots={{ textField: TextField }}
            slotProps={{
              textField: {
                fullWidth: true,
                sx: { minWidth: 180 }
              }
            }}
          />

          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByQuarter')}</InputLabel>
            <Select
              value={filterQuarter}
              label={t('filterByQuarter')}
              onChange={(e) => setFilterQuarter(e.target.value as number)}
            >
              <MenuItem value="">{t('allQuarters')}</MenuItem>
              <MenuItem value={1}>{t('quarter1')}</MenuItem>
              <MenuItem value={2}>{t('quarter2')}</MenuItem>
              <MenuItem value={3}>{t('quarter3')}</MenuItem>
              <MenuItem value={4}>{t('quarter4')}</MenuItem>
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 120 }}>
            <TextField
              label={t('yearForQuarter')}
              type="number"
              value={filterYearForQuarter}
              onChange={(e) => setFilterYearForQuarter(parseInt(e.target.value, 10))}
            />
          </FormControl>

          <Button
            variant="outlined"
            color="secondary"
            onClick={handleResetFilters}
          >
            {t('resetFilters')}
          </Button>

          <Button
            id="export-button"
            aria-controls={open ? 'export-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
            variant="contained"
          >
            {t('export')}
          </Button>
          <Menu
            id="export-menu"
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            MenuListProps={{
              'aria-labelledby': 'export-button',
            }}
          >
            <MenuItem onClick={handlePrint}>{t('print')}</MenuItem>
            <MenuItem onClick={handleExportExcel}>{t('exportToExcel')}</MenuItem>
            <MenuItem onClick={handleExportPdf}>{t('exportToPdf')}</MenuItem>
          </Menu>
        </Box>

        <div ref={reportRef} style={{ fontFamily: 'Amiri, Arial, sans-serif' }}>
          <Typography variant="h6" gutterBottom sx={{ mt: 2, textAlign: 'center' }}>
            {String(t(reportDateRange.key, reportDateRange.params))}
          </Typography>
          {/* Display Aggregated Data */}
          {filterClinicId ? (
            // Display Clinic-level aggregation if a specific clinic is selected
            <Box>
              <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>
                {t('clinicReportFor')} {getClinicName(filterClinicId as number)}
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('drug')}</TableCell>
                      <TableCell>{t('category')}</TableCell>
                      <TableCell>{t('unitPrice')}</TableCell>
                      <TableCell>{t('totalQuantity')}</TableCell>
                      <TableCell>{t('numberOfCases')}</TableCell>
                      <TableCell>{t('totalCost')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {clinicAggregatedData.map((data, index) => (
                      <TableRow key={index}>
                        <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>
                        <TableCell>{getCategoryName(data.CategoryID)}</TableCell>
                        <TableCell>{data.UnitPrice}</TableCell>
                        <TableCell>{data.TotalQuantity}</TableCell>
                        <TableCell>{data.NumberOfCases}</TableCell>
                        <TableCell>{data.TotalCost}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          ) : filterRegionId ? (
            // Display Region-level aggregation if a specific region is selected (and no clinic)
            <Box>
              <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>
                {t('regionReportFor')} {getRegionName(filterRegionId as number)}
              </Typography>
              {/* Aggregate by Clinic within the selected Region */}
              {regions
                .filter(r => r.id === filterRegionId)
                .map(selectedRegion => (
                  <Box key={selectedRegion.id} sx={{ mb: 4 }}>
                    {clinics
                      .filter(clinic => clinic.region_id === selectedRegion.id)
                      .map(clinic => {
                        const clinicData = clinicAggregatedData.filter(d => d.ClinicID === clinic.id);
                        if (clinicData.length === 0) return null;
                        return (
                          <Box key={clinic.id} sx={{ mb: 2 }}>
                            <Typography variant="h6" sx={{ mt: 2 }}>
                              {t('clinic')}: {clinic.name}
                            </Typography>
                            <TableContainer component={Paper}>
                              <Table size="small">
                                <TableHead>
                                  <TableRow>
                                    <TableCell>{t('drug')}</TableCell>
                                    <TableCell>{t('category')}</TableCell>
                                    <TableCell>{t('unitPrice')}</TableCell>
                                    <TableCell>{t('totalQuantity')}</TableCell>
                                    <TableCell>{t('numberOfCases')}</TableCell>
                                    <TableCell>{t('totalCost')}</TableCell>
                                  </TableRow>
                                </TableHead>
                                <TableBody>
                                  {clinicData.map((data, index) => (
                                    <TableRow key={index}>
                                      <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>
                                      <TableCell>{getCategoryName(data.CategoryID)}</TableCell>
                                      <TableCell>{data.UnitPrice}</TableCell>
                                      <TableCell>{data.TotalQuantity}</TableCell>
                                      <TableCell>{data.NumberOfCases}</TableCell>
                                      <TableCell>{data.TotalCost}</TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </Box>
                        );
                      })}
                    <Typography variant="h6" sx={{ mt: 4 }}>
                      {t('totalForRegion')}: {selectedRegion.name}
                    </Typography>
                    <TableContainer component={Paper}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>{t('drug')}</TableCell>
                            <TableCell>{t('category')}</TableCell>
                            <TableCell>{t('unitPrice')}</TableCell>
                            <TableCell>{t('totalQuantity')}</TableCell>
                            <TableCell>{t('numberOfCases')}</TableCell>
                            <TableCell>{t('totalCost')}</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {regionAggregatedData.map((data, index) => (
                            <TableRow key={index}>
                              <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>
                              <TableCell>{getCategoryName(data.CategoryID)}</TableCell>
                              <TableCell>{data.UnitPrice}</TableCell>
                              <TableCell>{data.TotalQuantity}</TableCell>
                              <TableCell>{data.NumberOfCases}</TableCell>
                              <TableCell>{data.TotalCost}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>
                ))}
            </Box>
          ) : filterBranchId ? (
            // Display Branch-level aggregation if a specific branch is selected (and no clinic/region)
            <Box>
              <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>
                {t('branchReportFor')} {getBranchName(filterBranchId as number)}
              </Typography>
              {/* Aggregate by Region within the selected Branch */}
              {branches
                .filter(b => b.id === filterBranchId)
                .map(selectedBranch => (
                  <Box key={selectedBranch.id} sx={{ mb: 4 }}>
                    {regions
                      .filter(region => region.branch_id === selectedBranch.id)
                      .map(region => {
                        const regionData = regionAggregatedData.filter(d => d.RegionID === region.id);
                        if (regionData.length === 0) return null;
                        return (
                          <Box key={region.id} sx={{ mb: 2 }}>
                            <Typography variant="h6" sx={{ mt: 2 }}>
                              {t('region')}: {region.name}
                            </Typography>
                            <TableContainer component={Paper}>
                              <Table size="small">
                                <TableHead>
                                  <TableRow>
                                    <TableCell>{t('drug')}</TableCell>
                                    <TableCell>{t('category')}</TableCell>
                                    <TableCell>{t('unitPrice')}</TableCell>
                                    <TableCell>{t('totalQuantity')}</TableCell>
                                    <TableCell>{t('numberOfCases')}</TableCell>
                                    <TableCell>{t('totalCost')}</TableCell>
                                  </TableRow>
                                </TableHead>
                                <TableBody>
                                  {regionData.map((data, index) => (
                                    <TableRow key={index}>
                                      <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>
                                      <TableCell>{getCategoryName(data.CategoryID)}</TableCell>
                                      <TableCell>{data.UnitPrice}</TableCell>
                                      <TableCell>{data.TotalQuantity}</TableCell>
                                      <TableCell>{data.NumberOfCases}</TableCell>
                                      <TableCell>{data.TotalCost}</TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </Box>
                        );
                      })}
                    <Typography variant="h6" sx={{ mt: 4 }}>
                      {t('totalForBranch')}: {selectedBranch.name}
                    </Typography>
                    <TableContainer component={Paper}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>{t('drug')}</TableCell>
                            <TableCell>{t('category')}</TableCell>
                            <TableCell>{t('unitPrice')}</TableCell>
                            <TableCell>{t('totalQuantity')}</TableCell>
                            <TableCell>{t('numberOfCases')}</TableCell>
                            <TableCell>{t('totalCost')}</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {branchAggregatedData.map((data, index) => (
                            <TableRow key={index}>
                              <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>
                              <TableCell>{getCategoryName(data.CategoryID)}</TableCell>
                              <TableCell>{data.UnitPrice}</TableCell>
                              <TableCell>{data.TotalQuantity}</TableCell>
                              <TableCell>{data.NumberOfCases}</TableCell>
                              <TableCell>{data.TotalCost}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>
                ))}
            </Box>
          ) : (
            // Default view or message if no filters are applied
            <Typography variant="body1" sx={{ mt: 4 }}>
              {t('selectFiltersForReport')}
            </Typography>
          )}
        </div>

        {snackbar && (
          <Snackbar
            open={snackbar.open}
            autoHideDuration={6000}
            onClose={() => setSnackbar(null)}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          >
            <Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>
              {snackbar.message}
            </Alert>
          </Snackbar>
        )}
      </Box>
    </LocalizationProvider>
  );
};


export default DispensedDrugsReportsPage;
