{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.20", "@mui/styles": "^5.15.20", "@mui/x-data-grid": "^6.20.0", "@mui/x-date-pickers": "^6.20.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^27.5.2", "@types/jspdf": "^1.3.3", "@types/node": "^16.18.126", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/xlsx": "^0.0.35", "axios": "^1.7.2", "dayjs": "^1.11.13", "html2canvas": "^1.4.1", "i18next": "^23.0.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.0.0", "react-router-dom": "^6.24.0", "react-scripts": "5.0.1", "recharts": "^2.12.7", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}