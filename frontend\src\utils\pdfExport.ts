import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import axios from 'axios';

// Add Arabic font support
const addArabicFontSupport = (pdf: jsPDF) => {
  // This is a placeholder - you would need to add actual Arabic font data
  // For now, we'll use a fallback approach
  try {
    // Try to use built-in font that supports Arabic
    pdf.setFont('Arial', 'normal');
  } catch (error) {
    console.warn('Arabic font not available, using default font');
  }
};

// Enhanced PDF export with Arabic support and better chart rendering
export const exportToPdf = async (elementId: string, filename: string, title: string) => {
  const input = document.getElementById(elementId);
  if (!input) {
    console.error('Element not found:', elementId);
    return;
  }

  try {
    // Add styles for better PDF rendering
    const style = document.createElement('style');
    style.textContent = `
      .pdf-export-rtl {
        direction: rtl !important;
        text-align: right !important;
        font-family: 'Arial', 'Traditional Arabic', 'Amiri', sans-serif !important;
      }
      .pdf-export-rtl table {
        direction: rtl !important;
        border-collapse: collapse !important;
      }
      .pdf-export-rtl th, .pdf-export-rtl td {
        text-align: right !important;
        border: 1px solid #ddd !important;
        padding: 8px !important;
      }
      .pdf-export-rtl svg {
        background: white !important;
      }
    `;
    document.head.appendChild(style);

    // Add temporary class for RTL support
    input.classList.add('pdf-export-rtl');

    // Wait for any dynamic content to render
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Capture the element with enhanced options
    const canvas = await html2canvas(input, {
      scale: 2, // High quality
      useCORS: true,
      allowTaint: true,
      logging: false,
      backgroundColor: '#ffffff',
      removeContainer: false,
      imageTimeout: 15000,
      onclone: (clonedDoc) => {
        // Ensure all SVG elements are properly rendered
        const svgElements = clonedDoc.querySelectorAll('svg');
        svgElements.forEach(svg => {
          svg.style.background = 'white';
          svg.style.display = 'block';
        });
      }
    });

    const imgData = canvas.toDataURL('image/png', 1.0);
    const pdf = new jsPDF('p', 'mm', 'a4');
    const imgWidth = 210; // A4 width in mm
    const pageHeight = 297; // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;
    let position = 0;

    // Add Arabic font support
    addArabicFontSupport(pdf);

    // Add title with Arabic support
    pdf.setFontSize(16);
    pdf.text(title, imgWidth / 2, 15, { align: 'center' });
    position = 25; // Adjust position after title

    // Add image to PDF
    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight - position;

    // Handle multiple pages
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    // Clean up
    input.classList.remove('pdf-export-rtl');
    document.head.removeChild(style);

    // Save the PDF
    pdf.save(filename);
    
    console.log('PDF exported successfully');
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw error;
  }
};

// New function to export reports via backend (for better Arabic support)
export const exportReportToPdf = async (data: any[], title: string, filename: string) => {
  try {
    const response = await axios.post(
      'http://localhost:8000/generate-pdf-report',
      data,
      {
        params: { title },
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    // Create blob and download
    const blob = new Blob([response.data], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    console.log('PDF exported successfully via backend');
  } catch (error) {
    console.error('Error exporting PDF via backend:', error);
    throw error;
  }
};

// Export chart as image for embedding in reports
export const exportChartAsImage = async (chartElementId: string): Promise<string> => {
  const chartElement = document.getElementById(chartElementId);
  if (!chartElement) {
    throw new Error('Chart element not found');
  }

  const canvas = await html2canvas(chartElement, {
    scale: 2,
    useCORS: true,
    allowTaint: true,
    backgroundColor: '#ffffff',
    logging: false,
  });

  return canvas.toDataURL('image/png', 1.0);
};
