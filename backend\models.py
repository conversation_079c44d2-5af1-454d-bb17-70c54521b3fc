from sqlalchemy import (
    Column,
    Integer,
    String,
    Float,
    Foreign<PERSON>ey,
    Date,
    DECIMAL,
    Computed,
)
from sqlalchemy.orm import relationship
from database import Base


# 1. Branches Table
class Branch(Base):
    __tablename__ = "branches"
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)

    regions = relationship("Region", back_populates="branch")
    court_rulings = relationship("CourtRulingsDrug", back_populates="branch")


# 2. Regions Table
class Region(Base):
    __tablename__ = "regions"
    id = Column(Integer, primary_key=True)
    branch_id = Column(Integer, ForeignKey("branches.id"), nullable=False)
    name = Column(String(100), nullable=False)

    branch = relationship("Branch", back_populates="regions")
    clinics = relationship("Clinic", back_populates="region")


# 3. Clinics Table
class Clinic(Base):
    __tablename__ = "clinics"
    id = Column(Integer, primary_key=True)
    region_id = Column(Integer, ForeignKey("regions.id"), nullable=False)
    name = Column(String(100), nullable=False)

    region = relationship("Region", back_populates="clinics")
    dispensed_drugs = relationship("DispensedDrug", back_populates="clinic")
    dispensed_insulin = relationship("DispensedInsulin", back_populates="clinic")
    monthly_dispense = relationship("MonthlyDispense", back_populates="clinic")
    monthly_supplies = relationship("MonthlySupplies", back_populates="clinic")
    medical_tickets = relationship("MedicalTicket", back_populates="clinic")
    subsidized_pharmacy_tickets = relationship(
        "SubsidizedPharmacyTicket", back_populates="clinic"
    )


# 4. DrugCategories Table
class DrugCategory(Base):
    __tablename__ = "drug_categories"
    CategoryID = Column(Integer, primary_key=True)
    CategoryName = Column(String(100), nullable=False)

    drugs = relationship("Drug", back_populates="category")


# 5. Drugs Table
class Drug(Base):
    __tablename__ = "drugs"
    DrugID = Column(Integer, primary_key=True)
    CategoryID = Column(Integer, ForeignKey("drug_categories.CategoryID"), nullable=False)
    DrugName = Column(String(100), nullable=False)
    Unit = Column(String(50))

    category = relationship("DrugCategory", back_populates="drugs")
    dispensed_drugs = relationship("DispensedDrug", back_populates="drug")


# 6. DispensedDrugs Table
class DispensedDrug(Base):
    __tablename__ = "dispensed_drugs"
    DispenseID = Column(Integer, primary_key=True)
    ClinicID = Column(Integer, ForeignKey("clinics.id"), nullable=False)
    DrugID = Column(Integer, ForeignKey("drugs.DrugID"), nullable=False)
    Quantity = Column(Integer, nullable=False)
    UnitPrice = Column(DECIMAL, nullable=False)
    Cases = Column(Integer, nullable=False)
    TotalCost = Column(DECIMAL, Computed("Quantity * UnitPrice"))
    DispenseDate = Column(Date, nullable=False)

    clinic = relationship("Clinic", back_populates="dispensed_drugs")
    drug = relationship("Drug", back_populates="dispensed_drugs")


# 7. InsulinTypes Table
class InsulinType(Base):
    __tablename__ = "insulin_types"
    InsulinID = Column(Integer, primary_key=True)
    InsulinName = Column(String(100), nullable=False)
    SupportType = Column(String(50))  # "مدعّم" أو "هيئة"
    Unit = Column(String(50))
    Balance = Column(Integer)

    dispensed_insulin = relationship("DispensedInsulin", back_populates="insulin_type")


# 8. DispensedInsulin Table
class DispensedInsulin(Base):
    __tablename__ = "dispensed_insulin"
    ID = Column(Integer, primary_key=True)
    ClinicID = Column(Integer, ForeignKey("clinics.id"), nullable=False)
    InsulinID = Column(Integer, ForeignKey("insulin_types.InsulinID"), nullable=False)
    Quantity = Column(Integer, nullable=False)
    Cases = Column(Integer, nullable=False)
    UnitPrice = Column(DECIMAL, nullable=False)
    TotalCost = Column(DECIMAL, Computed("Quantity * UnitPrice"))
    Date = Column(Date, nullable=False)

    clinic = relationship("Clinic", back_populates="dispensed_insulin")
    insulin_type = relationship("InsulinType", back_populates="dispensed_insulin")


# 9. DrugGroups Table
class DrugGroup(Base):
    __tablename__ = "drug_groups"
    GroupID = Column(Integer, primary_key=True)
    GroupName = Column(String(100), nullable=False)

    group_costs = relationship("GroupCost", back_populates="group")


# 10. GroupCosts Table
class GroupCost(Base):
    __tablename__ = "group_costs"
    ID = Column(Integer, primary_key=True)
    GroupID = Column(Integer, ForeignKey("drug_groups.GroupID"), nullable=False)
    Cost = Column(DECIMAL, nullable=False)
    Date = Column(Date, nullable=False)

    group = relationship("DrugGroup", back_populates="group_costs")


# 11. MonthlyDispense Table
class MonthlyDispense(Base):
    __tablename__ = "monthly_dispense"
    ID = Column(Integer, primary_key=True)
    ClinicID = Column(Integer, ForeignKey("clinics.id"), nullable=False)
    Year = Column(Integer, nullable=False)
    Month = Column(Integer, nullable=False)
    Hيئة = Column("هيئة", DECIMAL)
    طلاب = Column("طلاب", DECIMAL)
    رضع = Column("رضع", DECIMAL)
    امرأة_معيلة = Column("امرأة_معيلة", DECIMAL)

    clinic = relationship("Clinic", back_populates="monthly_dispense")


# 12. MonthlySupplies Table
class MonthlySupplies(Base):
    __tablename__ = "monthly_supplies"
    ID = Column(Integer, primary_key=True)
    ClinicID = Column(Integer, ForeignKey("clinics.id"), nullable=False)
    Year = Column(Integer, nullable=False)
    Month = Column(Integer, nullable=False)
    مستلزمات_عامة = Column("مستلزمات_عامة", DECIMAL)
    أشعة = Column("أشعة", DECIMAL)
    معامل = Column("معامل", DECIMAL)
    مستلزمات_عيادات = Column("مستلزمات_عيادات", DECIMAL)

    clinic = relationship("Clinic", back_populates="monthly_supplies")


# 13. MedicalTickets Table
class MedicalTicket(Base):
    __tablename__ = "medical_tickets"
    TicketID = Column(Integer, primary_key=True)
    ClinicID = Column(Integer, ForeignKey("clinics.id"), nullable=False)
    Year = Column(Integer, nullable=False)
    Month = Column(Integer, nullable=False)
    Week = Column(Integer, nullable=False)  # 1-4
    فئة = Column("فئة", String(50), nullable=False)  # "هيئة" – "طلاب" – "رضع" – "امرأة معيلة"
    تذاكر_56ب = Column("تذاكر_56ب", Integer)
    تذاكر_56ج = Column("تذاكر_56ج", Integer)

    clinic = relationship("Clinic", back_populates="medical_tickets")


# 14. SubsidizedPharmacyTickets Table
class SubsidizedPharmacyTicket(Base):
    __tablename__ = "subsidized_pharmacy_tickets"
    ID = Column(Integer, primary_key=True)
    ClinicID = Column(Integer, ForeignKey("clinics.id"), nullable=False)
    Year = Column(Integer, nullable=False)
    Month = Column(Integer, nullable=False)
    Week = Column(Integer, nullable=False)
    هيئة = Column("هيئة", Integer)
    طلاب = Column("طلاب", Integer)
    مدعّم = Column("مدعّم", Integer)

    clinic = relationship("Clinic", back_populates="subsidized_pharmacy_tickets")


# 15. CourtRulingsDrugs Table
class CourtRulingsDrug(Base):
    __tablename__ = "court_rulings_drugs"
    ID = Column(Integer, primary_key=True)
    branch_id = Column(Integer, ForeignKey("branches.id"), nullable=False)
    جهة = Column("جهة", String(100))
    اسم_المريض = Column("اسم_المريض", String(100))
    التشخيص = Column("التشخيص", String(200))
    صنف_العلاج = Column("صنف_العلاج", String(100))
    الوحدة = Column("الوحدة", String(50))
    سعر_الوحدة = Column("سعر_الوحدة", DECIMAL)
    الجرعة_شهرياً = Column("الجرعة_شهرياً", Integer)
    التكلفة_شهرياً = Column("التكلفة_شهرياً", DECIMAL, Computed("سعر_الوحدة * الجرعة_شهرياً"))
    تاريخ_الحكم = Column("تاريخ_الحكم", Date)
    تاريخ_أول_صرف = Column("تاريخ_أول_صرف", Date)
    شهر_الصرف = Column("شهر_الصرف", Date)

    branch = relationship("Branch", back_populates="court_rulings")