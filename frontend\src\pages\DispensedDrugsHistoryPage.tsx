import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Snackbar,
  Alert,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { Edit, Delete } from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';

interface DispensedDrug {
  DispenseID: number;
  ClinicID: number;
  DrugID: number;
  Quantity: number;
  UnitPrice: number;
  Cases: number;
  TotalCost: number;
  DispenseDate: string;
  ClinicName?: string;
  DrugName?: string;
  DrugUnit?: string | null;
  CategoryName?: string;
  CategoryID?: number; // Added this line
}

interface Clinic {
  id: number;
  name: string;
}

interface Region {
  id: number;
  name: string;
}

interface Drug {
  DrugID: number;
  DrugName: string;
  Unit: string | null;
  CategoryID: number;
}

interface DrugCategory {
  CategoryID: number;
  CategoryName: string;
}

const DispensedDrugsHistoryPage = () => {
  const { t } = useTranslation();
  const [dispensedDrugs, setDispensedDrugs] = useState<DispensedDrug[]>([]);
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [regions, setRegions] = useState<Region[]>([]); // Added regions state
  const [drugs, setDrugs] = useState<Drug[]>([]);
  const [categories, setCategories] = useState<DrugCategory[]>([]);

  const [filterClinicId, setFilterClinicId] = useState<number | ''>('');
  const [filterRegionId, setFilterRegionId] = useState<number | ''>(''); // Added filterRegionId state
  const [filterCategoryId, setFilterCategoryId] = useState<number | ''>('');
  const [filterDrugId, setFilterDrugId] = useState<number | ''>('');
  const [filterMonth, setFilterMonth] = useState<Dayjs | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedDispense, setSelectedDispense] = useState<DispensedDrug | null>(null);
  const [deleteItemId, setDeleteItemId] = useState<number | null>(null);

  const [editQuantity, setEditQuantity] = useState<string>('');
  const [editUnitPrice, setEditUnitPrice] = useState<string>('');
  const [editCases, setEditCases] = useState<string>('');
  const [editDispenseDate, setEditDispenseDate] = useState<Dayjs | null>(null);

  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);

  useEffect(() => {
    fetchClinics();
    fetchDrugs();
    fetchCategories();
    fetchRegions(); // Fetch regions on component mount
  }, []);

  useEffect(() => {
    fetchDispensedDrugs();
  }, [filterClinicId, filterRegionId, filterCategoryId, filterDrugId, filterMonth, searchTerm]); // Added filterRegionId to dependencies

  const fetchDispensedDrugs = async () => {
    try {
      const params: any = {};
      if (filterClinicId) params.clinic_id = filterClinicId;
      if (filterRegionId) params.region_id = filterRegionId; // Add region_id to params
      if (filterDrugId) params.drug_id = filterDrugId;
      if (filterCategoryId) params.category_id = filterCategoryId;
      if (filterMonth) params.month = filterMonth.format('YYYY-MM');
      if (searchTerm) params.search = searchTerm;

      const response = await axios.get<DispensedDrug[]>('http://localhost:8000/dispensed-drugs/', { params });
      setDispensedDrugs(response.data);
    } catch (error) {
      console.error('Error fetching dispensed drugs:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchClinics = async () => {
    try {
      const response = await axios.get<Clinic[]>('http://localhost:8000/clinics/');
      setClinics(response.data);
    } catch (error) {
      console.error('Error fetching clinics:', error);
    }
  };

  const fetchRegions = async () => { // Added fetchRegions function
    try {
      const response = await axios.get<Region[]>('http://localhost:8000/regions/');
      setRegions(response.data);
    } catch (error) {
      console.error('Error fetching regions:', error);
    }
  };

  const fetchDrugs = async () => {
    try {
      const response = await axios.get<Drug[]>('http://localhost:8000/drugs/');
      setDrugs(response.data);
    }
    catch (error) {
      console.error('Error fetching drugs:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/');
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching drug categories:', error);
    }
  };

  const handleEditClick = (dispense: DispensedDrug) => {
    setSelectedDispense(dispense);
    setEditQuantity(dispense.Quantity.toString());
    setEditUnitPrice(dispense.UnitPrice.toString());
    setEditCases(dispense.Cases.toString());
    setEditDispenseDate(dayjs(dispense.DispenseDate));
    setOpenEditDialog(true);
  };

  const handleUpdateDispense = async () => {
    if (!selectedDispense) return;

    try {
      const updatedData = {
        ClinicID: selectedDispense.ClinicID,
        DrugID: selectedDispense.DrugID,
        Quantity: parseFloat(editQuantity),
        UnitPrice: parseFloat(editUnitPrice),
        Cases: parseInt(editCases),
        DispenseDate: editDispenseDate?.format('YYYY-MM-DD') || selectedDispense.DispenseDate,
      };
      await axios.put(`http://localhost:8000/dispensed-drugs/${selectedDispense.DispenseID}`, updatedData);
      setSnackbar({ open: true, message: t('updateSuccess'), severity: 'success' });
      setOpenEditDialog(false);
      fetchDispensedDrugs();
    } catch (error) {
      console.error('Error updating dispensed drug:', error);
      setSnackbar({ open: true, message: t('updateError'), severity: 'error' });
    }
  };

  const handleDeleteClick = (dispenseId: number) => {
    setDeleteItemId(dispenseId);
    setOpenDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deleteItemId) return;
    try {
      await axios.delete(`http://localhost:8000/dispensed-drugs/${deleteItemId}`);
      setSnackbar({ open: true, message: t('deleteSuccess'), severity: 'success' });
      setOpenDeleteDialog(false);
      fetchDispensedDrugs();
    } catch (error) {
      console.error('Error deleting dispensed drug:', error);
      setSnackbar({ open: true, message: t('deleteError'), severity: 'error' });
    }
  };

  const handleResetFilters = () => {
    setFilterClinicId('');
    setFilterRegionId(''); // Reset filterRegionId
    setFilterCategoryId('');
    setFilterDrugId('');
    setFilterMonth(null);
    setSearchTerm('');
  };

  const getClinicName = (clinicId: number) => {
    const clinic = clinics.find(c => c.id === clinicId);
    return clinic ? clinic.name : t('unknownClinic');
  };

  const getDrugName = (drugId: number) => {
    const drug = drugs.find(d => d.DrugID === drugId);
    return drug ? `${drug.DrugName} (${drug.Unit})` : t('unknownDrug');
  };

  const getCategoryName = (categoryId: number | undefined) => {
    if (categoryId === undefined) {
      return t('unknownCategory');
    }
    const category = categories.find(c => c.CategoryID === categoryId);
    return category ? category.CategoryName : t('unknownCategory');
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          {t('dispensedDrugsHistory')}
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByRegion')}</InputLabel> {/* Added filterByRegion label */}
            <Select
              value={filterRegionId}
              label={t('filterByRegion')}
              onChange={(e) => setFilterRegionId(e.target.value as number)}
            >
              <MenuItem value="">{t('allRegions')}</MenuItem> {/* Added allRegions option */}
              {regions.map((region) => (
                <MenuItem key={region.id} value={region.id}>
                  {region.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByClinic')}</InputLabel>
            <Select
              value={filterClinicId}
              label={t('filterByClinic')}
              onChange={(e) => setFilterClinicId(e.target.value as number)}
            >
              <MenuItem value="">{t('allClinics')}</MenuItem>
              {clinics.map((clinic) => (
                <MenuItem key={clinic.id} value={clinic.id}>
                  {clinic.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByCategory')}</InputLabel>
            <Select
              value={filterCategoryId}
              label={t('filterByCategory')}
              onChange={(e) => setFilterCategoryId(e.target.value as number)}
            >
              <MenuItem value="">{t('allCategories')}</MenuItem>
              {categories.map((category) => (
                <MenuItem key={category.CategoryID} value={category.CategoryID}>
                  {category.CategoryName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByDrug')}</InputLabel>
            <Select
              value={filterDrugId}
              label={t('filterByDrug')}
              onChange={(e) => setFilterDrugId(e.target.value as number)}
            >
              <MenuItem value="">{t('allDrugs')}</MenuItem>
              {drugs.map((drug) => (
                <MenuItem key={drug.DrugID} value={drug.DrugID}>
                  {drug.DrugName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <DatePicker
            label={t('filterByMonth')}
            views={['year', 'month']}
            value={filterMonth}
            onChange={(value: unknown) => setFilterMonth(value as Dayjs | null)}
            slots={{ textField: TextField }}
            slotProps={{ textField: { fullWidth: true } }}
          />

          <TextField
            label={t('search')}
            variant="outlined"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ flexGrow: 1 }}
          />
          <Button
            variant="outlined"
            color="secondary"
            onClick={handleResetFilters}
          >
            {t('resetFilters')}
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('clinic')}</TableCell>
                <TableCell>{t('drug')}</TableCell>
                <TableCell>{t('category')}</TableCell> {/* Added this */}
                <TableCell>{t('quantity')}</TableCell>
                <TableCell>{t('unitPrice')}</TableCell>
                <TableCell>{t('cases')}</TableCell>
                <TableCell>{t('totalCost')}</TableCell>
                <TableCell>{t('dispenseDate')}</TableCell>
                <TableCell align="right">{t('actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {dispensedDrugs.map((dispense) => (
                <TableRow key={dispense.DispenseID}>
                  <TableCell>{getClinicName(dispense.ClinicID)}</TableCell>
                  <TableCell>{getDrugName(dispense.DrugID)}</TableCell>
                  <TableCell>
                    {dispense.CategoryID !== undefined
                      ? getCategoryName(dispense.CategoryID)
                      : t('unknownCategory')}
                  </TableCell> {/* Handled undefined CategoryID directly */}
                  <TableCell>{dispense.Quantity}</TableCell>
                  <TableCell>{dispense.UnitPrice}</TableCell>
                  <TableCell>{dispense.Cases}</TableCell>
                  <TableCell>{dispense.TotalCost}</TableCell> {/* Corrected to show TotalCost */}
                  <TableCell>{dayjs(dispense.DispenseDate).format('YYYY-MM')}</TableCell> {/* Corrected to show DispenseDate as YYYY-MM */}
                  <TableCell align="right">
                    <IconButton color="primary" onClick={() => handleEditClick(dispense)}>
                      <Edit />
                    </IconButton>
                    <IconButton color="secondary" onClick={() => handleDeleteClick(dispense.DispenseID)}>
                      <Delete />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Edit Dialog */}
        <Dialog open={openEditDialog} onClose={() => setOpenEditDialog(false)}>
          <DialogTitle>{t('editDispenseRecord')}</DialogTitle>
          <DialogContent>
            <TextField
              label={t('quantity')}
              type="number"
              fullWidth
              value={editQuantity}
              onChange={(e) => setEditQuantity(e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              label={t('unitPrice')}
              type="number"
              fullWidth
              value={editUnitPrice}
              onChange={(e) => setEditUnitPrice(e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              label={t('cases')}
              type="number"
              fullWidth
              value={editCases}
              onChange={(e) => setEditCases(e.target.value)}
              sx={{ mb: 2 }}
            />
            <DatePicker
              label={t('dispenseDate')}
              value={editDispenseDate}
              onChange={(value: unknown) => setEditDispenseDate(value as Dayjs | null)}
              slots={{ textField: TextField }}
              slotProps={{ textField: { fullWidth: true, sx: { mb: 2 } } }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenEditDialog(false)}>{t('cancel')}</Button>
            <Button onClick={handleUpdateDispense}>{t('save')}</Button>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={openDeleteDialog}
          onClose={() => setOpenDeleteDialog(false)}
        >
          <DialogTitle>{t('confirmDeleteTitle')}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {t('confirmDeleteRecordMessage')}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDeleteDialog(false)}>{t('cancel')}</Button>
            <Button onClick={handleDeleteConfirm} color="error">
              {t('delete')}
            </Button>
          </DialogActions>
        </Dialog>

        {snackbar && (
          <Snackbar
            open={snackbar.open}
            autoHideDuration={6000}
            onClose={() => setSnackbar(null)}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          >
            <Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>
              {snackbar.message}
            </Alert>
          </Snackbar>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default DispensedDrugsHistoryPage;