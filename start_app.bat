@echo off

echo Setting up and Starting Backend Server...
start "Backend" cmd /k "cd backend && call venv\Scripts\activate.bat && pip install -r requirements.txt && uvicorn main:app --reload --port 8000"

echo Setting up and Starting Frontend Development Server...
start "Frontend" cmd /k "cd frontend && npm install && npm start"

echo Application startup commands sent. Check the new command windows for server status.