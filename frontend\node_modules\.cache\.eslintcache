[{"E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\index.tsx": "1", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\reportWebVitals.ts": "2", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\i18n.ts": "3", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\App.tsx": "4", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\ClinicsPage.tsx": "5", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\BranchesPage.tsx": "6", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\RegionsPage.tsx": "7", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DispenseDrugsPage.tsx": "8", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\InsulinPage.tsx": "9", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DrugCategoriesPage.tsx": "10", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DrugGroupsPage.tsx": "11", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\BranchSelection.tsx": "12", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DispensedDrugsReportsPage.tsx": "13", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DispensedDrugsHistoryPage.tsx": "14", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\ComparisonReportsPage.tsx": "15", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DrugsPage.tsx": "16", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\SpecificDrugComparisonPage.tsx": "17", "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\components\\Clinics.tsx": "18"}, {"size": 739, "mtime": 1751400125000, "results": "19", "hashOfConfig": "20"}, {"size": 425, "mtime": 1751244817000, "results": "21", "hashOfConfig": "20"}, {"size": 678, "mtime": 1751400112000, "results": "22", "hashOfConfig": "20"}, {"size": 9291, "mtime": 1752338661000, "results": "23", "hashOfConfig": "20"}, {"size": 174, "mtime": 1751571507000, "results": "24", "hashOfConfig": "20"}, {"size": 7068, "mtime": 1752090084000, "results": "25", "hashOfConfig": "20"}, {"size": 8925, "mtime": 1752356957000, "results": "26", "hashOfConfig": "20"}, {"size": 22976, "mtime": 1752095941000, "results": "27", "hashOfConfig": "20"}, {"size": 9041, "mtime": 1752090168000, "results": "28", "hashOfConfig": "20"}, {"size": 7407, "mtime": 1751704540000, "results": "29", "hashOfConfig": "20"}, {"size": 6615, "mtime": 1752090092000, "results": "30", "hashOfConfig": "20"}, {"size": 5581, "mtime": 1751408196000, "results": "31", "hashOfConfig": "20"}, {"size": 39708, "mtime": 1754224550788, "results": "32", "hashOfConfig": "20"}, {"size": 16135, "mtime": 1752095899000, "results": "33", "hashOfConfig": "20"}, {"size": 23437, "mtime": 1754224848104, "results": "34", "hashOfConfig": "20"}, {"size": 16440, "mtime": 1751705971000, "results": "35", "hashOfConfig": "20"}, {"size": 22360, "mtime": 1754224739265, "results": "36", "hashOfConfig": "20"}, {"size": 9682, "mtime": 1752090072000, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1l8q49l", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\index.tsx", [], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\reportWebVitals.ts", [], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\i18n.ts", [], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\App.tsx", [], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\ClinicsPage.tsx", [], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\BranchesPage.tsx", ["92", "93"], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\RegionsPage.tsx", [], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DispenseDrugsPage.tsx", ["94", "95", "96", "97"], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\InsulinPage.tsx", [], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DrugCategoriesPage.tsx", ["98"], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DrugGroupsPage.tsx", [], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\BranchSelection.tsx", [], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DispensedDrugsReportsPage.tsx", ["99", "100", "101", "102", "103"], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DispensedDrugsHistoryPage.tsx", ["104"], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\ComparisonReportsPage.tsx", ["105", "106", "107"], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\DrugsPage.tsx", ["108"], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\pages\\SpecificDrugComparisonPage.tsx", [], [], "E:\\Python\\cmder\\drug_dispensing_app\\frontend\\src\\components\\Clinics.tsx", ["109", "110", "111"], [], {"ruleId": "112", "severity": 1, "message": "113", "line": 45, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 45, "endColumn": 30}, {"ruleId": "112", "severity": 1, "message": "116", "line": 45, "column": 32, "nodeType": "114", "messageId": "115", "endLine": 45, "endColumn": 55}, {"ruleId": "117", "severity": 1, "message": "118", "line": 98, "column": 6, "nodeType": "119", "endLine": 98, "endColumn": 8, "suggestions": "120"}, {"ruleId": "117", "severity": 1, "message": "121", "line": 102, "column": 6, "nodeType": "119", "endLine": 102, "endColumn": 32, "suggestions": "122"}, {"ruleId": "112", "severity": 1, "message": "123", "line": 341, "column": 9, "nodeType": "114", "messageId": "115", "endLine": 341, "endColumn": 27}, {"ruleId": "112", "severity": 1, "message": "124", "line": 346, "column": 9, "nodeType": "114", "messageId": "115", "endLine": 346, "endColumn": 22}, {"ruleId": "117", "severity": 1, "message": "125", "line": 47, "column": 6, "nodeType": "119", "endLine": 47, "endColumn": 8, "suggestions": "126"}, {"ruleId": "112", "severity": 1, "message": "127", "line": 30, "column": 8, "nodeType": "114", "messageId": "115", "endLine": 30, "endColumn": 12}, {"ruleId": "112", "severity": 1, "message": "128", "line": 31, "column": 8, "nodeType": "114", "messageId": "115", "endLine": 31, "endColumn": 13}, {"ruleId": "112", "severity": 1, "message": "129", "line": 32, "column": 8, "nodeType": "114", "messageId": "115", "endLine": 32, "endColumn": 19}, {"ruleId": "117", "severity": 1, "message": "130", "line": 143, "column": 6, "nodeType": "119", "endLine": 143, "endColumn": 8, "suggestions": "131"}, {"ruleId": "117", "severity": 1, "message": "132", "line": 147, "column": 6, "nodeType": "119", "endLine": 147, "endColumn": 151, "suggestions": "133"}, {"ruleId": "117", "severity": 1, "message": "134", "line": 109, "column": 6, "nodeType": "119", "endLine": 109, "endColumn": 95, "suggestions": "135"}, {"ruleId": "112", "severity": 1, "message": "136", "line": 26, "column": 92, "nodeType": "114", "messageId": "115", "endLine": 26, "endColumn": 101}, {"ruleId": "112", "severity": 1, "message": "137", "line": 26, "column": 103, "nodeType": "114", "messageId": "115", "endLine": 26, "endColumn": 107}, {"ruleId": "112", "severity": 1, "message": "138", "line": 36, "column": 11, "nodeType": "114", "messageId": "115", "endLine": 36, "endColumn": 15}, {"ruleId": "117", "severity": 1, "message": "139", "line": 75, "column": 6, "nodeType": "119", "endLine": 75, "endColumn": 36, "suggestions": "140"}, {"ruleId": "112", "severity": 1, "message": "141", "line": 35, "column": 12, "nodeType": "114", "messageId": "115", "endLine": 35, "endColumn": 32}, {"ruleId": "112", "severity": 1, "message": "142", "line": 37, "column": 12, "nodeType": "114", "messageId": "115", "endLine": 37, "endColumn": 34}, {"ruleId": "117", "severity": 1, "message": "143", "line": 58, "column": 8, "nodeType": "119", "endLine": 58, "endColumn": 10, "suggestions": "144"}, "@typescript-eslint/no-unused-vars", "'branchNameHelperText' is assigned a value but never used.", "Identifier", "unusedVar", "'setBranchNameHelperText' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchCategories' and 'fetchClinics'. Either include them or remove the dependency array.", "ArrayExpression", ["145"], "React Hook useEffect has a missing dependency: 'fetchDrugs'. Either include it or remove the dependency array.", ["146"], "'getDrugNameAndUnit' is assigned a value but never used.", "'getClinicName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", ["147"], "'i18n' is defined but never used.", "'jsPDF' is defined but never used.", "'html2canvas' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchBranches', 'fetchCategories', 'fetchClinics', 'fetchDrugs', and 'fetchRegions'. Either include them or remove the dependency array.", ["148"], "React Hook useEffect has a missing dependency: 'fetchAggregatedData'. Either include it or remove the dependency array.", ["149"], "React Hook useEffect has a missing dependency: 'fetchDispensedDrugs'. Either include it or remove the dependency array.", ["150"], "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'Drug' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchCategories' and 'fetchDrugs'. Either include them or remove the dependency array.", ["151"], "'clinicNameHelperText' is assigned a value but never used.", "'regionSelectHelperText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", ["152"], {"desc": "153", "fix": "154"}, {"desc": "155", "fix": "156"}, {"desc": "157", "fix": "158"}, {"desc": "159", "fix": "160"}, {"desc": "161", "fix": "162"}, {"desc": "163", "fix": "164"}, {"desc": "165", "fix": "166"}, {"desc": "167", "fix": "168"}, "Update the dependencies array to be: [fetchCategories, fetchClinics]", {"range": "169", "text": "170"}, "Update the dependencies array to be: [fetchDrugs, selectedFilterCategoryId]", {"range": "171", "text": "172"}, "Update the dependencies array to be: [fetchCategories]", {"range": "173", "text": "174"}, "Update the dependencies array to be: [fetchBranches, fetchCategories, fetchClinics, fetchDrugs, fetchRegions]", {"range": "175", "text": "176"}, "Update the dependencies array to be: [filterBranchId, filterRegionId, filterClinicId, filterMonth, filterStartDate, filterEndDate, filterQuarter, filterYearForQuarter, i18n.language, fetchAggregatedData]", {"range": "177", "text": "178"}, "Update the dependencies array to be: [filterClinicId, filterRegionId, filterCategoryId, filterDrugId, filterMonth, searchTerm, fetchDispensedDrugs]", {"range": "179", "text": "180"}, "Update the dependencies array to be: [fetchCategories, fetchDrugs, filterCategoryId, searchTerm]", {"range": "181", "text": "182"}, "Update the dependencies array to be: [t]", {"range": "183", "text": "184"}, [2817, 2819], "[fetchCategories, fetchClinics]", [2890, 2916], "[fetchDrugs, selectedFilterCategoryId]", [1359, 1361], "[fetchCategories]", [4055, 4057], "[fetchBranches, fetchCategories, fetchClinics, fetchDrugs, fetchRegions]", [4113, 4258], "[filterBranchId, filterRegionId, filterClinicId, filterMonth, filterStartDate, filterEndDate, filterQuarter, filterYearForQuarter, i18n.language, fetchAggregatedData]", [3105, 3194], "[filterClinicId, filterRegionId, filterCategoryId, filterDrugId, filterMonth, searchTerm, fetchDispensedDrugs]", [2199, 2229], "[fetchCategories, fetchDrugs, filterCategoryId, searchTerm]", [2194, 2196], "[t]"]