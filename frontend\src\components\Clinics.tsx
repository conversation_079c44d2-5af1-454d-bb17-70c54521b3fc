import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    Box, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper,
    Typography, TextField, Select, MenuItem, FormControl, InputLabel, CircularProgress,
    IconButton, Snackbar, Alert, Grid, FormHelperText
} from '@mui/material';
import { Edit, Delete } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const API_URL = 'http://127.0.0.1:8000';

interface Region {
    id: number;
    name: string;
}

interface Clinic {
    id: number;
    name: string;
    region_id: number;
}

const Clinics = () => {
    const [clinics, setClinics] = useState<Clinic[]>([]);
    const [regions, setRegions] = useState<Region[]>([]);
    const [loading, setLoading] = useState(true);

    const [editingId, setEditingId] = useState<number | null>(null);
    const [clinicName, setClinicName] = useState('');
    const [selectedRegion, setSelectedRegion] = useState<number | ''>('');

    const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);
    const [clinicNameError, setClinicNameError] = useState<boolean>(false);
    const [clinicNameHelperText, setClinicNameHelperText] = useState('');
    const [regionSelectError, setRegionSelectError] = useState<boolean>(false);
    const [regionSelectHelperText, setRegionSelectHelperText] = useState('');

    const { t } = useTranslation();

    useEffect(() => {
        const fetchClinicsAndRegions = async () => {
            setLoading(true);
            try {
                const [clinicsRes, regionsRes] = await Promise.all([
                    axios.get<Clinic[]>(`${API_URL}/clinics`),
                    axios.get<Region[]>(`${API_URL}/regions`)
                ]);
                setClinics(clinicsRes.data);
                setRegions(regionsRes.data);
            } catch (error) {
                console.error('Error fetching data:', error);
                setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
            }
            setLoading(false);
        };
        fetchClinicsAndRegions();
    }, []);

    const handleEdit = (clinic: Clinic) => {
        setEditingId(clinic.id);
        setClinicName(clinic.name);
        setSelectedRegion(clinic.region_id);
        setClinicNameError(false);
        setClinicNameHelperText('');
        setRegionSelectError(false);
        setRegionSelectHelperText('');
    };

    const handleCancelEdit = () => {
        setEditingId(null);
        setClinicName('');
        setSelectedRegion('');
        setClinicNameError(false);
        setClinicNameHelperText('');
        setRegionSelectError(false);
        setRegionSelectHelperText('');
    };

    const handleSubmit = async () => {
        // Reset errors
        setClinicNameError(false);
        setRegionSelectError(false);

        if (!clinicName.trim()) {
            setClinicNameError(true);
            return;
        }
        if (!selectedRegion) {
            setRegionSelectError(true);
            return;
        }
        const clinicData = { name: clinicName, region_id: selectedRegion };

        try {
            if (editingId) {
                const response = await axios.put<Clinic>(`${API_URL}/clinics/${editingId}`, clinicData);
                setClinics(clinics.map(c => (c.id === editingId ? response.data : c)));
                setSnackbar({ open: true, message: t('clinicUpdatedSuccess'), severity: 'success' });
            } else {
                const response = await axios.post<Clinic>(`${API_URL}/clinics`, clinicData);
                setClinics([...clinics, response.data]);
                setSnackbar({ open: true, message: t('clinicAddedSuccess'), severity: 'success' });
            }
            handleCancelEdit();
        } catch (error) {
            console.error('Error submitting form:', error);
            setSnackbar({ open: true, message: t('clinicOperationError'), severity: 'error' });
        }
    };

    const handleDeleteClinic = async (id: number) => {
        if (window.confirm(t('confirmDeleteClinic'))) {
            try {
                await axios.delete(`${API_URL}/clinics/${id}`);
                setClinics(clinics.filter(c => c.id !== id));
                setSnackbar({ open: true, message: t('clinicDeletedSuccess'), severity: 'success' });
            } catch (error) {
                console.error('Error deleting clinic:', error);
                setSnackbar({ open: true, message: t('clinicDeleteError'), severity: 'error' });
            }
        }
    };

    const getRegionName = (regionId: number) => {
        const region = regions.find(r => r.id === regionId);
        return region ? region.name : t('unknown');
    };

    if (loading) {
        return <CircularProgress />;
    }

    return (
        <Box sx={{ width: '100%', p: 3 }}>
            <Typography variant="h4" gutterBottom>
                {t('clinicsManagement')}
            </Typography>

            <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                    {editingId ? t('editClinic') : t('addNewClinic')}
                </Typography>
                <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={5}>
                        <TextField
                            fullWidth
                            label={t('clinicName')}
                            value={clinicName}
                            onChange={(e) => setClinicName(e.target.value)}
                            error={clinicNameError}
                            helperText={clinicNameError ? t('clinicNameRequired') : ''}
                        />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                        <FormControl fullWidth error={regionSelectError}>
                            <InputLabel>{t('region')}</InputLabel>
                            <Select
                                value={selectedRegion}
                                label={t('region')}
                                onChange={(e) => setSelectedRegion(e.target.value as number)}
                            >
                                {regions.map((region) => (
                                    <MenuItem key={region.id} value={region.id}>
                                        {region.name}
                                    </MenuItem>
                                ))}
                            </Select>
                            {regionSelectError ? <FormHelperText>{t('regionRequired')}</FormHelperText> : null}
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={3} component="div">
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={handleSubmit}
                            fullWidth
                        >
                            {editingId ? t('saveChanges') : t('add')}
                        </Button>
                        {editingId && (
                            <Button
                                variant="outlined"
                                color="secondary"
                                onClick={handleCancelEdit}
                                fullWidth
                                sx={{ mt: 1 }}
                            >
                                {t('cancel')}
                            </Button>
                        )}
                    </Grid>
                </Grid>
            </Paper>

            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>{t('clinicName')}</TableCell>
                            <TableCell>{t('affiliatedRegion')}</TableCell>
                            <TableCell align="center">{t('actions')}</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {clinics.map((clinic) => (
                            <TableRow key={clinic.id}>
                                <TableCell>{clinic.name}</TableCell>
                                <TableCell>{getRegionName(clinic.region_id)}</TableCell>
                                <TableCell align="center">
                                    <IconButton onClick={() => handleEdit(clinic)} color="primary">
                                        <Edit />
                                    </IconButton>
                                    <IconButton onClick={() => handleDeleteClinic(clinic.id)} color="error">
                                        <Delete />
                                    </IconButton>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            {snackbar && (
                <Snackbar
                    open={snackbar.open}
                    autoHideDuration={6000}
                    onClose={() => setSnackbar(null)}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
                >
                    <Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>
                        {snackbar.message}
                    </Alert>
                </Snackbar>
            )}
        </Box>
    );
};

export default Clinics;
