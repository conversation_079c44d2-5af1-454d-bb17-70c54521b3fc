import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
    Box, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, 
    Typography, Dialog, DialogActions, DialogContent, DialogTitle, TextField 
} from '@mui/material';

const API_URL = 'http://127.0.0.1:8000';

interface DrugCategory {
    id: number;
    name: string;
}

const DrugCategories = () => {
    const [categories, setCategories] = useState<DrugCategory[]>([]);
    const [open, setOpen] = useState(false);
    const [newCategoryName, setNewCategoryName] = useState('');

    useEffect(() => {
        fetchCategories();
    }, []);

    const fetchCategories = async () => {
        try {
            const response = await axios.get<DrugCategory[]>(`${API_URL}/drug-categories`);
            setCategories(response.data);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    const handleAddCategory = async () => {
        if (!newCategoryName.trim()) return;
        try {
            const response = await axios.post<DrugCategory>(`${API_URL}/drug-categories`, { name: newCategoryName });
            setCategories([...categories, response.data]);
            setNewCategoryName('');
            setOpen(false);
        } catch (error) {
            console.error('Error adding category:', error);
        }
    };

    return (
        <Box sx={{ width: '100%' }}>
            <Typography variant="h6" gutterBottom component="div">
                إدارة تصنيفات الأدوية
            </Typography>
            <Button variant="contained" onClick={() => setOpen(true)} sx={{ mb: 2 }}>
                إضافة تصنيف جديد
            </Button>
            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>اسم التصنيف</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {categories.map((category) => (
                            <TableRow key={category.id}>
                                <TableCell>{category.name}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            <Dialog open={open} onClose={() => setOpen(false)}>
                <DialogTitle>إضافة تصنيف جديد</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="اسم التصنيف"
                        type="text"
                        fullWidth
                        variant="standard"
                        value={newCategoryName}
                        onChange={(e) => setNewCategoryName(e.target.value)}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpen(false)}>إلغاء</Button>
                    <Button onClick={handleAddCategory}>إضافة</Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default DrugCategories;
